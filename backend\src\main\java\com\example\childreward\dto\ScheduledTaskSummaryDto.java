package com.example.childreward.dto;

import com.example.childreward.entity.TaskRequirementType;
import com.example.childreward.entity.TaskType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

/**
 * 计划任务摘要DTO - 用于列表查询，不包含详情字段
 * 优化性能，减少数据传输量
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduledTaskSummaryDto {
    
    private Long id;
    
    /**
     * 任务/惩罚标题
     */
    private String title;
    
    /**
     * 任务奖励的积分，或惩罚要扣除的积分
     */
    private Integer points;
    
    /**
     * 任务类型：奖励(REWARD) 或 惩罚审批(PENALTY_APPROVAL)
     */
    private TaskType type;
    
    /**
     * 任务执行类型：必做(REQUIRED) 或 选做(OPTIONAL)
     */
    private TaskRequirementType taskRequirementType;
    
    /**
     * CRON表达式，用于定义任务的执行周期
     */
    private String cronExpression;
    
    /**
     * 任务的预期完成分钟数
     */
    private Integer expectedMinutes;
    
    /**
     * 任务实例的截止时间点
     */
    private LocalTime dueTime;
    
    /**
     * 任务实例的生成时间点
     */
    private LocalTime executionTime;
    
    /**
     * 创建该模板的用户ID（通常是家长）
     */
    private Long createdByUserId;
    
    /**
     * 模板是否激活
     */
    private boolean active;
    
    /**
     * 是否直接进入审核状态
     */
    private Integer directToReview;
    
    /**
     * 便利方法：获取directToReview布尔值
     */
    public Boolean getDirectToReviewBoolean() {
        return directToReview != null && directToReview == 1;
    }
}