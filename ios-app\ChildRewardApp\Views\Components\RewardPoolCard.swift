import SwiftUI

// MARK: - 奖励池卡片组件
struct RewardPoolCard: View {
    let pool: RewardPool
    let userPoints: Int
    let onDraw: () -> Void
    
    private var canDraw: Bool {
        return userPoints >= pool.costPoints && pool.isAvailable
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // 奖励池图标和名称
            VStack(spacing: 8) {
                Image(systemName: "gift.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.orange)
                
                Text(pool.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            
            // 奖励池信息
            VStack(spacing: 8) {
                HStack {
                    Text("消费积分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(pool.costPoints)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
                
                HStack {
                    Text("奖品数量")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(pool.itemCount)个")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }
            }
            
            // 抽奖按钮
            Button(action: onDraw) {
                HStack {
                    Image(systemName: "dice.fill")
                        .font(.subheadline)
                    
                    Text(canDraw ? "立即抽奖" : "积分不足")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(canDraw ? Color.orange : Color.gray)
                .cornerRadius(12)
            }
            .disabled(!canDraw)
            .buttonStyle(PlainButtonStyle())
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .scaleEffect(canDraw ? 1.0 : 0.95)
        .opacity(canDraw ? 1.0 : 0.7)
        .animation(.easeInOut(duration: 0.2), value: canDraw)
    }
}

// MARK: - 预览
struct RewardPoolCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            RewardPoolCard(
                pool: RewardPool(
                    id: 1,
                    name: "幸运奖励池",
                    costPoints: 50,
                    isEnabled: true,
                    poolType: .reward,
                    createdTime: nil,
                    rewardItems: []
                ),
                userPoints: 100,
                onDraw: {}
            )
            
            RewardPoolCard(
                pool: RewardPool(
                    id: 2,
                    name: "超级大奖池",
                    costPoints: 200,
                    isEnabled: true,
                    poolType: .reward,
                    createdTime: nil,
                    rewardItems: []
                ),
                userPoints: 50,
                onDraw: {}
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
