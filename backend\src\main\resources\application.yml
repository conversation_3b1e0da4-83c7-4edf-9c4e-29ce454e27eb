# 应用名称和端口
spring:
  application:
    name: child-reward-system
  # 数据库连接配置
  datasource:
    url: *************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
  # JPA配置
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        # 性能优化配置
        jdbc:
          batch_size: 20
          order_inserts: true
          order_updates: true
  # SQL初始化配置（暂时禁用，等表创建后再手动执行）
  sql:
    init:
      mode: never
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=300s

server:
  port: 18080

# 日志配置
logging:
  level:
    org:
      springframework: INFO
      hibernate: INFO
    com:
      example:
        childreward: INFO