import Foundation
import Combine

// 应用设置管理器
class SettingsManager: ObservableObject {
    static let shared = SettingsManager()
    
    // 息屏设置
    @Published var screenDimmingDelay: TimeInterval = 5.0 // 默认5秒
    @Published var isScreenDimmingEnabled: Bool = true
    
    // 其他设置
    @Published var soundEnabled: Bool = true
    @Published var vibrationEnabled: Bool = true
    @Published var autoRefreshEnabled: Bool = true
    @Published var autoRefreshInterval: TimeInterval = 30.0
    @Published var infoPanelEnabled: Bool = true
    @Published var rewardTabEnabled: Bool = true
    
    private let userDefaults = UserDefaults.standard
    
    // UserDefaults键
    private enum Keys {
        static let screenDimmingDelay = "screen_dimming_delay"
        static let isScreenDimmingEnabled = "is_screen_dimming_enabled"
        static let soundEnabled = "sound_enabled"
        static let vibrationEnabled = "vibration_enabled"
        static let autoRefreshEnabled = "auto_refresh_enabled"
        static let autoRefreshInterval = "auto_refresh_interval"
        static let infoPanelEnabled = "info_panel_enabled"
        static let rewardTabEnabled = "reward_tab_enabled"
    }
    
    private init() {
        loadSettings()
    }
    
    // 加载设置
    private func loadSettings() {
        screenDimmingDelay = userDefaults.object(forKey: Keys.screenDimmingDelay) as? TimeInterval ?? 5.0
        isScreenDimmingEnabled = userDefaults.object(forKey: Keys.isScreenDimmingEnabled) as? Bool ?? true
        soundEnabled = userDefaults.object(forKey: Keys.soundEnabled) as? Bool ?? true
        vibrationEnabled = userDefaults.object(forKey: Keys.vibrationEnabled) as? Bool ?? true
        autoRefreshEnabled = userDefaults.object(forKey: Keys.autoRefreshEnabled) as? Bool ?? true
        autoRefreshInterval = userDefaults.object(forKey: Keys.autoRefreshInterval) as? TimeInterval ?? 30.0
        infoPanelEnabled = userDefaults.object(forKey: Keys.infoPanelEnabled) as? Bool ?? true
        rewardTabEnabled = userDefaults.object(forKey: Keys.rewardTabEnabled) as? Bool ?? true

        print("📱 设置已加载: 息屏延迟=\(screenDimmingDelay)秒, 启用=\(isScreenDimmingEnabled), 信息面板=\(infoPanelEnabled), 奖励页=\(rewardTabEnabled)")
    }
    
    // 保存设置
    private func saveSettings() {
        userDefaults.set(screenDimmingDelay, forKey: Keys.screenDimmingDelay)
        userDefaults.set(isScreenDimmingEnabled, forKey: Keys.isScreenDimmingEnabled)
        userDefaults.set(soundEnabled, forKey: Keys.soundEnabled)
        userDefaults.set(vibrationEnabled, forKey: Keys.vibrationEnabled)
        userDefaults.set(autoRefreshEnabled, forKey: Keys.autoRefreshEnabled)
        userDefaults.set(autoRefreshInterval, forKey: Keys.autoRefreshInterval)
        userDefaults.set(infoPanelEnabled, forKey: Keys.infoPanelEnabled)
        userDefaults.set(rewardTabEnabled, forKey: Keys.rewardTabEnabled)

        print("💾 设置已保存: 息屏延迟=\(screenDimmingDelay)秒, 启用=\(isScreenDimmingEnabled), 信息面板=\(infoPanelEnabled), 奖励页=\(rewardTabEnabled)")
    }
    
    // 更新息屏延迟时间
    func updateScreenDimmingDelay(_ delay: TimeInterval) {
        screenDimmingDelay = delay
        saveSettings()
    }
    
    // 切换息屏功能
    func toggleScreenDimming(_ enabled: Bool) {
        isScreenDimmingEnabled = enabled
        saveSettings()
    }
    
    // 更新声音设置
    func updateSoundEnabled(_ enabled: Bool) {
        soundEnabled = enabled
        saveSettings()
    }
    
    // 更新震动设置
    func updateVibrationEnabled(_ enabled: Bool) {
        vibrationEnabled = enabled
        saveSettings()
    }
    
    // 更新自动刷新设置
    func updateAutoRefreshEnabled(_ enabled: Bool) {
        autoRefreshEnabled = enabled
        saveSettings()
    }
    
    // 更新自动刷新间隔
    func updateAutoRefreshInterval(_ interval: TimeInterval) {
        autoRefreshInterval = interval
        saveSettings()
    }

    // 更新信息面板设置
    func updateInfoPanelEnabled(_ enabled: Bool) {
        infoPanelEnabled = enabled
        saveSettings()
    }

    // 更新奖励页tab设置
    func updateRewardTabEnabled(_ enabled: Bool) {
        rewardTabEnabled = enabled
        saveSettings()
    }
    
    // 重置所有设置
    func resetToDefaults() {
        screenDimmingDelay = 5.0
        isScreenDimmingEnabled = true
        soundEnabled = true
        vibrationEnabled = true
        autoRefreshEnabled = true
        autoRefreshInterval = 30.0
        infoPanelEnabled = true
        rewardTabEnabled = true
        saveSettings()

        print("🔄 设置已重置为默认值")
    }
}
