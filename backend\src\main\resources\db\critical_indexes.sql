-- 关键性能索引脚本
-- 基于实际数据库表结构，只包含最关键的索引

-- 1. 积分记录表 (point_record) - 最关键的索引
CREATE INDEX idx_point_record_time ON point_record(record_time);
CREATE INDEX idx_point_record_task_id ON point_record(related_task_id);

-- 2. 任务表 (tasks) - 最关键的索引  
CREATE INDEX idx_tasks_scheduled_date ON tasks(scheduled_date);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_status_scheduled ON tasks(status, scheduled_date);

-- 3. 兑换商品表 (exchange_items) - 最关键的索引
CREATE INDEX idx_exchange_items_active ON exchange_items(is_active);
CREATE INDEX idx_exchange_items_category_active ON exchange_items(category, is_active);

-- 4. 兑换记录表 (exchange_records) - 最关键的索引
CREATE INDEX idx_exchange_records_time ON exchange_records(exchange_time);
CREATE INDEX idx_exchange_records_status ON exchange_records(status);

-- 5. 奖励物品表 (reward_items) - 最关键的索引
CREATE INDEX idx_reward_items_pool_id ON reward_items(pool_id);

-- 6. 计划任务表 (scheduled_task) - 最关键的索引
CREATE INDEX idx_scheduled_task_active ON scheduled_task(active);

-- 显示完成信息
SELECT 'Critical performance indexes created successfully!' as message;
