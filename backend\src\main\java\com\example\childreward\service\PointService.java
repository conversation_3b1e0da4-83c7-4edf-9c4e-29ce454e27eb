package com.example.childreward.service;

import com.example.childreward.entity.PointRecord;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface PointService {
    
    /**
     * 获取当前总积分
     */
    Integer getTotalPoints();
    
    /**
     * 添加积分
     */
    PointRecord addPoints(Integer points, PointRecord.ChangeType changeType, String description, Long relatedId);
    
    /**
     * 扣减积分
     */
    PointRecord deductPoints(Integer points, PointRecord.ChangeType changeType, String description, Long relatedId);
    
    /**
     * 获取积分记录列表
     */
    List<PointRecord> getPointRecords(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取今日积分变化
     */
    Integer getTodayPointsChange();
    
    /**
     * 获取指定日期的积分变化
     */
    Integer getPointsChangeByDate(LocalDate date);
    
    /**
     * 获取所有积分记录（用于调试）
     */
    List<PointRecord> getAllRecords();
    
    /**
     * 保存积分记录（用于修复日期等）
     */
    PointRecord saveRecord(PointRecord record);
} 