# 儿童奖励系统 - 前后端一体化Docker镜像
# 基于OpenJDK 21，包含Nginx + Spring Boot

FROM docker.m.daocloud.io/openjdk:21-jdk-slim

# 安装必要软件
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        nginx \
        curl \
        dos2unix \
        fontconfig \
        libfreetype6 \
        supervisor && \
    rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制后端JAR文件
COPY backend/target/childreward-0.0.1-SNAPSHOT.jar /app/backend.jar

# 复制前端构建文件
COPY frontend/dist /var/www/html

# 复制配置文件
COPY nginx-all-in-one.conf /etc/nginx/nginx.conf
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY start-all.sh /app/start-all.sh

# 处理脚本权限和换行符
RUN dos2unix /app/start-all.sh && \
    chmod +x /app/start-all.sh && \
    mkdir -p /app/logs /var/log/supervisor

# 环境变量
ENV SPRING_PROFILES_ACTIVE=prod
ENV MYSQL_HOST=*************
ENV MYSQL_PORT=3307
ENV MYSQL_DATABASE=crs
ENV MYSQL_USERNAME=root
ENV MYSQL_PASSWORD=123456
ENV JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC"

# 暴露端口
EXPOSE 80 18080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/actuator/health || exit 1

# 启动命令
CMD ["/app/start-all.sh"]