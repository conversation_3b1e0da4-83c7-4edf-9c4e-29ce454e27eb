import SwiftUI

// MARK: - 奖励页面视图
struct RewardsView: View {
    @StateObject private var rewardViewModel = RewardViewModel()

    @State private var selectedTab = 0 // 0: 奖励池, 1: 兑换商店
    @State private var selectedCategory: String? = nil
    @State private var showingDrawAlert = false
    @State private var showingExchangeAlert = false
    @State private var selectedPool: RewardPool? = nil
    @State private var selectedItem: ExchangeItem? = nil

    var body: some View {
        VStack(spacing: 0) {
            // 积分卡片
            pointsCard

            // 标签切换器
            tabSelector

            // 内容区域
            TabView(selection: $selectedTab) {
                // 奖励池页面
                rewardPoolsView
                    .tag(0)

                // 兑换商店页面
                exchangeStoreView
                    .tag(1)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        }
        .background(Color(.systemGroupedBackground))
        .alert("抽奖确认", isPresented: $showingDrawAlert) {
            drawAlert
        }
        .alert("兑换确认", isPresented: $showingExchangeAlert) {
            exchangeAlert
        }
        .alert("操作结果", isPresented: .constant(rewardViewModel.successMessage != nil)) {
            Button("确定") {
                rewardViewModel.clearMessages()
            }
        } message: {
            if let message = rewardViewModel.successMessage {
                Text(message)
            }
        }
        .alert("错误", isPresented: .constant(rewardViewModel.errorMessage != nil)) {
            Button("确定") {
                rewardViewModel.clearMessages()
            }
        } message: {
            if let message = rewardViewModel.errorMessage {
                Text(message)
            }
        }
    }
    
    // MARK: - 子视图

    private var pointsCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("我的积分")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    Text("\(rewardViewModel.totalPoints)")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 8) {
                    Text("可用积分")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("\(rewardViewModel.totalPoints)")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .padding(.horizontal, 32)
    }

    private var tabSelector: some View {
        HStack(spacing: 0) {
            TabButton(title: "🎁 奖励池", isSelected: selectedTab == 0) {
                selectedTab = 0
            }

            TabButton(title: "🛒 兑换商店", isSelected: selectedTab == 1) {
                selectedTab = 1
            }
        }
        .padding(.horizontal, 32)
        .padding(.top, 16)
    }

    private var rewardPoolsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                if rewardViewModel.isLoading {
                    ProgressView("加载中...")
                        .frame(maxWidth: .infinity, minHeight: 200)
                } else if rewardViewModel.availableRewardPools.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "gift.fill")
                            .font(.system(size: 48))
                            .foregroundColor(.gray)
                        Text("暂无可用奖励池")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                } else {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(rewardViewModel.availableRewardPools, id: \.id) { pool in
                            RewardPoolCard(
                                pool: pool,
                                userPoints: rewardViewModel.totalPoints,
                                onDraw: {
                                    selectedPool = pool
                                    showingDrawAlert = true
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 32)
                }

                Spacer(minLength: 32)
            }
            .padding(.top, 20)
        }
    }

    private var exchangeStoreView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 分类筛选器
                if !rewardViewModel.availableCategories.isEmpty {
                    categoryFilter
                }

                if rewardViewModel.isLoading {
                    ProgressView("加载中...")
                        .frame(maxWidth: .infinity, minHeight: 200)
                } else if rewardViewModel.exchangeItems.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "cart.fill")
                            .font(.system(size: 48))
                            .foregroundColor(.gray)
                        Text("暂无可兑换商品")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                } else {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(filteredExchangeItems, id: \.id) { item in
                            ExchangeItemCard(
                                item: item,
                                userPoints: rewardViewModel.totalPoints,
                                onExchange: {
                                    selectedItem = item
                                    showingExchangeAlert = true
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 32)
                }

                Spacer(minLength: 32)
            }
            .padding(.top, 20)
        }
    }

    private var categoryFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                CategoryButton(
                    title: "全部",
                    isSelected: selectedCategory == nil
                ) {
                    selectedCategory = nil
                }

                ForEach(rewardViewModel.availableCategories, id: \.self) { category in
                    CategoryButton(
                        title: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, 32)
        }
    }

    // 筛选后的兑换商品
    private var filteredExchangeItems: [ExchangeItem] {
        return rewardViewModel.getExchangeItems(for: selectedCategory)
    }

    // 抽奖确认弹窗
    private var drawAlert: some View {
        Group {
            if let pool = selectedPool {
                Button("确认抽奖") {
                    rewardViewModel.drawReward(from: pool)
                    selectedPool = nil
                }

                Button("取消", role: .cancel) {
                    selectedPool = nil
                }
            }
        }
    }

    // 兑换确认弹窗
    private var exchangeAlert: some View {
        Group {
            if let item = selectedItem {
                Button("确认兑换") {
                    rewardViewModel.exchangeItem(item)
                    selectedItem = nil
                }

                Button("取消", role: .cancel) {
                    selectedItem = nil
                }
            }
        }
    }
}

// MARK: - 辅助组件

// 标签按钮
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(isSelected ? Color.blue : Color.clear)
                .cornerRadius(25)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 分类按钮
struct CategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? Color.blue : Color.clear)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
