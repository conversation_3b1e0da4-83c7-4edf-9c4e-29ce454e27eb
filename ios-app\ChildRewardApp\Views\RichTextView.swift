import SwiftUI
import Foundation

// MARK: - 富文本显示组件
struct RichTextView: View {
    let content: String
    @State private var attributedString: AttributedString = AttributedString("")
    @State private var images: [UIImage] = []
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 显示处理后的文本
            Text(attributedString)
                .font(.body)
                .lineSpacing(4)
            
            // 显示提取的图片
            if !images.isEmpty {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    ForEach(Array(images.enumerated()), id: \.offset) { index, image in
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 200)
                            .cornerRadius(8)
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                }
            }
        }
        .onAppear {
            processRichText()
        }
        .onChange(of: content) { _ in
            processRichText()
        }
    }
    
    // MARK: - 富文本处理
    private func processRichText() {
        // 提取base64图片
        let (cleanText, extractedImages) = extractBase64Images(from: content)
        
        // 处理文本格式
        attributedString = processTextFormatting(cleanText)
        
        // 更新图片数组
        images = extractedImages
    }
    
    // 提取base64图片
    private func extractBase64Images(from text: String) -> (String, [UIImage]) {
        var cleanText = text
        var extractedImages: [UIImage] = []
        
        // 匹配base64图片的正则表达式
        let base64Pattern = #"data:image\/[^;]+;base64,([A-Za-z0-9+/=]+)"#
        
        do {
            let regex = try NSRegularExpression(pattern: base64Pattern, options: [])
            let matches = regex.matches(in: text, options: [], range: NSRange(location: 0, length: text.count))
            
            // 从后往前处理，避免索引变化
            for match in matches.reversed() {
                if let range = Range(match.range, in: text),
                   let base64Range = Range(match.range(at: 1), in: text) {
                    
                    let base64String = String(text[base64Range])
                    
                    // 尝试解码base64图片
                    if let imageData = Data(base64Encoded: base64String),
                       let image = UIImage(data: imageData) {
                        extractedImages.insert(image, at: 0) // 保持顺序
                    }
                    
                    // 从文本中移除base64图片标签
                    cleanText = cleanText.replacingCharacters(in: range, with: "[图片]")
                }
            }
        } catch {
            print("❌ RichTextView: 正则表达式错误 - \(error)")
        }
        
        return (cleanText, extractedImages)
    }
    
    // 处理文本格式
    private func processTextFormatting(_ text: String) -> AttributedString {
        var attributedString = AttributedString(text)
        
        // 处理换行
        let formattedText = text.replacingOccurrences(of: "\\n", with: "\n")
        attributedString = AttributedString(formattedText)
        
        // 处理粗体文本 **text**
        attributedString = processBoldText(attributedString)
        
        // 处理斜体文本 *text*
        attributedString = processItalicText(attributedString)
        
        // 处理标题 # text
        attributedString = processHeaders(attributedString)
        
        return attributedString
    }
    
    // 处理粗体文本
    private func processBoldText(_ attributedString: AttributedString) -> AttributedString {
        var result = attributedString
        let pattern = #"\*\*(.*?)\*\*"#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [])
            let string = String(attributedString.characters)
            let matches = regex.matches(in: string, options: [], range: NSRange(location: 0, length: string.count))
            
            for match in matches.reversed() {
                if let range = Range(match.range, in: string),
                   let contentRange = Range(match.range(at: 1), in: string) {
                    
                    let content = String(string[contentRange])
                    var boldText = AttributedString(content)
                    boldText.font = .body.bold()
                    
                    if let attributedRange = Range(range, in: result) {
                        result.replaceSubrange(attributedRange, with: boldText)
                    }
                }
            }
        } catch {
            print("❌ RichTextView: 粗体处理错误 - \(error)")
        }
        
        return result
    }
    
    // 处理斜体文本
    private func processItalicText(_ attributedString: AttributedString) -> AttributedString {
        var result = attributedString
        let pattern = #"\*(.*?)\*"#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [])
            let string = String(attributedString.characters)
            let matches = regex.matches(in: string, options: [], range: NSRange(location: 0, length: string.count))
            
            for match in matches.reversed() {
                if let range = Range(match.range, in: string),
                   let contentRange = Range(match.range(at: 1), in: string) {
                    
                    let content = String(string[contentRange])
                    var italicText = AttributedString(content)
                    italicText.font = .body.italic()
                    
                    if let attributedRange = Range(range, in: result) {
                        result.replaceSubrange(attributedRange, with: italicText)
                    }
                }
            }
        } catch {
            print("❌ RichTextView: 斜体处理错误 - \(error)")
        }
        
        return result
    }
    
    // 处理标题
    private func processHeaders(_ attributedString: AttributedString) -> AttributedString {
        var result = attributedString
        let pattern = #"^#{1,3}\s+(.*?)$"#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [.anchorsMatchLines])
            let string = String(attributedString.characters)
            let matches = regex.matches(in: string, options: [], range: NSRange(location: 0, length: string.count))
            
            for match in matches.reversed() {
                if let range = Range(match.range, in: string),
                   let contentRange = Range(match.range(at: 1), in: string) {
                    
                    let content = String(string[contentRange])
                    var headerText = AttributedString(content)
                    headerText.font = .headline.bold()
                    headerText.foregroundColor = .primary
                    
                    if let attributedRange = Range(range, in: result) {
                        result.replaceSubrange(attributedRange, with: headerText)
                    }
                }
            }
        } catch {
            print("❌ RichTextView: 标题处理错误 - \(error)")
        }
        
        return result
    }
}

// MARK: - 预览
struct RichTextView_Previews: PreviewProvider {
    static var previews: some View {
        RichTextView(content: """
        # 任务详情
        
        这是一个**重要**的任务，需要*仔细*完成。
        
        ## 要求：
        1. 完成所有步骤
        2. 检查结果
        3. 提交报告
        
        [图片]
        """)
        .padding()
    }
}
