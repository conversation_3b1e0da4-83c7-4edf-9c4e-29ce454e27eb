package com.example.childreward.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 兑换记录实体类
 */
@Entity
@Table(name = "exchange_records")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "exchange_item_id", nullable = false)
    private Long exchangeItemId;
    
    @Column(name = "item_name", nullable = false, length = 100)
    private String itemName; // 冗余字段，防止商品被删除后记录丢失信息
    
    @Column(name = "points_used", nullable = false)
    private Integer pointsUsed;
    
    @Column(name = "exchange_time", nullable = false)
    private LocalDateTime exchangeTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private ExchangeStatus status = ExchangeStatus.UNUSED;
    
    @Column(name = "used_time")
    private LocalDateTime usedTime;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @PrePersist
    protected void onCreate() {
        this.exchangeTime = LocalDateTime.now();
    }
    
    /**
     * 兑换状态枚举
     */
    public enum ExchangeStatus {
        UNUSED("待使用"),
        USED("已使用"),
        EXPIRED("已过期");
        
        private final String description;
        
        ExchangeStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 标记为已使用
     */
    public void markAsUsed() {
        this.status = ExchangeStatus.USED;
        this.usedTime = LocalDateTime.now();
    }
    
    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
}
