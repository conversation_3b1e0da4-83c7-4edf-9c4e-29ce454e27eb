import React from 'react';
import styled from 'styled-components';
import { NavLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { useLocation } from 'react-router-dom';

const ParentNavigation = () => {
  const navItems = [
    {
      path: '/parent',
      icon: '📊',
      label: '概览',
      exact: true
    },
    {
      path: '/parent/tasks',
      icon: '📋',
      label: '任务'
    },
    {
      path: '/parent/approvals',
      icon: '✅',
      label: '审批'
    },
    {
      path: '/parent/rewards',
      icon: '🎁',
      label: '奖品'
    },
    {
      path: '/parent/exchange',
      icon: '🛒',
      label: '兑换'
    },
    {
      path: '/parent/points',
      icon: '⭐',
      label: '积分'
    },
    {
      path: '/parent/settings',
      icon: '⚙️',
      label: '设置'
    }
  ];
  
  const scheduledTaskNav = { icon: '⏰', label: '计划', path: '/parent/scheduled-tasks' };

  const finalNavItems = [
    ...navItems.slice(0, 2),
    scheduledTaskNav,
    ...navItems.slice(2)
  ];

  const location = useLocation();

  return (
    <NavContainer>
      <Logo>
        <LogoIcon>⭐</LogoIcon>
        <LogoText>家长控制台</LogoText>
      </Logo>
      
      <NavItems>
        {finalNavItems.map((item) => (
          <NavItem
            key={item.path}
            to={item.path}
            end={item.exact}
          >
            {({ isActive }) => (
              <>
                <NavIcon>{item.icon}</NavIcon>
                <NavLabel>{item.label}</NavLabel>
                {isActive && <ActiveIndicator layoutId="parentNavIndicator" />}
              </>
            )}
          </NavItem>
        ))}
      </NavItems>
      
      <UserSection>
        <UserIcon>👨‍👩‍👧‍👦</UserIcon>
        <UserRole>家长账户</UserRole>
      </UserSection>
    </NavContainer>
  );
};

const NavContainer = styled.nav`
  background: white;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
  box-shadow: 0 2px 10px ${parentTheme.shadowColor};
  height: 100%;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  padding: 0 1.5rem 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
`;

const LogoIcon = styled.span`
  font-size: 1.5rem;
  color: ${parentTheme.primaryColor};
  margin-right: 0.5rem;
`;

const LogoText = styled.h1`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${parentTheme.primaryColor};
  margin: 0;
`;

const NavItems = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const NavItem = styled(NavLink)`
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
  
  &.active {
    color: ${parentTheme.primaryColor};
    background-color: rgba(63, 81, 181, 0.05);
    font-weight: 500;
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }
`;

const NavIcon = styled.span`
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 1.5rem;
  text-align: center;
`;

const NavLabel = styled.span`
  font-size: 0.9rem;
`;

const ActiveIndicator = styled(motion.div)`
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: ${parentTheme.primaryColor};
  border-radius: 0 4px 4px 0;
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid #eee;
  margin-top: 1rem;
`;

const UserIcon = styled.span`
  font-size: 1.5rem;
  margin-right: 0.75rem;
`;

const UserRole = styled.span`
  font-size: 0.9rem;
  color: #666;
`;

export default ParentNavigation; 