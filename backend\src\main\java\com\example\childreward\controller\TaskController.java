package com.example.childreward.controller;

import com.example.childreward.dto.TaskSummaryDto;
import com.example.childreward.entity.Task;
import com.example.childreward.mapper.TaskMapper;
import com.example.childreward.service.TaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@RestController
@CrossOrigin(origins = "*") // 允许所有来源跨域请求
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final TaskService taskService;
    private final TaskMapper taskMapper;

    @PostMapping
    public ResponseEntity<Task> createTask(@RequestBody Task task) {
        return new ResponseEntity<>(taskService.createTask(task), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Task> updateTask(@PathVariable Long id, @RequestBody Task task) {
        return ResponseEntity.ok(taskService.updateTask(id, task));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTask(@PathVariable Long id) {
        taskService.deleteTask(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Task> getTaskById(@PathVariable Long id) {
        return ResponseEntity.ok(taskService.getTaskById(id));
    }

    @GetMapping("/date/{date}")
    public ResponseEntity<List<TaskSummaryDto>> getTasksByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        List<Task> tasks = taskService.getTasksByDate(date);
        return ResponseEntity.ok(taskMapper.toSummaryDtoList(tasks));
    }

    @GetMapping("/today")
    public ResponseEntity<List<TaskSummaryDto>> getTodayTasks() {
        String requestId = java.util.UUID.randomUUID().toString().substring(0, 8);
        LocalDateTime startTime = LocalDateTime.now();

        log.info("🚀 [请求{}] 收到获取今日任务请求", requestId);
        log.info("⏰ [请求{}] 请求时间: {}", requestId, startTime);

        try {
            LocalDate today = LocalDate.now();
            log.info("📅 [请求{}] 查询日期: {}", requestId, today);

            List<Task> tasks = taskService.getTasksByDate(today);
            log.info("📋 [请求{}] 从数据库获取到 {} 个任务", requestId, tasks.size());

            // 打印任务详情
            for (int i = 0; i < tasks.size(); i++) {
                Task task = tasks.get(i);
                log.info("📋 [请求{}] 任务{}: ID={}, 标题={}, 状态={}",
                    requestId, i+1, task.getId(), task.getTitle(), task.getStatus());
            }

            List<TaskSummaryDto> taskSummaries = taskMapper.toSummaryDtoList(tasks);
            log.info("🔄 [请求{}] 转换为DTO，数量: {}", requestId, taskSummaries.size());

            LocalDateTime endTime = LocalDateTime.now();
            long duration = java.time.Duration.between(startTime, endTime).toMillis();
            log.info("✅ [请求{}] 请求处理完成，耗时: {}ms", requestId, duration);

            return ResponseEntity.ok(taskSummaries);

        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long duration = java.time.Duration.between(startTime, endTime).toMillis();
            log.error("❌ [请求{}] 获取今日任务失败，耗时: {}ms, 错误: {}", requestId, duration, e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping("/range")
    public ResponseEntity<List<TaskSummaryDto>> getTasksByDateRange(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String status) {
        List<Task> tasks = taskService.getTasksByDateRangeAndStatus(startDate, endDate, status);
        return ResponseEntity.ok(taskMapper.toSummaryDtoList(tasks));
    }

    @GetMapping("/all")
    public ResponseEntity<List<TaskSummaryDto>> getAllTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        // 限制最大页面大小以防止性能问题
        if (size > 100) {
            size = 100;
        }
        List<Task> tasks = taskService.getAllTasks(page, size);
        return ResponseEntity.ok(taskMapper.toSummaryDtoList(tasks));
    }

    @PostMapping("/{id}/start")
    public ResponseEntity<Task> startTask(@PathVariable Long id) {
        return ResponseEntity.ok(taskService.startTask(id));
    }

    @PostMapping("/{id}/complete")
    public ResponseEntity<Task> completeTask(@PathVariable Long id) {
        return ResponseEntity.ok(taskService.completeTask(id));
    }

    @PostMapping("/{id}/approve")
    public ResponseEntity<Task> approveTask(@PathVariable Long id, @RequestParam Integer actualPoints) {
        return ResponseEntity.ok(taskService.approveTask(id, actualPoints));
    }

    @PostMapping("/{id}/reject")
    public ResponseEntity<Task> rejectTask(@PathVariable Long id, @RequestParam(required = false) String reason) {
        return ResponseEntity.ok(taskService.rejectTask(id, reason));
    }

    @GetMapping("/pending")
    public ResponseEntity<List<TaskSummaryDto>> getPendingTasks(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<Task> tasks;
        if (startDate != null || endDate != null) {
            tasks = taskService.getPendingTasksByDateRange(startDate, endDate);
        } else {
            tasks = taskService.getPendingTasks();
        }
        return ResponseEntity.ok(taskMapper.toSummaryDtoList(tasks));
    }

    @GetMapping("/overdue")
    public ResponseEntity<List<TaskSummaryDto>> getOverdueTasks() {
        List<Task> tasks = taskService.findOverdueTasks();
        return ResponseEntity.ok(taskMapper.toSummaryDtoList(tasks));
    }

    @GetMapping("/past")
    public ResponseEntity<List<TaskSummaryDto>> getPastTasks() {
        List<Task> tasks = taskService.getPastTasks();
        return ResponseEntity.ok(taskMapper.toSummaryDtoList(tasks));
    }

    // 批准惩罚性任务（扣分）
    @PostMapping("/{id}/approve-penalty")
    public ResponseEntity<Task> approvePenalty(@PathVariable Long id) {
        return ResponseEntity.ok(taskService.approvePenalty(id));
    }

    // 拒绝/忽略惩罚性任务
    @PostMapping("/{id}/reject-penalty")
    public ResponseEntity<Task> rejectPenalty(@PathVariable Long id) {
        return ResponseEntity.ok(taskService.rejectPenalty(id));
    }
} 