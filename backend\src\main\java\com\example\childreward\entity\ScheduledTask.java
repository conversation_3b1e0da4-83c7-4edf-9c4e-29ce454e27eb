package com.example.childreward.entity;

import com.example.childreward.util.TaskTypeConverter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalTime;

/**
 * 计划任务（任务模板）实体类
 * 用于定义可重复生成的任务或惩罚审批
 */
@Entity
@Data
public class ScheduledTask {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务/惩罚标题
     * 例如："完成一套数学试卷"或"晚归审核"
     */
    @Column(nullable = false)
    private String title;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 任务奖励的积分，或惩罚要扣除的积分
     * 约定为正数，业务逻辑根据TaskType判断是加分还是扣分
     */
    @Column(nullable = false)
    private Integer points;

    /**
     * 任务类型：奖励(REWARD) 或 惩罚审批(PENALTY_APPROVAL)
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TaskType type;

    /**
     * 任务执行类型：必做(REQUIRED) 或 选做(OPTIONAL)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "task_requirement_type", nullable = false)
    private TaskRequirementType taskRequirementType = TaskRequirementType.REQUIRED;

    /**
     * CRON表达式，用于定义任务的执行周期
     * 例如："0 0 20 * * MON-FRI" 表示周一至周五的晚上8点
     */
    @Column(nullable = false)
    private String cronExpression;

    /**
     * 任务的预期完成分钟数
     */
    private Integer expectedMinutes;

    /**
     * 任务实例的截止时间点
     * 如果不设置，将默认为当天的午夜
     */
    private LocalTime dueTime;

    /**
     * 任务实例的生成时间点
     * 注意：CRON表达式已包含时间，此字段可作为冗余或用于特定逻辑
     */
    private LocalTime executionTime;

    /**
     * 创建该模板的用户ID（通常是家长）
     */
    private Long createdByUserId;

    /**
     * 模板是否激活，只有激活的模板才会由调度器处理
     */
    private boolean active = true;

    /**
     * 是否直接进入审核状态
     * 0: 生成的任务进入NOT_STARTED状态，小孩可以自己操作
     * 1: 生成的任务直接进入PENDING状态，需要家长审批
     */
    @Column(name = "direct_to_review", nullable = false)
    private Integer directToReview = 0;



    // 便利方法：获取directToReview布尔值
    public Boolean getDirectToReviewBoolean() {
        return TaskTypeConverter.integerToBoolean(this.directToReview);
    }

    // 便利方法：设置directToReview布尔值
    public void setDirectToReviewBoolean(Boolean value) {
        this.directToReview = TaskTypeConverter.booleanToInteger(value);
    }

    // JSON反序列化时处理布尔值到整数的转换
    @JsonSetter("directToReview")
    public void setDirectToReviewFromJson(Object value) {
        if (value instanceof Boolean) {
            this.directToReview = ((Boolean) value) ? 1 : 0;
        } else if (value instanceof Integer) {
            this.directToReview = (Integer) value;
        } else if (value instanceof String) {
            this.directToReview = Boolean.parseBoolean((String) value) ? 1 : 0;
        } else {
            this.directToReview = 0; // 默认值
        }
    }
}