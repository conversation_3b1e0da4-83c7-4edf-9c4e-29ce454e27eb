-- 初始化系统配置数据
-- 这个文件会在应用启动时自动执行（如果表为空）

-- 插入系统配置（如果不存在）
INSERT IGNORE INTO system_configs (config_key, config_value, description, update_time) VALUES
('PARENT_PIN', '0907', '家长PIN码', NOW()),
('CHILD_PIN', '0000', '儿童PIN码', NOW()),
('TASK_EXPIRED_PENALTY', '10', '任务过期惩罚分值', NOW()),
('REQUIRED_TASK_MULTIPLIER', '1.5', '必做任务乘数', NOW()),
('BONUS_REWARD_PROBABILITY', '0.1', '奖励概率加成', NOW()),
('DAILY_MAX_POINTS', '100', '每日最大积分', NOW()),
('OVERDRAFT_ALLOWED', 'true', '允许透支积分', NOW()),
('OVERDRAFT_LIMIT', '50', '积分透支限额', NOW()),
('NOTIFICATION_TASK_REMINDER', 'true', '任务提醒通知', NOW()),
('NOTIFICATION_APPROVAL_NEEDED', 'true', '审批提醒通知', NOW()),
('NOTIFICATION_POINTS_EARNED', 'true', '积分获得通知', NOW()),
('NOTIFICATION_OVERDRAFT_ALERT', 'true', '透支提醒通知', NOW());

-- 初始化积分余额（如果不存在）
INSERT IGNORE INTO point_balance (id, total_points, update_time) VALUES
(1, 0, NOW());

-- 插入兑换商品数据（如果不存在）
INSERT IGNORE INTO exchange_items (name, description, required_points, stock, category, is_active, sort_order, created_time, updated_time) VALUES
('30分钟游戏时间', '可以额外玩30分钟游戏', 10, -1, '游戏时间', true, 1, NOW(), NOW()),
('1小时游戏时间', '可以额外玩1小时游戏', 18, -1, '游戏时间', true, 2, NOW(), NOW()),
('选择今晚晚餐', '可以选择今天晚上吃什么', 15, -1, '特权', true, 3, NOW(), NOW()),
('晚睡30分钟', '今晚可以晚睡30分钟', 12, -1, '特权', true, 4, NOW(), NOW()),
('周末外出游玩', '周末可以选择外出地点', 25, 2, '特权', true, 5, NOW(), NOW()),
('小玩具', '精美小玩具一个', 20, 5, '实物奖励', true, 6, NOW(), NOW()),
('图书', '可以选择一本喜欢的图书', 30, 3, '实物奖励', true, 7, NOW(), NOW()),
('零花钱5元', '获得5元零花钱', 25, -1, '零花钱', true, 8, NOW(), NOW()),
('零花钱10元', '获得10元零花钱', 45, -1, '零花钱', true, 9, NOW(), NOW()),
('免做一次家务', '可以免做一次指定的家务', 8, -1, '特权', true, 10, NOW(), NOW());
