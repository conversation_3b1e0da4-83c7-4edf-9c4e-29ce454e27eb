# 儿童奖励系统 Docker 构建脚本
# 用法: .\docker-build.ps1 -Version "1.0.0"

param (
    [string]$Version = "1.0.0"  # 版本号，默认1.0.0
)

# 镜像仓库配置
$REGISTRY = "registry.cn-hangzhou.aliyuncs.com"
$NAMESPACE = "zlean"
$BACKEND_REPO = "crs-backend"
$FRONTEND_REPO = "crs-frontend"

function Build-Backend {
    param ([string]$Version)
    
    $IMAGE_NAME = "${NAMESPACE}/${BACKEND_REPO}:${Version}"
    $FULL_IMAGE_NAME = "${REGISTRY}/${NAMESPACE}/${BACKEND_REPO}:${Version}"
    
    Write-Host "`n正在构建后端服务 (版本: ${Version})..." -ForegroundColor Green
    Write-Host "镜像名称: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    # 1. 使用Maven构建后端JAR包
    Write-Host "`n步骤1: 使用Maven构建后端..." -ForegroundColor Yellow
    Write-Host "执行命令: mvn clean package -DskipTests"
    Set-Location backend
    mvn clean package -DskipTests
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Maven构建失败" -ForegroundColor Red
        return $false
    }
    Set-Location ..
    Write-Host "Maven构建成功!" -ForegroundColor Green
    
    # 2. 构建Docker镜像
    Write-Host "`n步骤2: 构建Docker镜像..." -ForegroundColor Yellow
    Write-Host "执行命令: docker build --progress=plain -t $IMAGE_NAME -f Dockerfile ."
    docker build --progress=plain -t $IMAGE_NAME -f Dockerfile .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker镜像构建失败" -ForegroundColor Red
        return $false
    }
    Write-Host "Docker镜像构建成功!" -ForegroundColor Green
    
    # 3. 标记镜像
    Write-Host "`n步骤3: 标记镜像到远程仓库..." -ForegroundColor Yellow
    docker tag $IMAGE_NAME $FULL_IMAGE_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker tag标记失败" -ForegroundColor Red
        return $false
    }
    Write-Host "镜像标记成功: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    # 4. 推送镜像到仓库
    Write-Host "`n步骤4: 推送镜像到Docker Registry..." -ForegroundColor Yellow
    docker push $FULL_IMAGE_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker push推送失败" -ForegroundColor Red
        return $false
    }
    Write-Host "镜像推送成功!" -ForegroundColor Green
    
    Write-Host "`n后端服务构建完成!" -ForegroundColor Green
    Write-Host "镜像地址: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    return $true
}

function Build-Frontend {
    param ([string]$Version)
    
    $IMAGE_NAME = "${NAMESPACE}/${FRONTEND_REPO}:${Version}"
    $FULL_IMAGE_NAME = "${REGISTRY}/${NAMESPACE}/${FRONTEND_REPO}:${Version}"
    
    Write-Host "`n正在构建前端服务 (版本: ${Version})..." -ForegroundColor Green
    Write-Host "镜像名称: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    # 1. 构建Docker镜像 (包含npm build)
    Write-Host "`n步骤1: 构建前端Docker镜像..." -ForegroundColor Yellow
    Write-Host "执行命令: docker build --progress=plain -t $IMAGE_NAME -f Dockerfile.frontend ."
    docker build --progress=plain -t $IMAGE_NAME -f Dockerfile.frontend .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 前端Docker镜像构建失败" -ForegroundColor Red
        return $false
    }
    Write-Host "前端Docker镜像构建成功!" -ForegroundColor Green
    
    # 2. 标记镜像
    Write-Host "`n步骤2: 标记镜像到远程仓库..." -ForegroundColor Yellow
    docker tag $IMAGE_NAME $FULL_IMAGE_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker tag标记失败" -ForegroundColor Red
        return $false
    }
    Write-Host "镜像标记成功: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    # 3. 推送镜像到仓库
    Write-Host "`n步骤3: 推送镜像到Docker Registry..." -ForegroundColor Yellow
    docker push $FULL_IMAGE_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker push推送失败" -ForegroundColor Red
        return $false
    }
    Write-Host "镜像推送成功!" -ForegroundColor Green
    
    Write-Host "`n前端服务构建完成!" -ForegroundColor Green
    Write-Host "镜像地址: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    return $true
}

# 主执行逻辑
Write-Host "儿童奖励系统Docker构建 - 版本: $Version" -ForegroundColor Green

# 构建后端
Write-Host "`n======= 构建后端服务 =======" -ForegroundColor Magenta
$backendResult = Build-Backend -Version $Version

# 构建前端
Write-Host "`n======= 构建前端服务 =======" -ForegroundColor Magenta
$frontendResult = Build-Frontend -Version $Version

# 总结结果
Write-Host "`n======= 构建结果总结 =======" -ForegroundColor Yellow
if ($backendResult -and $frontendResult) {
    Write-Host "所有服务构建成功!" -ForegroundColor Green
    Write-Host "`n部署命令:" -ForegroundColor Cyan
    Write-Host "docker-compose up -d" -ForegroundColor White
    Write-Host "`n访问地址:" -ForegroundColor Cyan
    Write-Host "前端: http://你的服务器IP:8080" -ForegroundColor White
    Write-Host "后端: http://你的服务器IP:18080" -ForegroundColor White
} else {
    Write-Host "部分服务构建失败，请检查错误信息" -ForegroundColor Red
}