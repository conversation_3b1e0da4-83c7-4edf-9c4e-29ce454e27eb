# 儿童奖励系统 - 本地Docker运行脚本
# 用于本地开发和测试

param (
    [string]$Action = "start"  # start, stop, restart, logs, status
)

function Start-Services {
    Write-Host "启动儿童奖励系统..." -ForegroundColor Green
    
    # 1. 构建后端
    Write-Host "`n1. 构建后端JAR包..." -ForegroundColor Yellow
    Set-Location backend
    mvn clean package -DskipTests -q
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 后端构建失败" -ForegroundColor Red
        return
    }
    Set-Location ..
    Write-Host "后端构建成功!" -ForegroundColor Green
    
    # 2. 启动Docker Compose
    Write-Host "`n2. 启动Docker服务..." -ForegroundColor Yellow
    docker-compose up -d --build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker服务启动失败" -ForegroundColor Red
        return
    }
    
    Write-Host "`n服务启动成功!" -ForegroundColor Green
    Write-Host "等待服务完全启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    Show-Status
}

function Stop-Services {
    Write-Host "停止儿童奖励系统..." -ForegroundColor Yellow
    docker-compose down
    Write-Host "服务已停止!" -ForegroundColor Green
}

function Restart-Services {
    Write-Host "重启儿童奖励系统..." -ForegroundColor Yellow
    Stop-Services
    Start-Sleep -Seconds 3
    Start-Services
}

function Show-Logs {
    Write-Host "显示服务日志..." -ForegroundColor Yellow
    Write-Host "按 Ctrl+C 退出日志查看" -ForegroundColor Cyan
    docker-compose logs -f
}

function Show-Status {
    Write-Host "`n======= 服务状态 =======" -ForegroundColor Cyan
    docker-compose ps
    
    Write-Host "`n======= 访问地址 =======" -ForegroundColor Cyan
    Write-Host "前端 (家长端): http://localhost:8080" -ForegroundColor White
    Write-Host "前端 (儿童端): http://localhost:8080/child" -ForegroundColor White
    Write-Host "后端API: http://localhost:18080" -ForegroundColor White
    Write-Host "健康检查: http://localhost:18080/actuator/health" -ForegroundColor White
    
    Write-Host "`n======= 容器资源使用 =======" -ForegroundColor Cyan
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

function Test-Services {
    Write-Host "`n======= 服务健康检查 =======" -ForegroundColor Cyan
    
    # 检查后端健康状态
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:18080/actuator/health" -TimeoutSec 5
        if ($response.status -eq "UP") {
            Write-Host "✅ 后端服务: 健康" -ForegroundColor Green
        } else {
            Write-Host "❌ 后端服务: 异常" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 后端服务: 无法连接" -ForegroundColor Red
    }
    
    # 检查前端服务
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 前端服务: 正常" -ForegroundColor Green
        } else {
            Write-Host "❌ 前端服务: 异常" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 前端服务: 无法连接" -ForegroundColor Red
    }
}

# 主执行逻辑
switch ($Action.ToLower()) {
    "start" {
        Start-Services
    }
    "stop" {
        Stop-Services
    }
    "restart" {
        Restart-Services
    }
    "logs" {
        Show-Logs
    }
    "status" {
        Show-Status
        Test-Services
    }
    "test" {
        Test-Services
    }
    default {
        Write-Host "用法: .\docker-run-local.ps1 -Action [start|stop|restart|logs|status|test]" -ForegroundColor Yellow
        Write-Host "`n可用操作:" -ForegroundColor Cyan
        Write-Host "  start   - 启动服务" -ForegroundColor White
        Write-Host "  stop    - 停止服务" -ForegroundColor White
        Write-Host "  restart - 重启服务" -ForegroundColor White
        Write-Host "  logs    - 查看日志" -ForegroundColor White
        Write-Host "  status  - 查看状态" -ForegroundColor White
        Write-Host "  test    - 健康检查" -ForegroundColor White
    }
}