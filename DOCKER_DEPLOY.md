# 儿童奖励系统 - Docker部署指南

## 🐳 Docker部署优势

相比虚拟机Ubuntu部署，Docker方式具有以下优势：
- **资源占用少**: 无需完整操作系统，只运行必要进程
- **启动速度快**: 秒级启动，无需等待系统引导
- **性能更好**: 接近原生性能，无虚拟化开销
- **管理简单**: 一键启动/停止，自动重启
- **环境一致**: 开发、测试、生产环境完全一致

## 📦 部署包结构

```
cps/
├── Dockerfile              # 后端镜像构建文件
├── Dockerfile.frontend     # 前端镜像构建文件
├── docker-compose.yml      # 服务编排配置
├── docker-entrypoint.sh    # 后端启动脚本
├── nginx.conf              # Nginx配置
├── docker-build.ps1        # 构建脚本
├── docker-run-local.ps1    # 本地运行脚本
└── .dockerignore           # Docker忽略文件
```

## 🚀 本地测试部署

### 1. 本地快速启动
```powershell
# 启动服务
.\docker-run-local.ps1 -Action start

# 查看状态
.\docker-run-local.ps1 -Action status

# 查看日志
.\docker-run-local.ps1 -Action logs

# 停止服务
.\docker-run-local.ps1 -Action stop
```

### 2. 访问地址
- **前端 (家长端)**: http://localhost:8080
- **前端 (儿童端)**: http://localhost:8080/child
- **后端API**: http://localhost:18080
- **健康检查**: http://localhost:18080/actuator/health

## 🏠 威联通NAS部署

### 方案1: 使用Docker Compose (推荐)

#### 1. 准备工作
```bash
# SSH连接到NAS
ssh admin@你的NAS_IP

# 创建部署目录
mkdir -p /share/CACHEDEV1_DATA/crs-docker
cd /share/CACHEDEV1_DATA/crs-docker
```

#### 2. 上传文件
将以下文件上传到NAS的部署目录：
- `docker-compose.yml`
- `nginx.conf`
- 构建好的Docker镜像 (或使用远程镜像)

#### 3. 启动服务
```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 方案2: 使用预构建镜像

#### 1. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  crs-backend:
    image: registry.cn-hangzhou.aliyuncs.com/zlean/crs-backend:1.0.0
    container_name: crs-backend
    ports:
      - "18080:18080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC
      - MYSQL_HOST=*************
      - MYSQL_PORT=3307
      - MYSQL_DATABASE=crs
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=123456
    restart: always

  crs-frontend:
    image: registry.cn-hangzhou.aliyuncs.com/zlean/crs-frontend:1.0.0
    container_name: crs-frontend
    ports:
      - "8080:80"
    depends_on:
      - crs-backend
    restart: always
```

#### 2. 启动服务
```bash
docker-compose up -d
```

## 🔧 配置说明

### 环境变量配置
```bash
# 数据库配置
MYSQL_HOST=*************      # 数据库主机
MYSQL_PORT=3307               # 数据库端口
MYSQL_DATABASE=crs            # 数据库名
MYSQL_USERNAME=root           # 数据库用户名
MYSQL_PASSWORD=123456         # 数据库密码

# JVM配置 (NAS优化)
JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC
```

### 端口映射
- `8080:80` - 前端服务 (Nginx)
- `18080:18080` - 后端服务 (Spring Boot)

### 资源限制 (可选)
```yaml
services:
  crs-backend:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
```

## 📊 性能优化

### 1. JVM优化 (针对NAS)
```bash
# 低内存环境
JAVA_OPTS=-Xms128m -Xmx256m -XX:+UseG1GC

# 标准环境
JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC

# 高性能环境
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC
```

### 2. 数据库连接池优化
```yaml
environment:
  - SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE=5
  - SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE=1
  - SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT=30000
```

## 🛠️ 管理命令

### 基本操作
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f crs-backend
docker-compose logs -f crs-frontend
```

### 更新部署
```bash
# 拉取最新镜像
docker-compose pull

# 重新创建容器
docker-compose up -d --force-recreate
```

### 清理资源
```bash
# 停止并删除容器
docker-compose down

# 删除镜像
docker rmi $(docker images | grep crs | awk '{print $3}')

# 清理未使用的资源
docker system prune -f
```

## 🔍 故障排除

### 1. 容器无法启动
```bash
# 查看容器状态
docker-compose ps

# 查看详细日志
docker-compose logs crs-backend

# 检查端口占用
netstat -tlnp | grep -E "8080|18080"
```

### 2. 数据库连接失败
```bash
# 测试数据库连接
docker exec -it crs-backend sh
curl http://localhost:18080/actuator/health
```

### 3. 前端无法访问
```bash
# 检查Nginx配置
docker exec -it crs-frontend nginx -t

# 重新加载Nginx配置
docker exec -it crs-frontend nginx -s reload
```

### 4. 性能问题
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
top
free -h
df -h
```

## 📈 监控和日志

### 1. 日志管理
```bash
# 实时查看日志
docker-compose logs -f --tail=100

# 日志轮转配置
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 2. 健康检查
```bash
# 后端健康检查
curl http://localhost:18080/actuator/health

# 前端健康检查
curl http://localhost:8080
```

## 🔐 安全配置

### 1. 网络隔离
```yaml
networks:
  crs-net:
    driver: bridge
    internal: true  # 内部网络，不允许外部访问
```

### 2. 环境变量安全
```bash
# 使用.env文件存储敏感信息
echo "MYSQL_PASSWORD=your_secure_password" > .env
```

---

## 📞 技术支持

如遇到问题，请提供：
1. 容器状态: `docker-compose ps`
2. 服务日志: `docker-compose logs`
3. 系统信息: `docker version` 和 `docker-compose version`
4. 错误信息截图

🎉 **Docker部署让您的儿童奖励系统运行更稳定、更高效！**