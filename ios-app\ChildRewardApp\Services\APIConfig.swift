import Foundation

// HTTP方法枚举
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

// API错误类型
enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case serverError(Int)
    case unauthorized
    case forbidden
    case notFound
    case timeout
    case unknown

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .serverError(let code):
            return "服务器错误: \(code)"
        case .unauthorized:
            return "未授权访问"
        case .forbidden:
            return "访问被禁止"
        case .notFound:
            return "资源未找到"
        case .timeout:
            return "请求超时"
        case .unknown:
            return "未知错误"
        }
    }
}

// API配置
struct APIConfig {
    // 基础URL配置 - 现在使用EnvironmentManager动态获取
    static var baseURL: String {
        return EnvironmentManager.shared.currentBaseURL + "/api"
    }

    // 儿童端API路径 - 修复：儿童端直接使用/api，不需要/child后缀
    static let childAPIPath = ""

    // 完整的儿童端API URL
    static var childAPIURL: String {
        return baseURL + childAPIPath
    }
    
    // 请求超时时间 - 统一设置为15秒，给网络请求足够时间
    static let timeoutInterval: TimeInterval = 15.0

    // 快速超时时间 - 用于需要快速响应的场景
    static let fastTimeoutInterval: TimeInterval = 8.0
    
    // 请求头
    static let defaultHeaders: [String: String] = [
        "Content-Type": "application/json",
        "Accept": "application/json"
    ]
}

// API端点枚举
enum APIEndpoint {
    // 任务相关
    case todayTasks
    case yesterdayTasks
    case overdueTasks
    case pendingTasks
    case taskDetail(Int)
    case startTask(Int)
    case completeTask(Int)
    case tasksByDate(String)
    
    // 积分相关
    case pointBalance
    case todayPointChange
    case pointRecords
    case pointRecordsWithTime(String, String)

    // 奖励相关
    case availableRewardPools
    case drawReward(poolId: Int)

    // 兑换相关
    case availableExchangeItems
    case exchangeItemsByCategory(category: String)
    case exchangeItem(itemId: Int)
    case exchangeRecords
    case unusedExchangeRecords
    
    var path: String {
        switch self {
        // 任务相关
        case .todayTasks:
            return "/tasks/today"
        case .yesterdayTasks:
            return "/tasks/yesterday"
        case .overdueTasks:
            return "/tasks/overdue"
        case .pendingTasks:
            return "/tasks/pending"
        case .taskDetail(let id):
            return "/tasks/\(id)"
        case .startTask(let id):
            return "/tasks/\(id)/start"
        case .completeTask(let id):
            return "/tasks/\(id)/complete"
        case .tasksByDate(let date):
            return "/tasks/date/\(date)"
            
        // 积分相关
        case .pointBalance:
            return "/points/total"
        case .todayPointChange:
            return "/points/today"
        case .pointRecords:
            return "/points/records"
        case .pointRecordsWithTime(let start, let end):
            return "/points/records?startTime=\(start)&endTime=\(end)"

        // 奖励相关
        case .availableRewardPools:
            return "/rewards/pools/available"
        case .drawReward(let poolId):
            return "/rewards/pools/\(poolId)/draw"

        // 兑换相关
        case .availableExchangeItems:
            return "/exchange/items"
        case .exchangeItemsByCategory(let category):
            return "/exchange/items?category=\(category)"
        case .exchangeItem(let itemId):
            return "/exchange/exchange/\(itemId)"
        case .exchangeRecords:
            return "/exchange/records"
        case .unusedExchangeRecords:
            return "/exchange/records/unused"
        }
    }
    
    var method: HTTPMethod {
        switch self {
        case .startTask, .completeTask, .drawReward, .exchangeItem:
            return .POST
        default:
            return .GET
        }
    }
}

// API响应模型
struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let message: String?
    let code: Int?
}

// 通用响应模型
struct EmptyResponse: Codable {
    // 空响应体
}
