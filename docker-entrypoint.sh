#!/bin/sh
echo "Starting Child Reward System..."

# 显式设置数据库连接参数
echo "Using database: ${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}"
echo "Using JVM options: ${JAVA_OPTS:--server -Xms256m -Xmx512m -XX:+UseG1GC}"

# 确保日志目录存在
mkdir -p /app/logs

# 使用环境变量中的JAVA_OPTS，如果未设置则使用默认值
# NAS优化的JVM配置 - 低内存占用
java ${JAVA_OPTS:--server -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -Xlog:gc*=info:file=/app/logs/gc-%t.log:time,uptime,level,tags:filecount=5,filesize=50m} \
  -Dspring.datasource.url=jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}?useUnicode=true\&characterEncoding=utf8\&useSSL=false\&serverTimezone=Asia/Shanghai \
  -Dspring.datasource.username=${MYSQL_USERNAME} \
  -Dspring.datasource.password=${MYSQL_PASSWORD} \
  -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE} \
  -jar /app/app.jar