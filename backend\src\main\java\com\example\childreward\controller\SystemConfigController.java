package com.example.childreward.controller;

import com.example.childreward.entity.SystemConfig;
import com.example.childreward.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*") // 允许所有来源跨域请求
@RequestMapping("/api/configs")
@RequiredArgsConstructor
public class SystemConfigController {

    private final SystemConfigService configService;

    @GetMapping
    public ResponseEntity<List<SystemConfig>> getAllConfigs() {
        return ResponseEntity.ok(configService.getAllConfigs());
    }

    @GetMapping("/{key}")
    public ResponseEntity<SystemConfig> getConfigByKey(@PathVariable String key) {
        return ResponseEntity.ok(configService.getConfigByKey(key));
    }

    @PostMapping
    public ResponseEntity<SystemConfig> createConfig(@RequestBody SystemConfig config) {
        return new ResponseEntity<>(configService.createConfig(config), HttpStatus.CREATED);
    }

    @PutMapping("/{key}")
    public ResponseEntity<SystemConfig> updateConfig(@PathVariable String key, @RequestBody Map<String, String> payload) {
        String value = payload.get("value");
        String description = payload.get("description");
        return ResponseEntity.ok(configService.updateConfig(key, value, description));
    }

    @DeleteMapping("/{key}")
    public ResponseEntity<Void> deleteConfig(@PathVariable String key) {
        configService.deleteConfig(key);
        return ResponseEntity.noContent().build();
    }
} 