package com.example.childreward.aspect;

import com.example.childreward.annotation.PreventDuplicateSubmit;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交的AOP切面
 */
@Aspect
@Component
@Log4j2
public class DuplicateSubmitAspect {

    // 使用ConcurrentHashMap存储用户提交记录
    private static final ConcurrentHashMap<String, Long> SUBMISSION_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 环绕通知，处理防重复提交
     */
    @Around("@annotation(com.example.childreward.annotation.PreventDuplicateSubmit)")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        
        // 获取用户标识 (IP + UA + URL + 方法)
        String userIdentifier = getUserIdentifier(request);
        String methodName = joinPoint.getSignature().getName();
        String targetClassName = joinPoint.getTarget().getClass().getName();
        String cacheKey = userIdentifier + ":" + targetClassName + ":" + methodName;
        
        // 获取注解和配置
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        PreventDuplicateSubmit annotation = method.getAnnotation(PreventDuplicateSubmit.class);
        long lockTime = annotation.value();
        String errorMessage = annotation.message();
        
        // 检查是否在冷却期
        if (isRecentDuplicateSubmission(cacheKey, lockTime)) {
            log.warn("检测到重复请求: key={}, 用户IP={}, 方法={}", cacheKey, request.getRemoteAddr(), methodName);
            throw new IllegalStateException(errorMessage);
        }
        
        // 记录本次请求
        recordSubmission(cacheKey, lockTime);
        
        // 执行目标方法
        return joinPoint.proceed();
    }
    
    /**
     * 获取用户唯一标识
     */
    private String getUserIdentifier(HttpServletRequest request) {
        String ip = request.getRemoteAddr();
        String url = request.getRequestURI();
        String userAgent = request.getHeader("User-Agent");
        return ip + ":" + url + ":" + (userAgent != null ? userAgent.hashCode() : "");
    }
    
    /**
     * 检查是否是最近的重复提交
     */
    private boolean isRecentDuplicateSubmission(String key, long lockTime) {
        Long lastSubmitTime = SUBMISSION_CACHE.get(key);
        if (lastSubmitTime == null) {
            return false;
        }
        
        long now = System.currentTimeMillis();
        return (now - lastSubmitTime) < lockTime;
    }
    
    /**
     * 记录本次提交
     */
    private void recordSubmission(String key, long lockTime) {
        // 记录当前时间
        SUBMISSION_CACHE.put(key, System.currentTimeMillis());
        
        // 使用后台线程在指定时间后自动清除缓存记录
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.schedule(() -> {
            SUBMISSION_CACHE.remove(key);
            scheduler.shutdown();
        }, lockTime, TimeUnit.MILLISECONDS);
    }
} 