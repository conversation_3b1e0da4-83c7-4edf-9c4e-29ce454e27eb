# 作业小助手 iOS 应用

这是作业小助手的 iPad 原生 iOS 版本，专为 iPad 横屏使用设计。

## 功能特性

### 📱 iPad 专用设计
- **三栏布局**：左侧导航栏、中间主内容区、右侧信息面板
- **横屏优化**：专为 iPad 横屏使用优化的界面布局
- **毛玻璃效果**：现代化的 iOS 设计风格

### ✅ 任务管理
- 任务列表展示（每行2个任务卡片）
- 任务详情页面（支持富文本和图片）
- 任务步骤系统（带进度显示）
- 任务完成状态管理

### 🎁 积分奖励系统
- 积分累积和显示
- 奖励兑换功能
- 积分历史记录

### 🛒 商店系统
- 商品展示和兑换
- 分类浏览
- 兑换历史

### 👤 个人中心
- 用户信息管理
- 设置选项
- 统计数据

## 技术规格

- **最低 iOS 版本**：iOS 16.0+
- **支持设备**：iPad（推荐横屏使用）
- **开发语言**：Swift + SwiftUI
- **架构**：arm64（真机版本）

## 构建说明

### 环境要求
- macOS 13.0+
- Xcode 15.0+
- iOS 16.0+ SDK

### 构建步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ios-app
   ```

2. **打开项目**
   ```bash
   open ChildRewardApp.xcodeproj
   ```

3. **构建 IPA 文件**
   ```bash
   chmod +x create_ipa.sh
   ./create_ipa.sh
   ```

4. **输出文件**
   - 构建完成后，ipa 文件位于 `build/作业小助手.ipa`
   - 文件大小约 15MB（包含 Swift 运行时库）

## 安装说明

### 使用巨魔签名安装
1. 将生成的 `作业小助手.ipa` 文件传输到 iPad
2. 使用巨魔签名工具进行签名
3. 安装到 iPad 设备

### 注意事项
- 应用未签名，需要使用第三方签名工具
- 建议在 iPad 上横屏使用以获得最佳体验
- 首次启动可能需要在设置中信任开发者

## 项目结构

```
ios-app/
├── ChildRewardApp/
│   ├── ContentView.swift          # 主界面
│   ├── Models/                    # 数据模型
│   ├── Views/                     # 视图组件
│   ├── ViewModels/                # 视图模型
│   ├── Services/                  # 服务层
│   ├── Utils/                     # 工具类
│   └── Resources/                 # 资源文件
├── ChildRewardApp.xcodeproj/      # Xcode 项目文件
├── create_ipa.sh                  # 构建脚本
└── README.md                      # 说明文档
```

## 开发说明

### 主要组件
- **ContentView**：主界面，包含三栏布局
- **TaskListView**：任务列表视图
- **TaskDetailView**：任务详情视图
- **RewardStoreView**：奖励商店视图
- **ProfileView**：个人中心视图

### 数据模型
- **TaskItem**：任务数据模型
- **RewardItem**：奖励数据模型
- **UserProfile**：用户信息模型

### 设计原则
- 遵循 iOS Human Interface Guidelines
- 使用 SwiftUI 原生组件
- 响应式设计，适配不同屏幕尺寸
- 无障碍访问支持

## 版本历史

### v1.0.0
- 初始版本
- 完整的任务管理功能
- 积分奖励系统
- iPad 专用三栏布局
- iOS 16.0+ 兼容性

## 许可证

本项目遵循 MIT 许可证。详见 LICENSE 文件。
