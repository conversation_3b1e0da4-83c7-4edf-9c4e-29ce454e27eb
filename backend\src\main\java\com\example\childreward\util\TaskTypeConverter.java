package com.example.childreward.util;

import com.example.childreward.entity.TaskType;

/**
 * 任务类型转换工具类
 * 用于在枚举和整数之间进行转换
 */
public class TaskTypeConverter {
    
    /**
     * 将TaskType枚举转换为整数
     * @param taskType 任务类型枚举
     * @return 对应的整数值
     */
    public static Integer toInteger(TaskType taskType) {
        if (taskType == null) {
            return 0; // 默认为REQUIRED
        }
        
        switch (taskType) {
            case REQUIRED:
                return 0;
            case OPTIONAL:
                return 1;
            case REWARD:
                return 2;
            case PENALTY_APPROVAL:
                return 3;
            default:
                return 0;
        }
    }
    
    /**
     * 将整数转换为TaskType枚举
     * @param value 整数值
     * @return 对应的任务类型枚举
     */
    public static TaskType fromInteger(Integer value) {
        if (value == null) {
            return TaskType.REQUIRED; // 默认为REQUIRED
        }
        
        switch (value) {
            case 0:
                return TaskType.REQUIRED;
            case 1:
                return TaskType.OPTIONAL;
            case 2:
                return TaskType.REWARD;
            case 3:
                return TaskType.PENALTY_APPROVAL;
            default:
                return TaskType.REQUIRED;
        }
    }
    
    /**
     * 将布尔值转换为整数（用于directToReview字段）
     * @param value 布尔值
     * @return 对应的整数值
     */
    public static Integer booleanToInteger(Boolean value) {
        return (value != null && value) ? 1 : 0;
    }
    
    /**
     * 将整数转换为布尔值（用于directToReview字段）
     * @param value 整数值
     * @return 对应的布尔值
     */
    public static Boolean integerToBoolean(Integer value) {
        return value != null && value == 1;
    }
}
