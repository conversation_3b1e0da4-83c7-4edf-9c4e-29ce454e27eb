import SwiftUI

// MARK: - 转盘抽奖视图
struct SpinWheelView: View {
    let items: [SpinWheelItem]
    let onSpinComplete: (SpinWheelItem) -> Void
    
    @State private var rotation: Double = 0
    @State private var isSpinning = false
    @State private var selectedItem: SpinWheelItem?
    
    private let wheelSize: CGFloat = 280
    private let pointerSize: CGFloat = 20
    
    var body: some View {
        VStack(spacing: 30) {
            // 转盘主体
            ZStack {
                // 转盘背景
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: wheelSize, height: wheelSize)
                    .shadow(color: .black.opacity(0.2), radius: 15, x: 0, y: 8)
                
                // 转盘扇形区域
                ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                    WheelSegment(
                        item: item,
                        angle: segmentAngle,
                        startAngle: Double(index) * segmentAngle,
                        radius: wheelSize / 2
                    )
                }
                
                // 中心圆
                Circle()
                    .fill(.white)
                    .frame(width: 60, height: 60)
                    .shadow(color: .black.opacity(0.1), radius: 5)
                
                // 中心图标
                Image(systemName: "star.fill")
                    .font(.title2)
                    .foregroundColor(.orange)
            }
            .rotationEffect(.degrees(rotation))
            .animation(.easeOut(duration: isSpinning ? 3.0 : 0.5), value: rotation)
            
            // 指针
            VStack {
                Triangle()
                    .fill(.red)
                    .frame(width: pointerSize, height: pointerSize)
                    .shadow(color: .black.opacity(0.3), radius: 3)
                
                Spacer()
            }
            .offset(y: -wheelSize/2 - 10)
            
            // 抽奖按钮
            Button(action: spinWheel) {
                HStack(spacing: 12) {
                    Image(systemName: isSpinning ? "arrow.clockwise" : "play.fill")
                        .font(.title3)
                    
                    Text(isSpinning ? "转盘中..." : "开始抽奖")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(isSpinning ? .gray : .blue)
                        .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                )
            }
            .disabled(isSpinning)
            .scaleEffect(isSpinning ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSpinning)
        }
        .padding(40)
    }
    
    private var segmentAngle: Double {
        360.0 / Double(items.count)
    }
    
    private func spinWheel() {
        guard !isSpinning else { return }
        
        isSpinning = true
        
        // 随机选择一个奖品
        let randomIndex = Int.random(in: 0..<items.count)
        selectedItem = items[randomIndex]
        
        // 计算目标角度
        let targetAngle = Double(randomIndex) * segmentAngle
        let spins = Double.random(in: 5...8) * 360 // 5-8圈
        let finalRotation = rotation + spins + (360 - targetAngle)
        
        // 开始旋转
        withAnimation(.easeOut(duration: 3.0)) {
            rotation = finalRotation
        }
        
        // 延迟显示结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.2) {
            isSpinning = false
            if let item = selectedItem {
                onSpinComplete(item)
            }
        }
    }
}

// MARK: - 转盘扇形区域
struct WheelSegment: View {
    let item: SpinWheelItem
    let angle: Double
    let startAngle: Double
    let radius: CGFloat
    
    var body: some View {
        ZStack {
            // 扇形背景
            Path { path in
                let center = CGPoint(x: radius, y: radius)
                path.move(to: center)
                path.addArc(
                    center: center,
                    radius: radius - 5,
                    startAngle: .degrees(startAngle - 90),
                    endAngle: .degrees(startAngle + angle - 90),
                    clockwise: false
                )
                path.closeSubpath()
            }
            .foregroundColor(item.color.opacity(0.8))
            .overlay(
                Path { path in
                    let center = CGPoint(x: radius, y: radius)
                    path.move(to: center)
                    path.addArc(
                        center: center,
                        radius: radius - 5,
                        startAngle: .degrees(startAngle - 90),
                        endAngle: .degrees(startAngle + angle - 90),
                        clockwise: false
                    )
                    path.closeSubpath()
                }
                .stroke(Color.white, lineWidth: 2)
            )
            
            // 奖品内容
            VStack(spacing: 4) {
                Image(systemName: item.iconName)
                    .font(.title3)
                    .foregroundColor(.white)
                
                Text(item.name)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .offset(
                x: cos(Angle.degrees(startAngle + angle/2 - 90).radians) * (radius * 0.6),
                y: sin(Angle.degrees(startAngle + angle/2 - 90).radians) * (radius * 0.6)
            )
        }
        .frame(width: radius * 2, height: radius * 2)
    }
}

// MARK: - 三角形指针
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.closeSubpath()
        return path
    }
}

// MARK: - 转盘奖品项目
struct SpinWheelItem: Identifiable {
    let id = UUID()
    let name: String
    let iconName: String
    let color: Color
    let value: Int
    let type: SpinWheelItemType
}

enum SpinWheelItemType {
    case reward
    case penalty
    case points
    case special
}

// MARK: - 预览
struct SpinWheelView_Previews: PreviewProvider {
    static var previews: some View {
        SpinWheelView(
            items: [
                SpinWheelItem(name: "看动画片", iconName: "tv", color: .blue, value: 30, type: .reward),
                SpinWheelItem(name: "冰淇淋", iconName: "snowflake", color: .cyan, value: 1, type: .reward),
                SpinWheelItem(name: "10积分", iconName: "star.fill", color: .orange, value: 10, type: .points),
                SpinWheelItem(name: "做家务", iconName: "house", color: .red, value: 15, type: .penalty),
                SpinWheelItem(name: "小玩具", iconName: "teddybear", color: .purple, value: 1, type: .reward),
                SpinWheelItem(name: "5积分", iconName: "star", color: .yellow, value: 5, type: .points),
                SpinWheelItem(name: "早睡", iconName: "moon", color: .indigo, value: 30, type: .penalty),
                SpinWheelItem(name: "特殊奖励", iconName: "gift", color: .pink, value: 1, type: .special)
            ]
        ) { item in
            print("抽中了: \(item.name)")
        }
        .preferredColorScheme(.light)
    }
}
