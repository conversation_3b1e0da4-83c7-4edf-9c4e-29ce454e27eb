// 儿童端主题
export const childTheme = {
  backgroundColor: '#f0f9ff',
  textColor: '#333333',
  primaryColor: '#007AFF',
  secondaryColor: '#FF3B30',
  accentColor: '#FFCC00',
  shadowColor: 'rgba(0, 0, 0, 0.15)',
  gradients: {
    primary: 'linear-gradient(135deg, #4c7cff 0%, #36c9ff 100%)',
    secondary: 'linear-gradient(135deg, #ff7c4c 0%, #ffb636 100%)',
    success: 'linear-gradient(135deg, #7cff4c 0%, #36ffb6 100%)',
    warning: 'linear-gradient(135deg, #ffe84c 0%, #ffb636 100%)',
    danger: 'linear-gradient(135deg, #ff4c4c 0%, #ff367c 100%)',
  },
  statusColors: {
    COMPLETED: '#4caf50',
    IN_PROGRESS: '#007AFF',
    NOT_STARTED: '#FF3B30',
    PENDING: '#FFCC00',
    REJECTED: '#FF3B30',
    OVERDUE: 'darkgrey'
  },
  borderRadius: '20px',
  fontSizes: {
    small: '0.875rem',
    medium: '1rem',
    large: '1.25rem',
    xlarge: '1.5rem',
    xxlarge: '2rem',
  },
};

// 家长端主题
export const parentTheme = {
  backgroundColor: '#ffffff',
  textColor: '#333333',
  primaryColor: '#3f51b5',
  secondaryColor: '#f50057',
  accentColor: '#00c853',
  shadowColor: 'rgba(0, 0, 0, 0.1)',
  gradients: {
    primary: 'linear-gradient(135deg, #3f51b5 0%, #2196f3 100%)',
    secondary: 'linear-gradient(135deg, #f50057 0%, #ff4081 100%)',
    success: 'linear-gradient(135deg, #00c853 0%, #69f0ae 100%)',
    warning: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',
    danger: 'linear-gradient(135deg, #f44336 0%, #ff5252 100%)',
  },
  statusColors: {
    COMPLETED: '#4caf50',
    IN_PROGRESS: '#ff9800',
    NOT_STARTED: '#f44336',
    PENDING: '#9e9e9e',
    REJECTED: '#ff3b30',
  },
  borderRadius: '8px',
  fontSizes: {
    small: '0.75rem',
    medium: '0.875rem',
    large: '1rem',
    xlarge: '1.25rem',
    xxlarge: '1.5rem',
  },
};

// 动画预设
export const animations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.3 },
  },
  slideUp: {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
    transition: { duration: 0.3 },
  },
  slideDown: {
    initial: { y: -20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 20, opacity: 0 },
    transition: { duration: 0.3 },
  },
  popIn: {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 },
    transition: { type: 'spring', stiffness: 300, damping: 15 },
  },
  rotate: {
    initial: { rotate: 0 },
    animate: { rotate: 360 },
    transition: { repeat: Infinity, duration: 2, ease: 'linear' },
  },
  bounce: {
    animate: {
      y: [0, -10, 0],
    },
    transition: {
      repeat: Infinity,
      repeatType: 'loop',
      duration: 1,
      ease: 'easeInOut',
    },
  },
}; 