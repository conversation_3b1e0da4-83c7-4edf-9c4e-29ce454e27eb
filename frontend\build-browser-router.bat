@echo off
echo 正在构建BrowserRouter版本（需要nginx配置try_files）...
echo.

REM 设置环境变量使用BrowserRouter
set NODE_ENV=production
set VITE_USE_BROWSER_ROUTER=true

REM 执行构建
npm run build:prod

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ BrowserRouter版本构建成功！
    echo.
    echo 📁 构建文件位置: dist/
    echo 🌐 API地址配置: http://*************:18080/api
    echo 🔗 路由模式: BrowserRouter (干净的URL)
    echo.
    echo 📦 创建BrowserRouter部署压缩包...
    
    REM 创建部署压缩包
    if exist "crs-frontend-deploy.zip" del "crs-frontend-deploy.zip"
    powershell "Compress-Archive -Path 'dist\*' -DestinationPath 'crs-frontend-deploy.zip'"
    
    if exist "crs-frontend-deploy.zip" (
        echo ✅ BrowserRouter部署压缩包创建成功: crs-frontend-deploy.zip
        echo.
        echo 🚀 部署说明:
        echo 1. 将 crs-frontend-deploy.zip 上传到服务器
        echo 2. 解压到网站根目录
        echo 3. 配置nginx添加: try_files $uri $uri/ /index.html;
        echo 4. 访问地址: http://your-server/
        echo 5. 路由示例: http://your-server/child/rewards
        echo.
        echo 📋 BrowserRouter特点:
        echo - 干净的URL，无 # 符号
        echo - 需要服务器端配置支持
        echo - 更好的SEO和用户体验
        echo.
        echo 📄 nginx配置文件已生成: nginx-spa.conf
    ) else (
        echo ❌ 压缩包创建失败
    )
) else (
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause