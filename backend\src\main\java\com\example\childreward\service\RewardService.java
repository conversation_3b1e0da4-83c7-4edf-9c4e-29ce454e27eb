package com.example.childreward.service;

import com.example.childreward.entity.RewardItem;
import com.example.childreward.entity.RewardPool;

import java.util.List;

public interface RewardService {
    
    /**
     * 创建奖品池
     */
    RewardPool createRewardPool(RewardPool rewardPool);
    
    /**
     * 更新奖品池
     */
    RewardPool updateRewardPool(Long id, RewardPool rewardPool);
    
    /**
     * 删除奖品池
     */
    void deleteRewardPool(Long id);
    
    /**
     * 获取所有启用的奖品池
     */
    List<RewardPool> getAllEnabledPools();
    
    /**
     * 获取所有启用的奖励池（不包含惩罚池）
     */
    List<RewardPool> getAllEnabledRewardPools();
    
    /**
     * 获取所有启用的惩罚池
     */
    List<RewardPool> getAllEnabledPenaltyPools();
    
    /**
     * 获取奖品池详情
     */
    RewardPool getRewardPoolById(Long id);
    
    /**
     * 添加奖品项
     */
    RewardItem addRewardItem(Long poolId, RewardItem rewardItem);
    
    /**
     * 更新奖品项
     */
    RewardItem updateRewardItem(Long id, RewardItem rewardItem);
    
    /**
     * 删除奖品项
     */
    void deleteRewardItem(Long id);
    
    /**
     * 获取奖品项详情
     */
    RewardItem getRewardItemById(Long id);
    
    /**
     * 抽奖（扣减积分）
     */
    RewardItem drawReward(Long poolId);
    
    /**
     * 惩罚抽奖（增加积分）
     */
    RewardItem drawPenalty(Long poolId);
} 