import { getApiClient, getApiBaseUrl } from './apiConfig.js';



// 任务相关API
export const taskApi = {
  // 获取任务列表
  getTasks: (date) => {
    const client = getApiClient();
    return client.get(date ? `/tasks/date/${date}` : '/tasks/today');
  },

  // 获取所有任务
  getAllTasks: () => {
    const client = getApiClient();
    return client.get('/tasks/all');
  },

  // 获取待审批任务
  getPendingTasks: (startDate, endDate) => {
    let url = '/tasks/pending';
    const params = [];

    if (startDate) params.push(`startDate=${startDate}`);
    if (endDate) params.push(`endDate=${endDate}`);

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    const client = getApiClient();
    return client.get(url);
  },
  
  // 获取已完成任务
  getCompletedTasks: () => {
    // 临时方案：获取所有任务，前端过滤已完成状态（兼容大小写和不同格式）
    const client = getApiClient();
    return client.get('/tasks/today').then(response => {
      return {
        ...response,
        data: response.data.filter(task => {
          const status = task.status ? task.status.toString().toUpperCase() : '';
          return status === 'COMPLETED' || status === 'APPROVED' || status === 'COMPLETE';
        })
      };
    });
  },

  // 获取已拒绝任务
  getRejectedTasks: () => {
    // 临时方案：获取所有任务，前端过滤已拒绝状态（兼容大小写和不同格式）
    const client = getApiClient();
    return client.get('/tasks/today').then(response => {
      return {
        ...response,
        data: response.data.filter(task => {
          const status = task.status ? task.status.toString().toUpperCase() : '';
          return status === 'REJECTED' || status === 'REJECT';
        })
      };
    });
  },

  // 获取指定日期范围和状态的任务
  getTasksByDateRangeAndStatus: (startDate, endDate, status) => {
    let url = '/tasks/range';
    const params = [];

    if (startDate) params.push(`startDate=${startDate}`);
    if (endDate) params.push(`endDate=${endDate}`);
    if (status) params.push(`status=${status}`);

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    const client = getApiClient();
    return client.get(url);
  },
  
  // 创建任务
  createTask: (taskData) => {
    const client = getApiClient();
    return client.post('/tasks', taskData);
  },

  // 更新任务
  updateTask: (taskId, taskData) => {
    const client = getApiClient();
    return client.put(`/tasks/${taskId}`, taskData);
  },

  // 删除任务
  deleteTask: (taskId) => {
    const client = getApiClient();
    return client.delete(`/tasks/${taskId}`);
  },
  
  // 开始任务
  startTask: (taskId) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/start`);
  },

  // 完成任务
  completeTask: (taskId) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/complete`);
  },
  
  // 审批任务
  approveTask: (taskId, actualPoints) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/approve?actualPoints=${actualPoints}`);
  },

  // 拒绝任务
  rejectTask: (taskId, reason) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/reject${reason ? `?reason=${reason}` : ''}`);
  }
};

// 积分相关API
export const pointApi = {
  // 获取总积分
  getTotalPoints: () => {
    const client = getApiClient();
    return client.get('/points/total');
  },

  // 获取今日积分变化
  getTodayPoints: () => {
    const client = getApiClient();
    return client.get('/points/today');
  },

  // 获取指定日期的积分变化
  getPointsByDate: (date) => {
    const client = getApiClient();
    return client.get(`/points/date/${date}`);
  },
  
  // 获取积分记录
  getPointRecords: (startTime, endTime) => {
    let url = '/points/records';
    const params = [];
    
    if (startTime) params.push(`startTime=${encodeURIComponent(startTime.toISOString())}`);
    if (endTime) params.push(`endTime=${encodeURIComponent(endTime.toISOString())}`);
    
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }
    
    console.log('🔍 发送积分记录请求:', url);
    console.log('请求参数:', { startTime, endTime });
    
    const client = getApiClient();
    return client.get(url)
      .then(response => {
        console.log('✅ 积分记录响应:', response);
        
        // 检查响应是否为空
        if (!response) {
          console.warn('⚠️ 响应对象为空');
          return { data: [] };
        }
        
        // 检查data是否存在
        if (!response.data && !Array.isArray(response.data)) {
          console.warn('⚠️ 响应数据为空或非数组:', response);
          return { ...response, data: [] };
        }
        
        return response;
      })
      .catch(error => {
        console.error('❌ 获取积分记录失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        return { data: [] };
      });
  },
  
  // 获取最近几天的积分记录（简化版）
  getRecentPointRecords: (days = 7) => {
    console.log('🔍 发送获取最近积分记录请求, days=' + days);
    
    const client = getApiClient();
    return client.get(`/points/recent?days=${days}`)
      .then(response => {
        console.log('✅ 最近积分记录响应:', response);
        
        // 检查响应是否为空
        if (!response) {
          console.warn('⚠️ 响应对象为空');
          return { data: [] };
        }
        
        // 检查data是否存在且是数组
        if (!response.data) {
          console.warn('⚠️ 响应数据为空');
          return { ...response, data: [] };
        }
        
        // 打印数据条数
        if (Array.isArray(response.data)) {
          console.log(`📊 获取到 ${response.data.length} 条积分记录`);
          if (response.data.length > 0) {
            console.log('📝 第一条记录:', response.data[0]);
          }
        }
        
        return response;
      })
      .catch(error => {
        console.error('❌ 获取最近积分记录失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        return { data: [] };
      });
  },
  
  // 添加积分
  addPoints: (points, description) => {
    const client = getApiClient();
    return client.post('/points/add', null, {
      params: { points, description }
    });
  },

  // 扣减积分
  deductPoints: (points, description) => {
    const client = getApiClient();
    return client.post('/points/deduct', null, {
      params: { points, description }
    });
  },

  // 扣减惩罚积分
  deductPunishmentPoints: (points, description) => {
    const client = getApiClient();
    return client.post('/points/deduct/punishment', null, {
      params: { points, description }
    });
  },

  // 手动调整积分（正数为加分，负数为扣分）
  manualAdjustPoints: (points, description) => {
    const client = getApiClient();
    if (points > 0) {
      return client.post('/points/add', null, {
        params: { points, description }
      });
    } else if (points < 0) {
      return client.post('/points/deduct', null, {
        params: { points: Math.abs(points), description }
      });
    } else {
      return Promise.reject(new Error('积分变化不能为0'));
    }
  },
  
  // 获取所有积分记录（调试用）
  getAllPointRecords: () => {
    console.log('🔍 发送获取所有积分记录请求');

    const client = getApiClient();
    return client.get('/points/debug/all-records')
      .then(response => {
        console.log('✅ 所有积分记录响应:', response);
        
        // 检查响应是否为空
        if (!response) {
          console.warn('⚠️ 响应对象为空');
          return { data: [] };
        }
        
        // 检查data是否存在
        if (!response.data && !Array.isArray(response.data)) {
          console.warn('⚠️ 响应数据为空或非数组');
          return { ...response, data: [] };
        }
        
        return response;
      })
      .catch(error => {
        console.error('❌ 获取所有积分记录失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        return { data: [] };
      });
  },
  
  // 创建测试积分记录（调试用）
  createTestData: () => {
    const client = getApiClient();
    return client.post('/points/debug/create-test-data');
  },

  // 创建历史积分记录（调试用）
  createHistoricalData: () => {
    console.log('🔧 发送创建历史积分记录请求');

    const client = getApiClient();
    return client.post('/points/debug/create-historical-data')
      .then(response => {
        console.log('✅ 创建历史数据响应:', response);
        return response;
      })
      .catch(error => {
        console.error('❌ 创建历史积分记录失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        throw error;
      });
  },
  
  // 修复积分记录日期问题（调试用）
  fixPointRecordDates: () => {
    console.log('🔧 发送修复积分记录日期请求');

    const client = getApiClient();
    return client.post('/points/debug/fix-dates')
      .then(response => {
        console.log('✅ 修复日期响应:', response);
        return response;
      })
      .catch(error => {
        console.error('❌ 修复积分记录日期失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        throw error;
      });
  },
  
  // 系统诊断（调试用）
  diagnoseSystem: () => {
    console.log('🔍 发送系统诊断请求');

    const client = getApiClient();
    return client.get('/points/debug/diagnose')
      .then(response => {
        console.log('✅ 系统诊断响应:', response);
        return response;
      })
      .catch(error => {
        console.error('❌ 系统诊断失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        }
        throw error;
      });
  }
};

// 奖励相关API
export const rewardApi = {
  // 获取所有奖品池
  getRewardPools: () => {
    const client = getApiClient();
    return client.get('/rewards/pools');
  },

  // 获取奖品池详情
  getRewardPool: (poolId) => {
    const client = getApiClient();
    return client.get(`/rewards/pools/${poolId}`);
  },

  // 创建奖品池
  createRewardPool: (poolData) => {
    const client = getApiClient();
    return client.post('/rewards/pools', poolData);
  },

  // 更新奖品池
  updateRewardPool: (poolId, poolData) => {
    const client = getApiClient();
    return client.put(`/rewards/pools/${poolId}`, poolData);
  },

  // 删除奖品池
  deleteRewardPool: (poolId) => {
    const client = getApiClient();
    return client.delete(`/rewards/pools/${poolId}`);
  },
  
  // 添加奖品
  addRewardItem: (poolId, itemData) => {
    return new Promise((resolve, reject) => {
      const client = getApiClient();
      client.post(`/rewards/pools/${poolId}/items`, itemData)
        .then(response => {
          // 成功处理
          resolve(response);
        })
        .catch(error => {
          // 特殊处理201 Created状态，将其视为成功
          if (error.response && error.response.status === 201) {
            // 尝试从响应中获取数据
            let responseData = error.response.data;
            
            // 如果数据不存在，创建一个默认的响应数据
            if (!responseData || typeof responseData !== 'object') {
              responseData = { 
                success: true, 
                message: "奖品已创建", 
                id: Date.now() // 临时ID，实际应用中应由后端提供
              };
            }
            
            // 如果状态码是201但没有id，添加一个临时id
            if (!responseData.id) {
              responseData.id = Date.now();
            }
            
            resolve({
              status: 201,
              data: responseData
            });
          } else if (error.code === 'ERR_NETWORK' || error.code === 'ERR_INCOMPLETE_CHUNKED_ENCODING') {
            // 处理特定的网络错误，可能是由于201状态码导致的连接提前关闭
            resolve({
              status: 201,
              data: { 
                success: true, 
                message: "奖品可能已创建，但响应不完整", 
                id: Date.now() // 临时ID
              }
            });
          } else {
            // 其他错误正常拒绝
            reject(error);
          }
        });
    });
  },
  
  // 更新奖品
  updateRewardItem: (itemId, itemData) => {
    const client = getApiClient();
    return client.put(`/rewards/items/${itemId}`, itemData);
  },

  // 删除奖品
  deleteRewardItem: (itemId) => {
    const client = getApiClient();
    return client.delete(`/rewards/items/${itemId}`);
  },

  // 抽奖
  drawReward: (poolId) => {
    const client = getApiClient();
    return client.post(`/rewards/pools/${poolId}/draw`);
  },

  // 获取惩罚奖品池
  getPenaltyPools: () => {
    const client = getApiClient();
    return client.get('/rewards/pools/penalties');
  },

  // 获取可用奖品池
  getAvailablePools: () => {
    const client = getApiClient();
    return client.get('/rewards/pools/available');
  },

  // 抽奖
  drawPenalty: (poolId) => {
    const client = getApiClient();
    return client.post(`/rewards/pools/${poolId}/draw-penalty`);
  }
};

// 系统配置相关API
export const configApi = {
  // 获取所有配置
  getAllConfigs: () => {
    const client = getApiClient();
    return client.get('/configs');
  },

  // 获取配置详情
  getConfig: (key) => {
    const client = getApiClient();
    return client.get(`/configs/${key}`);
  },

  // 创建配置
  createConfig: (configData) => {
    const client = getApiClient();
    return client.post('/configs', configData);
  },

  // 更新配置
  updateConfig: (key, value, description) => {
    const client = getApiClient();
    return client.put(`/configs/${key}`, { value, description });
  },

  // 删除配置
  deleteConfig: (key) => {
    const client = getApiClient();
    return client.delete(`/configs/${key}`);
  }
};

// 小孩端API
export const childApi = {
  // 获取今日任务
  getTodayTasks: () => {
    const client = getApiClient();
    return client.get('/tasks/today');
  },
  
  // 获取过期任务
  getOverdueTasks: () => {
    const client = getApiClient();
    return client.get('/tasks/overdue');
  },

  // 获取任务详情（包含完整description字段）
  getTaskById: (taskId) => {
    const client = getApiClient();
    return client.get(`/tasks/${taskId}`);
  },

  // 获取过去的任务（昨天以前的最近7天任务）
  getPastTasks: () => {
    const client = getApiClient();
    return client.get('/tasks/past');
  },

  // 获取昨日任务（所有状态）
  getYesterdayTasks: () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0]; // YYYY-MM-DD格式
    const client = getApiClient();
    return client.get(`/tasks/date/${yesterdayStr}`);
  },
  
  // 开始任务
  startTask: (taskId) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/start`);
  },

  // 完成任务
  completeTask: (taskId) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/complete`);
  },
  
  // 获取可用奖品池
  getAvailableRewardPools: () => {
    const client = getApiClient();
    return client.get('/rewards/pools/available');
  },

  // 抽奖
  drawReward: (poolId) => {
    const client = getApiClient();
    return client.post(`/rewards/pools/${poolId}/draw`);
  },
  
  // 获取积分余额
  getPointBalance: () => {
    const client = getApiClient();
    return client.get('/points/total');
  },
  
  // 获取今日积分变化（使用后端的today接口）
  getTodayPointChanges: () => {
    console.log('获取今日积分变化...');
    const client = getApiClient();
    return client.get('/points/today')
      .then(response => {
        console.log('今日积分变化API返回的原始数据:', response.data);
        // 将后端返回的格式转换为前端期望的格式
        const todayChange = response.data.todayChange || 0;
        return {
          data: [{ pointChange: todayChange, description: '今日积分变化' }]
        };
      })
      .catch(error => {
        console.error('获取今日积分变化失败:', error);
        return { data: [] };
      });
  },
  
  // 获取近期积分变动
  getRecentPointChanges: (days = 7) => {
    try {
      console.log(`获取最近${days}天的积分记录`);
      
      // 使用当前真实系统时间，不进行年份修正
      const now = new Date();
      const endTime = now;
      const startTime = new Date(now);
      startTime.setDate(startTime.getDate() - days);
      
      // 确保日期格式正确
      const formattedStartTime = startTime.toISOString();
      const formattedEndTime = endTime.toISOString();
      
      console.log(`请求时间范围: 从 ${formattedStartTime} 到 ${formattedEndTime}`);
      console.log(`当前系统时间: ${new Date()}`);
      
      const client = getApiClient();
      return client.get('/points/records', {
        params: {
          startTime: formattedStartTime,
          endTime: formattedEndTime
        }
      }).then(response => {
        console.log('积分记录API返回的原始数据:', response.data);
        return response;
      }).catch(error => {
        console.error('获取积分记录失败:', error);
        // 返回空数组，避免UI错误
        return { data: [] };
      });
    } catch (error) {
      console.error('构建积分记录请求失败:', error);
      return Promise.resolve({ data: [] });
    }
  }
};

export default {
  taskApi,
  pointApi,
  rewardApi,
  configApi,
  childApi
}; 