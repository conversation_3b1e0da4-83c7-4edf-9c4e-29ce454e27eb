@echo off
echo 正在构建HashRouter版本（适用于无法配置nginx的情况）...
echo.

REM 设置环境变量使用HashRouter
set NODE_ENV=production
set VITE_USE_BROWSER_ROUTER=false

REM 执行构建
npm run build:prod

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ HashRouter版本构建成功！
    echo.
    echo 📁 构建文件位置: dist/
    echo 🌐 API地址配置: http://*************:18080/api
    echo 🔗 路由模式: HashRouter (使用 # 路由)
    echo.
    echo 📦 创建HashRouter部署压缩包...
    
    REM 创建部署压缩包
    if exist "crs-frontend-deploy.zip" del "crs-frontend-deploy.zip"
    powershell "Compress-Archive -Path 'dist\*' -DestinationPath 'crs-frontend-deploy.zip'"
    
    if exist "crs-frontend-deploy.zip" (
        echo ✅ HashRouter部署压缩包创建成功: crs-frontend-deploy.zip
        echo.
        echo 🚀 部署说明:
        echo 1. 将 crs-frontend-deploy.zip 上传到服务器
        echo 2. 解压到网站根目录
        echo 3. 访问地址: http://your-server/
        echo 4. 路由示例: http://your-server/#/child/rewards
        echo 5. 无需特殊nginx配置，直接部署即可
        echo.
        echo 📋 HashRouter特点:
        echo - URL中包含 # 符号
        echo - 不需要服务器端路由配置
        echo - 兼容性更好，适合简单部署
    ) else (
        echo ❌ 压缩包创建失败
    )
) else (
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause