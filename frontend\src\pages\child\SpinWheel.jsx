import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { childTheme } from '../../utils/themes';
import { childApi, rewardApi } from '../../api/apiService';

// 不再使用mock数据，所有奖品池数据从API获取

// 翻牌抽奖组件
const CardGame = ({ items, onSelect = () => {}, isPenalty = false }) => {
  const [cards, setCards] = useState([]);
  const [flippedIndex, setFlippedIndex] = useState(null);
  const [cardLocked, setCardLocked] = useState(false);
  
  // 初始化卡片
  useEffect(() => {
    // 确保有9张卡片，如果奖品不足，则复制
    let cardItems = [...items];
    while (cardItems.length < 9) {
      cardItems = [...cardItems, ...items.slice(0, 9 - cardItems.length)];
    }
    
    // 只取前9个奖品
    const cardData = cardItems.slice(0, 9).map((item, index) => ({
      ...item,
      index,
      flipped: false,
      winner: false
    }));
    
    // 随机选择一个中奖卡片
    const selectWinner = () => {
      const totalProbability = items.reduce((sum, item) => sum + item.probability, 0);
      const randomValue = Math.random() * totalProbability;
      
      let cumulativeProbability = 0;
      let winnerIndex = 0;
      
      for (let i = 0; i < items.length; i++) {
        cumulativeProbability += items[i].probability;
        if (randomValue <= cumulativeProbability) {
          winnerIndex = i % 9; // 确保索引在0-8范围内
          break;
        }
      }
      
      return winnerIndex;
    };
    
    const winnerIndex = selectWinner();
    
    // 设置中奖卡片
    cardData[winnerIndex].winner = true;
    
    setCards(cardData);
  }, [items]);
  
  const handleCardClick = (index) => {
    if (cardLocked || flippedIndex !== null) return;
    
    setCardLocked(true);
    setFlippedIndex(index);
    
    // 翻牌后通知父组件
    setTimeout(() => {
      onSelect(cards[index]);
    }, 1000); // 等待翻牌动画完成
  };
  
  return (
    <CardsContainer>
      {cards.map((card, index) => (
        <Card
          key={index}
          flipped={flippedIndex === index}
          isWinner={card.winner}
          onClick={() => handleCardClick(index)}
          initial={{ scale: 1 }}
          animate={{ 
            scale: [1, 1.03, 1],
            transition: { 
              repeat: Infinity, 
              duration: 2,
              repeatType: "reverse",
              ease: "easeInOut"
            }
          }}
          whileHover={{ 
            scale: 1.06,
            boxShadow: "0 10px 20px rgba(0, 0, 0, 0.25)",
            transition: { duration: 0.2 } 
          }}
          isPenalty={isPenalty}
        >
          <CardFront isPenalty={isPenalty}>
            <CardPattern>
              <PatternOverlay />
              <PatternSymbol>{isPenalty ? '⚠️' : '?'}</PatternSymbol>
            </CardPattern>
          </CardFront>
          
          <CardBack isPenalty={isPenalty}>
            <CardImage>{card.image}</CardImage>
            <CardName>{card.name}</CardName>
          </CardBack>
        </Card>
      ))}
    </CardsContainer>
  );
};

// 主组件
const CardRewards = ({ onPointsChange = () => {} }) => {
  const { poolId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  
  // 检查是否为惩罚池
  const isPenalty = location.search.includes('penalty=true');
  
  const [pool, setPool] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [pointsDeducted, setPointsDeducted] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // 获取奖品池数据
    const fetchPoolData = async () => {
      setLoading(true);
      try {
        // 获取奖品池详情
        const id = parseInt(poolId);
        const response = await rewardApi.getRewardPool(id);
        const poolData = response.data;
        
        // 格式化奖品池数据
        const formattedPool = {
          id: poolData.id,
          name: poolData.name,
          costPoints: poolData.costPoints,
          items: poolData.rewardItems ? poolData.rewardItems.map(item => ({
            id: item.id,
            name: item.name,
            probability: item.probability,
            stock: item.stock,
            image: item.imageUrl || '🎁' // 默认为礼物表情
          })) : []
        };
        
        setPool(formattedPool);
      } catch (error) {
        console.error('获取奖品池数据失败:', error);
        // 错误处理 - 返回奖励中心
        navigate('/child/rewards');
      } finally {
        setLoading(false);
      }
    };

    fetchPoolData();
  }, [poolId, navigate]);

  useEffect(() => {
    // 当奖品池数据加载完成后，如果有costPoints，则扣除积分
    if (pool && pool.costPoints > 0 && !pointsDeducted) {
      const deductPoints = async () => {
        try {
          // 调用抽奖API（此API会扣除积分）
          await childApi.drawReward(pool.id);
          
          // 更新积分变化
          onPointsChange(-pool.costPoints);
          setPointsDeducted(true);
        } catch (error) {
          console.error('扣除积分失败:', error);
          // 如果扣除失败，返回奖励中心
          navigate('/child/rewards');
        }
      };
      
      deductPoints();
    }
  }, [pool, pointsDeducted, onPointsChange, navigate]);

  const handleCardSelect = (card) => {
    setSelectedCard(card);
    setShowResult(true);
    
    // 奖励和惩罚池都需要在这里更新UI
    if (isPenalty && card.winner) {
      // 惩罚池中的"奖励"实际是惩罚
      onPointsChange(-(Math.abs(pool?.costPoints || 10)));
    }
  };
  
  const handleBackToRewards = () => {
    navigate('/child/rewards');
  };
  
  const handleTryAgain = () => {
    // 重新抽奖 - 这将回到奖励中心
    navigate('/child/rewards');
  };
  
  if (!pool) {
    return <LoadingContainer>加载中...</LoadingContainer>;
  }
  
  return (
    <Container
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <BackgroundGradient isPenalty={isPenalty} />
      <BackgroundStars />
      
      <Header>
        <HeaderBackButton onClick={handleBackToRewards}>
          <ArrowIcon>←</ArrowIcon>
          返回
        </HeaderBackButton>
        
        <HeaderContent>
          <PoolTitle>{pool.name}</PoolTitle>
          <PoolInfo>
            {isPenalty ? (
              <>扣除 <PenaltyHighlight>{Math.abs(pool.costPoints)}</PenaltyHighlight> 积分</>
            ) : (
              <>消耗 <PointsHighlight>{pool.costPoints}</PointsHighlight> 积分</>
            )}
            | 选择一张卡片揭晓{isPenalty ? '惩罚' : '奖品'}
          </PoolInfo>
        </HeaderContent>
        
        <div style={{ width: '50px' }}></div> {/* 平衡布局 */}
      </Header>
      
      <GameContainer>
        <CardGame 
          items={pool.items}
          onSelect={handleCardSelect}
          isPenalty={isPenalty}
        />
        
        <GameInstruction>
          <InstructionIcon>👆</InstructionIcon>
          <InstructionText>选择任意一张卡片，揭晓你的{isPenalty ? '惩罚' : '奖品'}</InstructionText>
        </GameInstruction>
      </GameContainer>
      
      <AnimatePresence>
        {showResult && selectedCard && (
          <ResultOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowResult(false)}
          >
            <ResultCard
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              transition={{ 
                type: 'spring', 
                stiffness: 400, 
                damping: 25,
                delay: 0.2 
              }}
              onClick={(e) => e.stopPropagation()}
              isPenalty={isPenalty}
            >
              <Confetti isPenalty={isPenalty} />
              <CloseButton onClick={() => setShowResult(false)}>×</CloseButton>
              
              <ResultTitle isPenalty={isPenalty}>
                <TitleDecoration>{isPenalty ? '⚠️' : '★'}</TitleDecoration>
                {isPenalty ? '接受惩罚' : '恭喜获得'}
                <TitleDecoration>{isPenalty ? '⚠️' : '★'}</TitleDecoration>
              </ResultTitle>
              
              <PrizeDisplay>
                <PrizeSpotlight isPenalty={isPenalty} />
                <PrizeImage>{selectedCard.image}</PrizeImage>
                <PrizeImageShadow />
              </PrizeDisplay>
              
              <PrizeName>{selectedCard.name}</PrizeName>
              
              <ResultButtons>
                <BackButton
                  onClick={handleBackToRewards}
                  whileHover={{ 
                    scale: 1.05, 
                    boxShadow: isPenalty 
                      ? "0 8px 20px rgba(255, 82, 82, 0.4)"
                      : "0 8px 20px rgba(76, 175, 80, 0.4)"
                  }}
                  whileTap={{ scale: 0.95 }}
                  isPenalty={isPenalty}
                >
                  返回{isPenalty ? '惩罚' : '奖励'}中心
                </BackButton>
                
                <TryAgainButton 
                  onClick={handleTryAgain}
                  whileHover={{ 
                    scale: 1.05, 
                    boxShadow: "0 8px 20px rgba(66, 133, 244, 0.4)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  再抽一次
                </TryAgainButton>
              </ResultButtons>
            </ResultCard>
          </ResultOverlay>
        )}
      </AnimatePresence>
    </Container>
  );
};

// 五彩纸屑效果
const Confetti = ({ isPenalty = false }) => {
  const [confetti, setConfetti] = useState([]);
  
  useEffect(() => {
    // 生成彩色纸屑
    const pieces = Array.from({ length: 60 }, () => {
      const color = isPenalty 
        ? `hsl(${0 + Math.random() * 30}, 80%, 60%)` // 红色系
        : `hsl(${Math.random() * 360}, 80%, 60%)`;   // 彩色
        
      return {
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        size: Math.random() * 10 + 5,
        background: color,
        animationDelay: `${Math.random() * 2}s`,
        animationDuration: `${Math.random() * 3 + 2}s`,
        rotation: Math.random() * 360,
        shape: Math.random() > 0.5 ? 'circle' : 'square'
      };
    });
    
    setConfetti(pieces);
  }, [isPenalty]);
  
  return (
    <ConfettiContainer>
      {confetti.map((piece, i) => (
        <ConfettiPiece
          key={i}
          style={{
            left: piece.left,
            top: piece.top,
            width: `${piece.size}px`,
            height: `${piece.size}px`,
            background: piece.background,
            animationDelay: piece.animationDelay,
            animationDuration: piece.animationDuration,
            transform: `rotate(${piece.rotation}deg)`,
            borderRadius: piece.shape === 'circle' ? '50%' : '2px'
          }}
        />
      ))}
    </ConfettiContainer>
  );
};

// 背景星星效果
const BackgroundStars = () => {
  const [stars, setStars] = useState([]);
  
  useEffect(() => {
    const newStars = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      size: Math.random() * 3 + 1,
      left: Math.random() * 100,
      top: Math.random() * 100,
      delay: Math.random() * 5,
      duration: Math.random() * 3 + 2
    }));
    
    setStars(newStars);
  }, []);
  
  return (
    <StarsContainer>
      {stars.map(star => (
        <Star 
          key={star.id}
          style={{
            width: `${star.size}px`,
            height: `${star.size}px`,
            left: `${star.left}%`,
            top: `${star.top}%`,
            animationDelay: `${star.delay}s`,
            animationDuration: `${star.duration}s`
          }}
        />
      ))}
    </StarsContainer>
  );
};

// 样式组件
const Container = styled(motion.div)`
  height: 100%;
  width: 100vw;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  left: 0;
  right: 0;
`;

const BackgroundGradient = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${props => props.isPenalty 
    ? 'linear-gradient(135deg, #2d1a20 0%, #541e2b 100%)'  // 暗红色背景
    : 'linear-gradient(135deg, #1a1e3c 0%, #2d3a66 100%)'}; // 原蓝色背景
  z-index: -1;
`;

const StarsContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
`;

const Star = styled.div`
  position: absolute;
  background-color: white;
  border-radius: 50%;
  animation: twinkle infinite ease-in-out alternate;
  
  @keyframes twinkle {
    0% { opacity: 0.2; }
    100% { opacity: 1; }
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  position: relative;
  z-index: 5;
`;

const HeaderBackButton = styled(motion.button)`
  background: transparent;
  border: none;
  color: #fff;
  font-weight: 600;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const ArrowIcon = styled.span`
  margin-right: 0.5rem;
  font-size: 1.2rem;
`;

const HeaderContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const PoolTitle = styled.h1`
  font-size: 1.8rem;
  margin-bottom: 0.3rem;
  color: white;
  text-align: center;
  font-weight: 600;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
`;

const PoolInfo = styled.div`
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 4px;
`;

const PointsHighlight = styled.span`
  font-weight: 700;
  color: #ffca28;
  font-size: 1.2rem;
`;

const PenaltyHighlight = styled.span`
  font-weight: 700;
  color: #ff5252;
  font-size: 1.2rem;
`;

const GameContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
  position: relative;
  z-index: 2;
`;

const CardsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 15px;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
`;

const Card = styled(motion.div)`
  position: relative;
  width: 100%;
  aspect-ratio: 1 / 1; /* 使用纵横比代替padding-bottom */
  cursor: pointer;
  perspective: 1000px;
  transform-style: preserve-3d;
  transform: scale(0.8);
  
  /* 3D翻转效果 */
  & > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    transition: transform 0.8s cubic-bezier(.3,1,.6,1);
  }
`;

const CardFront = styled.div`
  background: ${props => props.isPenalty
    ? 'linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%)'  // 红色渐变
    : 'linear-gradient(135deg, #7e57c2 0%, #5e35b1 100%)'}; // 紫色渐变
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transform: ${props => props.flipped ? "rotateY(180deg)" : "rotateY(0)"};
  
  ${Card}:hover & {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
  }
  
  ${Card}[flipped="true"] & {
    transform: rotateY(180deg);
  }
`;

const CardPattern = styled.div`
  width: 85%;
  height: 85%;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const PatternOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 10% 10%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 90% 90%, rgba(255,255,255,0.1) 0%, transparent 50%);
  border-radius: 8px;
`;

const PatternSymbol = styled.div`
  font-size: 3rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.5);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
`;

const CardBack = styled.div`
  background: ${props => props.isPenalty
    ? 'linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%)'  // 浅红色
    : 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)'}; // 浅灰色
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transform: rotateY(-180deg);
  padding: 10px;
  
  ${Card}[flipped="true"] & {
    transform: rotateY(0);
  }
`;

const CardImage = styled.div`
  font-size: 3.5rem;
  margin-bottom: 10px;
  filter: drop-shadow(0 2px 5px rgba(0,0,0,0.2));
`;

const CardName = styled.div`
  font-size: 1rem;
  text-align: center;
  font-weight: 600;
  color: #333;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 12px;
`;

const GameInstruction = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 2rem;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
`;

const InstructionIcon = styled.div`
  font-size: 1.5rem;
  animation: point-up 2s infinite;
  
  @keyframes point-up {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
  }
`;

const InstructionText = styled.div`
  color: white;
  font-size: 1rem;
`;

const ResultOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(8px);
`;

const ResultCard = styled(motion.div)`
  background: ${props => props.isPenalty
    ? 'linear-gradient(145deg, #ffebee, #ffcdd2)'  // 浅红色
    : 'linear-gradient(145deg, #ffffff, #f9f9f9)'}; // 白色
  border-radius: 24px;
  padding: 2rem 2rem 2.5rem;
  width: 85%;
  max-width: 400px;
  text-align: center;
  box-shadow: ${props => props.isPenalty
    ? '0 20px 60px rgba(255,82,82,0.3), 0 10px 30px rgba(255,82,82,0.2)'
    : '0 20px 60px rgba(0,0,0,0.3), 0 10px 30px rgba(0,0,0,0.2)'};
  position: relative;
  overflow: hidden;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0,0,0,0.1);
  border: none;
  color: #555;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(0,0,0,0.2);
  }
  
  &:focus {
    outline: none;
  }
`;

const ResultTitle = styled.h2`
  font-size: 1.8rem;
  color: ${props => props.isPenalty ? '#d32f2f' : '#5e35b1'};
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
`;

const TitleDecoration = styled.span`
  color: gold;
  animation: spin 3s linear infinite;
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const PrizeDisplay = styled.div`
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const PrizeSpotlight = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: ${props => props.isPenalty
    ? 'radial-gradient(circle, rgba(255, 82, 82, 0.2) 0%, rgba(255, 82, 82, 0) 70%)'
    : 'radial-gradient(circle, rgba(94, 53, 177, 0.2) 0%, rgba(94, 53, 177, 0) 70%)'};
  animation: pulse-light 2s infinite alternate;
  
  @keyframes pulse-light {
    0% { transform: scale(1); opacity: 0.5; }
    100% { transform: scale(1.2); opacity: 0.2; }
  }
`;

const PrizeImage = styled.div`
  font-size: 6rem;
  position: relative;
  z-index: 5;
  animation: hover-bounce 3s ease-in-out infinite;
  
  @keyframes hover-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
  }
`;

const PrizeImageShadow = styled.div`
  position: absolute;
  bottom: 0;
  width: 60px;
  height: 10px;
  background: rgba(0,0,0,0.2);
  border-radius: 50%;
  filter: blur(5px);
  animation: shadow-pulse 3s ease-in-out infinite;
  
  @keyframes shadow-pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(0.8); opacity: 0.2; }
  }
`;

const PrizeName = styled.h3`
  font-size: 1.6rem;
  color: #5e35b1;
  margin-bottom: 2rem;
`;

const ResultButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
`;

const BackButton = styled(motion.button)`
  padding: 0.9rem 1.5rem;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  background: ${props => props.isPenalty
    ? 'linear-gradient(145deg, #ff5252, #d32f2f)'
    : 'linear-gradient(145deg, #4CAF50, #2E7D32)'};
  color: white;
  box-shadow: ${props => props.isPenalty
    ? '0 8px 20px rgba(255, 82, 82, 0.3)'
    : '0 8px 20px rgba(76, 175, 80, 0.3)'};
  flex: 1;
  max-width: 170px;
  
  &:focus {
    outline: none;
  }
`;

const TryAgainButton = styled(motion.button)`
  padding: 0.9rem 1.5rem;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  background: linear-gradient(145deg, #7e57c2, #5e35b1);
  color: white;
  box-shadow: 0 8px 20px rgba(94, 53, 177, 0.3);
  flex: 1;
  max-width: 170px;
  
  &:focus {
    outline: none;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2rem;
  color: #555;
`;

const ConfettiContainer = styled.div`
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  overflow: hidden;
  pointer-events: none;
`;

const ConfettiPiece = styled.div`
  position: absolute;
  animation: confettiFall 4s linear forwards;
  
  @keyframes confettiFall {
    0% {
      transform: translateY(-100px) rotate(0deg);
      opacity: 1;
    }
    70% {
      opacity: 1;
    }
    100% {
      transform: translateY(800px) rotate(720deg);
      opacity: 0;
    }
  }
`;

// 更改导出组件名称以反映新设计
export default CardRewards; 