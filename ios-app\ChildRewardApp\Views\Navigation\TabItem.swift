import SwiftUI

// 导航标签项枚举
enum TabItem: String, CaseIterable {
    case tasks = "tasks"
    case rewards = "rewards"
    case exchange = "exchange"
    case points = "points"
    
    var title: String {
        switch self {
        case .tasks: return "任务"
        case .rewards: return "奖励"
        case .exchange: return "兑换"
        case .points: return "积分"
        }
    }
    
    var icon: String {
        switch self {
        case .tasks: return "📝"
        case .rewards: return "🎁"
        case .exchange: return "🛒"
        case .points: return "⭐"
        }
    }
    
    var systemIcon: String {
        switch self {
        case .tasks: return "list.clipboard"
        case .rewards: return "gift"
        case .exchange: return "cart"
        case .points: return "star"
        }
    }
    
    var selectedSystemIcon: String {
        switch self {
        case .tasks: return "list.clipboard.fill"
        case .rewards: return "gift.fill"
        case .exchange: return "cart.fill"
        case .points: return "star.fill"
        }
    }
    
    var gradientColors: [Color] {
        switch self {
        case .tasks:
            return [DesignSystem.Colors.primary, DesignSystem.Colors.primaryLight]
        case .rewards:
            return [DesignSystem.Colors.secondary, DesignSystem.Colors.secondaryLight]
        case .exchange:
            return [Color.purple, Color.pink]
        case .points:
            return [DesignSystem.Colors.warning, DesignSystem.Colors.warningLight]
        }
    }
}

// 导航标签视图
struct TabItemView: View {
    let item: TabItem
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Spacing.xs) {
                // 图标容器
                ZStack {
                    // 背景圆圈
                    Circle()
                        .fill(
                            isSelected ? 
                            LinearGradient(
                                colors: item.gradientColors,
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ) :
                            LinearGradient(
                                colors: [DesignSystem.Colors.surfaceSecondary],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 56, height: 56)
                        .shadow(
                            color: isSelected ? item.gradientColors[0].opacity(0.3) : Color.clear,
                            radius: isSelected ? 8 : 0,
                            x: 0,
                            y: isSelected ? 4 : 0
                        )
                    
                    // 图标
                    Image(systemName: isSelected ? item.selectedSystemIcon : item.systemIcon)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(
                            isSelected ? .white : DesignSystem.Colors.textSecondary
                        )
                }
                .scaleEffect(isSelected ? 1.1 : 1.0)
                .animation(DesignSystem.Animation.spring, value: isSelected)
                
                // 标签文字
                Text(item.title)
                    .font(DesignSystem.Typography.caption1)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(
                        isSelected ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary
                    )
                    .animation(DesignSystem.Animation.quick, value: isSelected)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 导航栏视图
struct NavigationTabBar: View {
    @Binding var selectedTab: TabItem
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(TabItem.allCases, id: \.self) { tab in
                TabItemView(
                    item: tab,
                    isSelected: selectedTab == tab
                ) {
                    withAnimation(DesignSystem.Animation.spring) {
                        selectedTab = tab
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(
            // 毛玻璃背景
            RoundedRectangle(cornerRadius: 0)
                .fill(DesignSystem.Colors.glassBackground)
                .background(
                    // 顶部分割线
                    Rectangle()
                        .fill(DesignSystem.Colors.separator.opacity(0.3))
                        .frame(height: 0.5)
                        .offset(y: -DesignSystem.Spacing.md)
                )
        )
        .background(.ultraThinMaterial)
    }
}

// 预览
struct NavigationTabBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            Spacer()
            NavigationTabBar(selectedTab: .constant(.tasks))
        }
        .background(DesignSystem.Colors.background)
        .previewDevice("iPad Pro (12.9-inch) (6th generation)")
    }
}
