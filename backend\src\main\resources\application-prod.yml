# 生产环境配置
spring:
  application:
    name: child-reward-system

  # 数据库连接配置 - 支持环境变量覆盖
  datasource:
    url: ${SPRING_DATASOURCE_URL:**********************************************************************************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:root}
    password: ${SPRING_DATASOURCE_PASSWORD:123456}
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

  # SQL初始化配置（生产环境禁用）
  sql:
    init:
      mode: never

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized

# 日志配置
logging:
  level:
    org:
      springframework: INFO
      hibernate: WARN
    com:
      example:
        childreward: INFO
  file:
    name: /app/logs/application.log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30
