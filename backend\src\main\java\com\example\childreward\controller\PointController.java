package com.example.childreward.controller;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.service.PointService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import org.springframework.http.HttpStatus;
import java.util.Random;

@RestController
@CrossOrigin(origins = "*") // 允许所有来源跨域请求
@RequestMapping("/api/points")
@RequiredArgsConstructor
public class PointController {

    private final PointService pointService;

    @GetMapping("/total")
    public ResponseEntity<Map<String, Integer>> getTotalPoints() {
        return ResponseEntity.ok(Map.of("totalPoints", pointService.getTotalPoints()));
    }

    @GetMapping("/today")
    public ResponseEntity<Map<String, Integer>> getTodayPointsChange() {
        return ResponseEntity.ok(Map.of("todayChange", pointService.getTodayPointsChange()));
    }

    @GetMapping("/date/{date}")
    public ResponseEntity<Map<String, Integer>> getPointsChangeByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return ResponseEntity.ok(Map.of("dateChange", pointService.getPointsChangeByDate(date)));
    }

    @GetMapping("/records")
    public ResponseEntity<List<PointRecord>> getPointRecords(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        // 打印原始请求参数
        System.out.println("积分记录查询 - 原始参数: startTime=" + startTime + ", endTime=" + endTime);
        
        // 如果没有指定时间范围，默认查询最近30天
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(30);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }
        
        // 不再检查和修正年份范围，使用原始参数查询
        // 确保开始时间不晚于结束时间
        if (startTime.isAfter(endTime)) {
            System.out.println("警告: 开始时间晚于结束时间，交换时间参数");
            LocalDateTime temp = startTime;
            startTime = endTime;
            endTime = temp;
        }
        
        System.out.println("积分记录查询 - 执行查询参数: startTime=" + startTime + ", endTime=" + endTime);
        
        List<PointRecord> records = pointService.getPointRecords(startTime, endTime);
        System.out.println("查询结果记录数: " + (records != null ? records.size() : "null"));
        
        return ResponseEntity.ok(records);
    }

    /**
     * 简化版接口 - 直接获取最近N天的积分记录
     * @param days 天数，默认为7天
     * @return 积分记录列表
     */
    @GetMapping("/recent")
    public ResponseEntity<List<PointRecord>> getRecentPointRecords(
            @RequestParam(required = false, defaultValue = "7") Integer days) {
        
        System.out.println("===== 获取最近" + days + "天积分记录 =====");
        
        // 计算开始和结束时间
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);
        
        System.out.println("查询时间范围: " + startTime + " 到 " + endTime);
        
        // 直接从数据库获取记录
        List<PointRecord> records = pointService.getPointRecords(startTime, endTime);
        
        System.out.println("查询结果记录数: " + (records != null ? records.size() : "null"));
        if (records != null && !records.isEmpty()) {
            System.out.println("第一条记录: " + records.get(0));
        }
        
        return ResponseEntity.ok(records);
    }

    @PostMapping("/add")
    public ResponseEntity<PointRecord> addPoints(
            @RequestParam Integer points,
            @RequestParam String description) {
        return ResponseEntity.ok(pointService.addPoints(points, PointRecord.ChangeType.MANUAL_ADJUST, description, null));
    }

    @PostMapping("/deduct")
    public ResponseEntity<PointRecord> deductPoints(
            @RequestParam Integer points,
            @RequestParam String description) {
        return ResponseEntity.ok(pointService.deductPoints(points, PointRecord.ChangeType.MANUAL_ADJUST, description, null));
    }

    @PostMapping("/deduct/punishment")
    public ResponseEntity<PointRecord> deductPointsForPunishment(
            @RequestParam Integer points,
            @RequestParam String description) {
        return ResponseEntity.ok(pointService.deductPoints(points, PointRecord.ChangeType.TASK_PENALTY, description, null));
    }

    @GetMapping("/debug/all-records")
    public ResponseEntity<?> getAllPointRecords() {
        // 生产环境禁用此接口
        String profile = System.getProperty("spring.profiles.active", "default");
        if ("production".equals(profile)) {
            return ResponseEntity.status(403).body(Map.of("error", "调试接口在生产环境中不可用"));
        }

        try {
            System.out.println("===== 积分记录调试信息 =====");
            List<PointRecord> allRecords = pointService.getAllRecords();
            System.out.println("总记录数: " + (allRecords != null ? allRecords.size() : "null"));
            
            if (allRecords != null && !allRecords.isEmpty()) {
                System.out.println("最新记录: " + allRecords.get(0));
                // 输出所有记录信息用于调试
                for (PointRecord record : allRecords) {
                    System.out.println(String.format(
                        "记录ID: %d, 积分变化: %d, 类型: %s, 时间: %s, 描述: %s", 
                        record.getId(),
                        record.getPointChange(),
                        record.getChangeType(),
                        record.getRecordTime(),
                        record.getDescription()
                    ));
                }
            } else {
                System.out.println("没有找到任何积分记录!");
            }
            
            // 直接返回记录数组，与pointApi.getPointRecords方法返回格式保持一致
            return ResponseEntity.ok(allRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of(
                "error", e.getMessage(),
                "timestamp", LocalDateTime.now().toString()
            ));
        }
    }

    @PostMapping("/debug/create-test-data")
    public ResponseEntity<?> createTestData() {
        try {
            System.out.println("===== 创建测试积分记录 =====");
            List<PointRecord> createdRecords = new ArrayList<>();
            
            // 添加两条任务完成记录
            PointRecord taskRecord1 = pointService.addPoints(10, PointRecord.ChangeType.TASK_COMPLETION, "完成数学作业", 1L);
            createdRecords.add(taskRecord1);
            
            PointRecord taskRecord2 = pointService.addPoints(15, PointRecord.ChangeType.TASK_COMPLETION, "完成阅读任务", 2L);
            createdRecords.add(taskRecord2);
            
            // 添加一条奖品兑换记录
            PointRecord rewardRecord = pointService.deductPoints(5, PointRecord.ChangeType.REWARD_EXCHANGE, "兑换小零食", 1L);
            createdRecords.add(rewardRecord);
            
            // 添加一条手动调整记录
            PointRecord manualRecord = pointService.addPoints(8, PointRecord.ChangeType.MANUAL_ADJUST, "表现良好额外奖励", null);
            createdRecords.add(manualRecord);
            
            return ResponseEntity.ok(Map.of(
                "message", "成功创建测试数据",
                "recordsCreated", createdRecords.size(),
                "records", createdRecords,
                "timestamp", LocalDateTime.now().toString()
            ));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of(
                "error", e.getMessage(),
                "timestamp", LocalDateTime.now().toString()
            ));
        }
    }

    @PostMapping("/debug/create-historical-data")
    public ResponseEntity<?> createHistoricalData() {
        try {
            System.out.println("===== 创建历史积分记录 =====");
            List<PointRecord> createdRecords = new ArrayList<>();
            
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            
            // 创建过去30天的随机数据
            Random random = new Random();
            for (int i = 30; i >= 0; i--) {
                // 计算日期：当前日期减去i天
                LocalDateTime recordDate = now.minusDays(i);
                
                // 50%概率创建积分增加记录
                if (random.nextBoolean()) {
                    int points = 5 + random.nextInt(20); // 5-24点积分
                    PointRecord.ChangeType type = random.nextBoolean() ? 
                        PointRecord.ChangeType.TASK_COMPLETION : 
                        PointRecord.ChangeType.MANUAL_ADJUST;
                    
                    String description = type == PointRecord.ChangeType.TASK_COMPLETION ? 
                        "完成任务" + (i + 1) : 
                        "良好表现奖励";
                        
                    // 创建记录并手动设置日期
                    PointRecord record = PointRecord.builder()
                        .pointChange(points)
                        .changeType(type)
                        .description(description)
                        .recordTime(recordDate)
                        .build();
                    
                    // 保存记录
                    PointRecord saved = pointService.saveRecord(record);
                    createdRecords.add(saved);
                }
                
                // 30%概率创建积分消费记录
                if (random.nextFloat() < 0.3) {
                    int points = 2 + random.nextInt(10); // 2-11点积分
                    
                    // 创建记录并手动设置日期
                    PointRecord record = PointRecord.builder()
                        .pointChange(-points)
                        .changeType(PointRecord.ChangeType.REWARD_EXCHANGE)
                        .description("兑换奖品" + (i + 1))
                        .recordTime(recordDate.plusHours(random.nextInt(5))) // 加一些随机时间
                        .build();
                    
                    // 保存记录
                    PointRecord saved = pointService.saveRecord(record);
                    createdRecords.add(saved);
                }
            }
            
            return ResponseEntity.ok(Map.of(
                "message", "成功创建历史积分记录",
                "recordsCreated", createdRecords.size(),
                "timestamp", LocalDateTime.now().toString()
            ));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of(
                "error", e.getMessage(),
                "timestamp", LocalDateTime.now().toString()
            ));
        }
    }

    @PostMapping("/debug/fix-dates")
    public ResponseEntity<?> fixPointRecordDates() {
        try {
            System.out.println("===== 开始修复积分记录日期 =====");
            List<PointRecord> allRecords = pointService.getAllRecords();
            int fixedCount = 0;
            
            for (PointRecord record : allRecords) {
                LocalDateTime recordTime = record.getRecordTime();
                if (recordTime != null && recordTime.getYear() > 2024) {
                    // 将未来日期调整为当前年份，保持月日时分秒不变
                    LocalDateTime fixedTime = LocalDateTime.of(
                        LocalDateTime.now().getYear(), 
                        recordTime.getMonthValue(),
                        recordTime.getDayOfMonth(),
                        recordTime.getHour(),
                        recordTime.getMinute(),
                        recordTime.getSecond()
                    );
                    
                    record.setRecordTime(fixedTime);
                    pointService.saveRecord(record);
                    fixedCount++;
                    System.out.println("已修复记录ID " + record.getId() + ": " + recordTime + " -> " + fixedTime);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "成功修复 " + fixedCount + " 条积分记录日期");
            result.put("fixedCount", fixedCount);
            result.put("totalCount", allRecords.size());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "修复积分记录日期失败: " + e.getMessage());
            System.err.println("修复积分记录日期失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @GetMapping("/debug/diagnose")
    public ResponseEntity<?> diagnoseSystem() {
        Map<String, Object> result = new HashMap<>();
        result.put("timestamp", LocalDateTime.now().toString());
        result.put("serverStatus", "运行正常");
        
        try {
            System.out.println("===== 系统诊断开始 =====");
            
            // 1. 检查数据库连接
            System.out.println("1. 检查数据库连接");
            boolean dbConnected = false;
            String dbError = null;
            
            try {
                List<PointRecord> checkRecords = pointService.getAllRecords();
                dbConnected = true;
                result.put("databaseConnected", true);
                result.put("recordCount", checkRecords != null ? checkRecords.size() : 0);
                System.out.println("数据库连接正常，记录数: " + (checkRecords != null ? checkRecords.size() : "null"));
                
                // 2. 查看部分记录的内容
                if (checkRecords != null && !checkRecords.isEmpty()) {
                    PointRecord sample = checkRecords.get(0);
                    Map<String, Object> sampleData = new HashMap<>();
                    sampleData.put("id", sample.getId());
                    sampleData.put("pointChange", sample.getPointChange());
                    sampleData.put("changeType", sample.getChangeType());
                    sampleData.put("recordTime", sample.getRecordTime());
                    sampleData.put("description", sample.getDescription());
                    
                    result.put("sampleRecord", sampleData);
                    System.out.println("示例记录: " + sample);
                } else {
                    result.put("sampleRecord", null);
                    System.out.println("没有可用的记录");
                }
                
            } catch (Exception e) {
                dbConnected = false;
                dbError = e.getMessage();
                result.put("databaseConnected", false);
                result.put("databaseError", dbError);
                System.err.println("数据库连接失败: " + e.getMessage());
                e.printStackTrace();
            }
            
            // 3. 检查API访问
            System.out.println("3. 检查API访问权限");
            result.put("apiAccessible", true);
            result.put("apiEndpoint", "/api/points/debug/diagnose");
            
            System.out.println("===== 系统诊断完成 =====");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("error", e.getMessage());
            System.err.println("系统诊断过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(result);
        }
    }
} 