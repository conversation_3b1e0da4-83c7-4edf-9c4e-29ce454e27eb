# <font style="color:rgb(64, 64, 64);">儿童行为激励系统需求文档</font>
## <font style="color:rgb(64, 64, 64);">1. 系统概述</font>
<font style="color:rgb(64, 64, 64);">开发一个基于H5的儿童行为激励系统，通过积分奖惩机制培养孩子良好习惯。系统包含儿童端（iPad横屏）和家长端（手机竖屏），聚焦核心赏罚功能。</font>

## <font style="color:rgb(64, 64, 64);">2. 核心功能需求</font>
### <font style="color:rgb(64, 64, 64);">2.1 任务管理系统</font>
#### <font style="color:rgb(64, 64, 64);">任务生命周期流程：</font>
<font style="color:rgb(82, 82, 82);background-color:rgb(250, 250, 250);">plantuml</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">复制</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">下载</font>

```plain
@startuml
|儿童|
start
:查看当日任务;
:点击【开始任务】;
|系统|
:记录开始时间;
|儿童|
:完成任务;
:点击【完成】;
|系统|
:记录结束时间;
:计算实际耗时;
:生成审批请求;
|家长|
:审批任务;
if (超时完成?) then (是)
  :按折扣计算积分;
else (否)
  :按标准计算积分;
endif
:更新积分;
|儿童|
:查看积分变化;
|系统|
:每日凌晨检查任务;
if (未完成?) then (是)
  :标记❌;
  if (超过阈值?) then (是)
    :自动扣分;
  endif
endif
stop
@enduml
```

#### <font style="color:rgb(64, 64, 64);">任务规则：</font>
1. **<font style="color:rgb(64, 64, 64);">任务预设</font>**<font style="color:rgb(64, 64, 64);">：</font>
    - <font style="color:rgb(64, 64, 64);">家长可提前设置每日任务</font>
    - <font style="color:rgb(64, 64, 64);">包含：任务名称、预计时长、基础积分、截止时间</font>
2. **<font style="color:rgb(64, 64, 64);">时间监控</font>**<font style="color:rgb(64, 64, 64);">：</font>
    - <font style="color:rgb(64, 64, 64);">自动记录开始/结束时间</font>
    - <font style="color:rgb(64, 64, 64);">计算实际耗时 = 结束时间 - 开始时间</font>
3. **<font style="color:rgb(64, 64, 64);">超时处理</font>**<font style="color:rgb(64, 64, 64);">：</font>
    - <font style="color:rgb(64, 64, 64);">超时系数 = min(1, 预计时长/实际耗时)</font>
    - <font style="color:rgb(64, 64, 64);">实际积分 = 基础积分 × 超时系数</font>
4. **<font style="color:rgb(64, 64, 64);">逾期处理</font>**<font style="color:rgb(64, 64, 64);">：</font>
    - <font style="color:rgb(64, 64, 64);">当天24:00未完成 → 标记</font><font style="color:rgb(64, 64, 64);">❌</font>
    - <font style="color:rgb(64, 64, 64);">第二天补做 → 无积分</font>
5. **<font style="color:rgb(64, 64, 64);">惩罚机制</font>**<font style="color:rgb(64, 64, 64);">：</font>
    - <font style="color:rgb(64, 64, 64);">每日未完成任务 > 设定阈值 → 自动扣分</font>
    - <font style="color:rgb(64, 64, 64);">扣分值由家长设置</font>

### <font style="color:rgb(64, 64, 64);">2.2 积分系统</font>
#### <font style="color:rgb(64, 64, 64);">积分计算逻辑：</font>
<font style="color:rgb(82, 82, 82);background-color:rgb(250, 250, 250);">plantuml</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">复制</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">下载</font>

```plain
@startuml
skinparam monochrome true
start
:获取任务基础积分;
:计算实际耗时;
if (实际耗时 <= 预计时长) then (是)
  :积分 = 基础积分;
else (否)
  if (实际耗时 <= 1.5*预计时长) then (是)
    :积分 = 基础积分 * 0.8;
  else (否)
    :积分 = 基础积分 * 0.5;
  endif
endif
:记录积分变更;
stop
@enduml
```

### <font style="color:rgb(64, 64, 64);">2.3 奖励系统</font>
#### <font style="color:rgb(64, 64, 64);">奖品池管理：</font>
<font style="color:rgb(82, 82, 82);background-color:rgb(250, 250, 250);">plantuml</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">复制</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">下载</font>

```plain
@startuml
class 奖品池 {
  + 名称
  + 消耗积分
  + 启用状态
}

class 奖品项 {
  + 名称
  + 图片
  + 库存
  + 中奖概率
}

奖品池 "1" *-- "n" 奖品项
@enduml
```

#### <font style="color:rgb(64, 64, 64);">抽奖流程：</font>
1. <font style="color:rgb(64, 64, 64);">儿童选择奖品池（不同消耗积分）</font>
2. <font style="color:rgb(64, 64, 64);">系统随机抽取奖品</font>
3. <font style="color:rgb(64, 64, 64);">显示3D抽奖动画</font>
4. <font style="color:rgb(64, 64, 64);">扣除对应积分</font>
5. <font style="color:rgb(64, 64, 64);">减少奖品库存</font>

### <font style="color:rgb(64, 64, 64);">2.4 家长控制功能</font>
#### <font style="color:rgb(64, 64, 64);">家长端功能矩阵：</font>
| **<font style="color:rgb(64, 64, 64);">功能模块</font>** | **<font style="color:rgb(64, 64, 64);">核心操作</font>** | **<font style="color:rgb(64, 64, 64);">特别提醒</font>** |
| --- | --- | --- |
| <font style="color:rgb(64, 64, 64);">任务管理</font> | <font style="color:rgb(64, 64, 64);">添加/编辑每日任务</font> | <font style="color:rgb(64, 64, 64);">逾期任务红色警报</font> |
| <font style="color:rgb(64, 64, 64);">审批中心</font> | <font style="color:rgb(64, 64, 64);">批量审批任务</font> | <font style="color:rgb(64, 64, 64);">超时任务黄色警示</font> |
| <font style="color:rgb(64, 64, 64);">奖品管理</font> | <font style="color:rgb(64, 64, 64);">多奖品池配置</font> | <font style="color:rgb(64, 64, 64);">库存不足提醒</font> |
| <font style="color:rgb(64, 64, 64);">惩罚设置</font> | <font style="color:rgb(64, 64, 64);">阈值/扣分设置</font> | <font style="color:rgb(64, 64, 64);">高频惩罚预警</font> |
| <font style="color:rgb(64, 64, 64);">数据视图</font> | <font style="color:rgb(64, 64, 64);">任务完成率统计</font> | <font style="color:rgb(64, 64, 64);">图表可视化</font> |


## <font style="color:rgb(64, 64, 64);">3. 界面设计规范</font>
### <font style="color:rgb(64, 64, 64);">3.1 儿童端（iPad横屏）</font>
| **<font style="color:rgb(64, 64, 64);">区域</font>** | **<font style="color:rgb(64, 64, 64);">组件</font>** | **<font style="color:rgb(64, 64, 64);">状态指示</font>** |
| --- | --- | --- |
| **<font style="color:rgb(64, 64, 64);">顶部状态栏</font>** | <font style="color:rgb(64, 64, 64);">当前积分</font> | <font style="color:rgb(64, 64, 64);">积分变化动画</font> |
| | <font style="color:rgb(64, 64, 64);">今日积分变化</font> | <font style="color:rgb(64, 64, 64);">浮动数字效果</font> |
| **<font style="color:rgb(64, 64, 64);">任务看板</font>** | <font style="color:rgb(64, 64, 64);">任务卡片</font> | <font style="color:rgb(64, 64, 64);">颜色编码：   </font><font style="color:rgb(64, 64, 64);">🟢</font><font style="color:rgb(64, 64, 64);"> 完成 </font><font style="color:rgb(64, 64, 64);">🟡</font><font style="color:rgb(64, 64, 64);"> 进行中   </font><font style="color:rgb(64, 64, 64);">🔴</font><font style="color:rgb(64, 64, 64);"> 未开始 </font><font style="color:rgb(64, 64, 64);">⚪</font><font style="color:rgb(64, 64, 64);"> 待审批</font> |
| | <font style="color:rgb(64, 64, 64);">开始/完成按钮</font> | <font style="color:rgb(64, 64, 64);">点击动画反馈</font> |
| | <font style="color:rgb(64, 64, 64);">任务计时器</font> | <font style="color:rgb(64, 64, 64);">超时红色闪烁</font> |
| **<font style="color:rgb(64, 64, 64);">奖励中心</font>** | <font style="color:rgb(64, 64, 64);">奖品兑换区</font> | <font style="color:rgb(64, 64, 64);">库存显示</font> |
| | <font style="color:rgb(64, 64, 64);">转盘抽奖入口</font> | <font style="color:rgb(64, 64, 64);">炫光动画效果</font> |


### <font style="color:rgb(64, 64, 64);">3.2 家长端（手机竖屏）</font>
| **<font style="color:rgb(64, 64, 64);">功能区</font>** | **<font style="color:rgb(64, 64, 64);">核心组件</font>** | **<font style="color:rgb(64, 64, 64);">交互特性</font>** |
| --- | --- | --- |
| **<font style="color:rgb(64, 64, 64);">今日概览</font>** | <font style="color:rgb(64, 64, 64);">任务完成率环状图</font> | <font style="color:rgb(64, 64, 64);">点击查看详情</font> |
| **<font style="color:rgb(64, 64, 64);">待审批区</font>** | <font style="color:rgb(64, 64, 64);">可折叠任务卡片</font> | <font style="color:rgb(64, 64, 64);">滑动审批操作</font> |
| **<font style="color:rgb(64, 64, 64);">预警面板</font>** | <font style="color:rgb(64, 64, 64);">超时任务列表</font> | <font style="color:rgb(64, 64, 64);">红黄颜色编码</font> |
| **<font style="color:rgb(64, 64, 64);">奖品管理</font>** | <font style="color:rgb(64, 64, 64);">奖品池切换标签</font> | <font style="color:rgb(64, 64, 64);">拖拽排序奖品</font> |
| **<font style="color:rgb(64, 64, 64);">设置面板</font>** | <font style="color:rgb(64, 64, 64);">滑块控制阈值</font> | <font style="color:rgb(64, 64, 64);">实时预览效果</font> |


## <font style="color:rgb(64, 64, 64);">4. 关键业务流程</font>
### <font style="color:rgb(64, 64, 64);">4.1 任务执行流程</font>
<font style="color:rgb(82, 82, 82);background-color:rgb(250, 250, 250);">plantuml</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">复制</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">下载</font>

```plain
@startuml
actor 儿童 as Child
participant 儿童端 as FE
participant 后端 as BE
database 数据库 as DB

Child -> FE: 点击开始任务
FE -> BE: POST /task/start/{id}
BE -> DB: 更新任务状态(start_time=NOW())
BE --> FE: 返回成功
FE -> Child: 显示计时器

Child -> FE: 点击完成任务
FE -> BE: POST /task/complete/{id}
BE -> DB: 更新任务状态(end_time=NOW())
BE -> BE: 计算实际耗时
BE -> DB: 创建审批记录
BE --> FE: 返回待审批状态
FE -> Child: 显示等待图标

BE -> 家长端: 推送审批通知
@enduml
```

### <font style="color:rgb(64, 64, 64);">4.2 每日结算流程</font>
<font style="color:rgb(82, 82, 82);background-color:rgb(250, 250, 250);">plantuml</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">复制</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">下载</font>

```plain
@startuml
start
partition 每日凌晨1点 {
  :获取所有未完成任务;
  :标记为❌未完成;
  :统计未完成数量;
  if (未完成任务 > 阈值?) then (yes)
    :计算应扣积分;
    :创建扣分记录;
  else (no)
  endif
  :生成每日报告;
  :推送家长端;
}
stop
@enduml
```

## <font style="color:rgb(64, 64, 64);">5. 数据模型设计</font>
### <font style="color:rgb(64, 64, 64);">5.1 核心数据表</font>
#### <font style="color:rgb(64, 64, 64);">任务表 (tasks)</font>
| **<font style="color:rgb(64, 64, 64);">字段</font>** | **<font style="color:rgb(64, 64, 64);">类型</font>** | **<font style="color:rgb(64, 64, 64);">描述</font>** |
| --- | --- | --- |
| <font style="color:rgb(64, 64, 64);">id</font> | <font style="color:rgb(64, 64, 64);">BIGINT</font> | <font style="color:rgb(64, 64, 64);">主键</font> |
| <font style="color:rgb(64, 64, 64);">title</font> | <font style="color:rgb(64, 64, 64);">VARCHAR(50)</font> | <font style="color:rgb(64, 64, 64);">任务名称</font> |
| <font style="color:rgb(64, 64, 64);">expected_minutes</font> | <font style="color:rgb(64, 64, 64);">INT</font> | <font style="color:rgb(64, 64, 64);">预计分钟数</font> |
| <font style="color:rgb(64, 64, 64);">base_points</font> | <font style="color:rgb(64, 64, 64);">INT</font> | <font style="color:rgb(64, 64, 64);">基础积分</font> |
| <font style="color:rgb(64, 64, 64);">due_time</font> | <font style="color:rgb(64, 64, 64);">TIME</font> | <font style="color:rgb(64, 64, 64);">当天截止时间</font> |
| <font style="color:rgb(64, 64, 64);">status</font> | <font style="color:rgb(64, 64, 64);">ENUM</font> | <font style="color:rgb(64, 64, 64);">状态值</font> |
| <font style="color:rgb(64, 64, 64);">start_time</font> | <font style="color:rgb(64, 64, 64);">DATETIME</font> | <font style="color:rgb(64, 64, 64);">实际开始时间</font> |
| <font style="color:rgb(64, 64, 64);">end_time</font> | <font style="color:rgb(64, 64, 64);">DATETIME</font> | <font style="color:rgb(64, 64, 64);">实际结束时间</font> |


#### <font style="color:rgb(64, 64, 64);">奖品池表 (reward_pools)</font>
| **<font style="color:rgb(64, 64, 64);">字段</font>** | **<font style="color:rgb(64, 64, 64);">类型</font>** | **<font style="color:rgb(64, 64, 64);">描述</font>** |
| --- | --- | --- |
| <font style="color:rgb(64, 64, 64);">id</font> | <font style="color:rgb(64, 64, 64);">BIGINT</font> | <font style="color:rgb(64, 64, 64);">主键</font> |
| <font style="color:rgb(64, 64, 64);">name</font> | <font style="color:rgb(64, 64, 64);">VARCHAR(50)</font> | <font style="color:rgb(64, 64, 64);">奖品池名称</font> |
| <font style="color:rgb(64, 64, 64);">cost_points</font> | <font style="color:rgb(64, 64, 64);">INT</font> | <font style="color:rgb(64, 64, 64);">抽奖消耗积分</font> |


#### <font style="color:rgb(64, 64, 64);">奖品项表 (reward_items)</font>
| **<font style="color:rgb(64, 64, 64);">字段</font>** | **<font style="color:rgb(64, 64, 64);">类型</font>** | **<font style="color:rgb(64, 64, 64);">描述</font>** |
| --- | --- | --- |
| <font style="color:rgb(64, 64, 64);">id</font> | <font style="color:rgb(64, 64, 64);">BIGINT</font> | <font style="color:rgb(64, 64, 64);">主键</font> |
| <font style="color:rgb(64, 64, 64);">pool_id</font> | <font style="color:rgb(64, 64, 64);">BIGINT</font> | <font style="color:rgb(64, 64, 64);">奖品池ID</font> |
| <font style="color:rgb(64, 64, 64);">name</font> | <font style="color:rgb(64, 64, 64);">VARCHAR(50)</font> | <font style="color:rgb(64, 64, 64);">奖品名称</font> |
| <font style="color:rgb(64, 64, 64);">probability</font> | <font style="color:rgb(64, 64, 64);">FLOAT</font> | <font style="color:rgb(64, 64, 64);">中奖概率(0-1)</font> |
| <font style="color:rgb(64, 64, 64);">stock</font> | <font style="color:rgb(64, 64, 64);">INT</font> | <font style="color:rgb(64, 64, 64);">库存数量</font> |


## <font style="color:rgb(64, 64, 64);">6. 安全与可靠性</font>
### <font style="color:rgb(64, 64, 64);">6.1 家长认证</font>
+ <font style="color:rgb(64, 64, 64);">4位数字密码</font>
+ <font style="color:rgb(64, 64, 64);">错误尝试限制（3次/小时）</font>
+ <font style="color:rgb(64, 64, 64);">密码重置功能</font>

### <font style="color:rgb(64, 64, 64);">6.2 数据保护</font>
1. <font style="color:rgb(64, 64, 64);">每日自动备份</font>
2. <font style="color:rgb(64, 64, 64);">手动导出/导入功能</font>
3. <font style="color:rgb(64, 64, 64);">敏感操作日志记录：</font>
    - <font style="color:rgb(64, 64, 64);">积分大额变动</font>
    - <font style="color:rgb(64, 64, 64);">密码修改</font>
    - <font style="color:rgb(64, 64, 64);">关键设置变更</font>

## <font style="color:rgb(64, 64, 64);">7. 部署要求</font>
### <font style="color:rgb(64, 64, 64);">7.1 环境配置</font>
| **<font style="color:rgb(64, 64, 64);">组件</font>** | **<font style="color:rgb(64, 64, 64);">版本</font>** | **<font style="color:rgb(64, 64, 64);">备注</font>** |
| --- | --- | --- |
| <font style="color:rgb(64, 64, 64);">JDK</font> | <font style="color:rgb(64, 64, 64);">21</font> | <font style="color:rgb(64, 64, 64);">必需</font> |
| <font style="color:rgb(64, 64, 64);">MySQL</font> | <font style="color:rgb(64, 64, 64);">8.0+</font> | <font style="color:rgb(64, 64, 64);">数据存储</font> |
| <font style="color:rgb(64, 64, 64);">Web服务器</font> | <font style="color:rgb(64, 64, 64);">Nginx/Apache</font> | <font style="color:rgb(64, 64, 64);">前端部署</font> |


### <font style="color:rgb(64, 64, 64);">7.2 访问端点</font>
+ <font style="color:rgb(64, 64, 64);">儿童端：</font>`**<font style="color:rgb(64, 64, 64);background-color:rgb(236, 236, 236);">/child</font>**`<font style="color:rgb(64, 64, 64);"> </font><font style="color:rgb(64, 64, 64);">(iPad横屏优化)</font>
+ <font style="color:rgb(64, 64, 64);">家长端：</font>`**<font style="color:rgb(64, 64, 64);background-color:rgb(236, 236, 236);">/parent</font>**`<font style="color:rgb(64, 64, 64);"> </font><font style="color:rgb(64, 64, 64);">(手机竖屏优化)</font>
+ <font style="color:rgb(64, 64, 64);">管理API：</font>`**<font style="color:rgb(64, 64, 64);background-color:rgb(236, 236, 236);">/api/*</font>**`

## <font style="color:rgb(64, 64, 64);">8. 扩展规划</font>
### <font style="color:rgb(64, 64, 64);">8.1 成就系统</font>
<font style="color:rgb(82, 82, 82);background-color:rgb(250, 250, 250);">plantuml</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">复制</font>

<font style="color:rgb(82, 82, 82);background-color:rgba(0, 0, 0, 0);">下载</font>

```plain
@startuml
start
:连续7天完成任务;
:解锁"坚持之星"徽章;
:奖励额外积分;
:显示庆祝动画;
stop
@enduml
```

### <font style="color:rgb(64, 64, 64);">8.2 行为分析</font>
+ <font style="color:rgb(64, 64, 64);">任务完成模式识别</font>
+ <font style="color:rgb(64, 64, 64);">超时高频任务预警</font>
+ <font style="color:rgb(64, 64, 64);">个性化任务推荐</font>

<font style="color:rgb(64, 64, 64);">本方案聚焦核心赏罚机制，通过直观的任务管理和丰富的奖励系统实现行为激励目标。家长端简化设计，儿童端强化视觉反馈，确保系统易用且具有吸引力。</font>

