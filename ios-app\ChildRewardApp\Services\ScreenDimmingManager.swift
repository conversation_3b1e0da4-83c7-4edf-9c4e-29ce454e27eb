import UIKit
import Foundation
import Combine

#if targetEnvironment(simulator)
let isSimulator = true
#else
let isSimulator = false
#endif

// 屏幕亮度管理器
class ScreenDimmingManager: ObservableObject {
    static let shared = ScreenDimmingManager()

    @Published var isDimmed: Bool = false
    @Published var isTimerActive: Bool = false
    @Published var showDimmingOverlay: Bool = false // 用于模拟器的可视化息屏效果

    private var dimmingTimer: Timer?
    private var originalBrightness: CGFloat = 1.0
    private let settingsManager = SettingsManager.shared
    private var cancellables = Set<AnyCancellable>()

    // 息屏相关常量
    private let dimmedBrightness: CGFloat = 0.1 // 息屏时的亮度（10%）
    private let animationDuration: TimeInterval = 0.5 // 亮度变化动画时长

    // 系统idle timer的原始状态
    private var originalIdleTimerDisabled: Bool = false
    
    private init() {
        // 保存当前亮度和系统idle timer状态
        originalBrightness = UIScreen.main.brightness
        originalIdleTimerDisabled = UIApplication.shared.isIdleTimerDisabled

        // 监听应用状态变化
        setupNotifications()

        if isSimulator {
            print("🌟 ScreenDimmingManager 初始化完成（模拟器模式），将使用系统息屏")
        } else {
            print("🌟 ScreenDimmingManager 初始化完成（真机模式），当前亮度: \(originalBrightness)")
        }
        print("🌟 系统idle timer原始状态: \(originalIdleTimerDisabled ? "禁用" : "启用")")
    }
    
    deinit {
        cancelDimmingTimer()
        NotificationCenter.default.removeObserver(self)
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 应用进入前台时恢复亮度
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        // 应用进入后台时取消定时器
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        // 用户开始交互时恢复亮度
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(userDidInteract),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    // 开始息屏倒计时
    func startDimmingTimer() {
        guard settingsManager.isScreenDimmingEnabled else {
            print("🌟 息屏功能已禁用，跳过倒计时")
            return
        }

        // 如果定时器已经在运行，不重复启动
        if isTimerActive {
            print("🌟 息屏倒计时已在运行，跳过重复启动")
            return
        }

        // 取消之前的定时器
        cancelDimmingTimer()

        // 如果已经息屏，先恢复亮度
        if isDimmed {
            restoreBrightness()
        }

        let delay = settingsManager.screenDimmingDelay
        print("🌟 开始息屏倒计时: \(delay)秒，当前时间: \(Date())")

        isTimerActive = true

        // 确保在主线程上创建定时器
        DispatchQueue.main.async {
            self.dimmingTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] timer in
                print("🌙 ⚡️ 定时器触发！息屏倒计时到达，当前时间: \(Date())")
                print("🌙 ⚡️ 定时器有效性: \(timer.isValid)")
                print("🌙 ⚡️ self存在: \(self != nil)")
                DispatchQueue.main.async {
                    self?.dimScreen()
                }
            }

            // 将定时器添加到当前RunLoop
            if let timer = self.dimmingTimer {
                RunLoop.current.add(timer, forMode: .common)
                print("🌟 息屏定时器已启动并添加到RunLoop，预计息屏时间: \(Date().addingTimeInterval(delay))")
            }
        }
    }
    
    // 取消息屏倒计时
    func cancelDimmingTimer() {
        dimmingTimer?.invalidate()
        dimmingTimer = nil
        isTimerActive = false
        
        print("🌟 息屏倒计时已取消")
    }
    
    // 息屏 - 强制显示效果
    private func dimScreen() {
        print("🌙 ⚡️ dimScreen被调用！")

        guard !isDimmed else {
            print("🌙 息屏被跳过：屏幕已经息屏")
            return
        }

        print("🌙 开始强制息屏，当前时间: \(Date())")
        print("🌙 运行环境: \(isSimulator ? "模拟器" : "真机")")

        // 在主线程上执行息屏操作
        DispatchQueue.main.async {
            print("🌙 在主线程执行息屏操作")

            // 启用系统自动锁屏
            UIApplication.shared.isIdleTimerDisabled = false
            print("🌙 已启用系统自动锁屏功能")

            if isSimulator {
                // 模拟器强制显示覆盖层
                print("🌙 模拟器：强制显示息屏覆盖层")
                self.showDimmingOverlay = true
                print("🌙 模拟器：showDimmingOverlay = \(self.showDimmingOverlay)")
            } else {
                // 真机降低亮度
                print("🌙 真机：降低亮度至 \(self.dimmedBrightness)")
                self.originalBrightness = UIScreen.main.brightness
                UIScreen.main.brightness = self.dimmedBrightness
            }

            self.isDimmed = true
            self.isTimerActive = false
            print("🌙 ✅ 息屏操作完成！isDimmed = \(self.isDimmed)")
        }
    }
    
    // 恢复亮度和系统状态
    func restoreBrightness() {
        guard isDimmed else {
            print("🌟 恢复被跳过：屏幕未息屏")
            return
        }

        print("🌟 开始恢复系统状态，当前时间: \(Date())")
        print("🌟 运行环境: \(isSimulator ? "模拟器" : "真机")")

        DispatchQueue.main.async {
            // 恢复系统idle timer的原始状态
            UIApplication.shared.isIdleTimerDisabled = self.originalIdleTimerDisabled
            print("🌟 已恢复系统idle timer状态: \(self.originalIdleTimerDisabled ? "禁用" : "启用")")

            if isSimulator {
                // 模拟器隐藏覆盖层
                print("🌟 模拟器恢复：隐藏覆盖层")
                self.showDimmingOverlay = false
            } else {
                // 真机恢复亮度
                print("🌟 真机恢复：\(self.dimmedBrightness) -> \(self.originalBrightness)")
                UIScreen.main.brightness = self.originalBrightness
            }

            self.isDimmed = false
            print("🌟 系统状态恢复完成")
        }

        // 取消定时器
        cancelDimmingTimer()
    }
    
    // 用户交互时的处理
    func handleUserInteraction() {
        if isDimmed {
            print("🌟 用户交互：屏幕已息屏，恢复亮度")
            restoreBrightness()
        } else if isTimerActive {
            print("🌟 用户交互：定时器运行中，重置倒计时")
            // 重新开始倒计时
            startDimmingTimer()
        } else {
            print("🌟 用户交互：无活动定时器，不执行操作")
        }
    }

    // 专门用于任务开始时的息屏启动（强制启用，用于调试）
    func startDimmingForTask() {
        print("🌟 强制启动任务息屏功能")
        print("🌟 设置状态 - 启用: \(settingsManager.isScreenDimmingEnabled), 延迟: \(settingsManager.screenDimmingDelay)秒")

        // 强制启用息屏功能（忽略设置）
        print("🌟 强制启用息屏功能进行测试")

        // 立即启用系统自动锁屏功能
        enableSystemAutoLock()

        // 强制启动定时器，使用固定的3秒延迟进行测试
        forceStartDimmingTimer()
    }

    // 强制启动定时器（用于调试）
    private func forceStartDimmingTimer() {
        let testDelay: TimeInterval = 3.0 // 固定3秒用于测试

        print("🌟 强制启动息屏定时器: \(testDelay)秒")

        // 取消之前的定时器
        cancelDimmingTimer()

        // 如果已经息屏，先恢复
        if isDimmed {
            restoreBrightness()
        }

        isTimerActive = true

        // 在主线程上创建定时器
        DispatchQueue.main.async {
            self.dimmingTimer = Timer.scheduledTimer(withTimeInterval: testDelay, repeats: false) { [weak self] timer in
                print("🌙 ⚡️ 强制定时器触发！开始息屏")
                DispatchQueue.main.async {
                    self?.dimScreen()
                }
            }

            print("🌟 强制定时器已启动，\(testDelay)秒后将触发息屏")
        }
    }

    // 启用系统自动锁屏功能
    private func enableSystemAutoLock() {
        DispatchQueue.main.async {
            print("🌟 启用系统自动锁屏功能，为任务期间节电做准备")
            UIApplication.shared.isIdleTimerDisabled = false
        }
    }

    // 任务完成时恢复系统状态
    func restoreSystemStateAfterTask() {
        print("🌟 任务完成，恢复系统原始状态")
        DispatchQueue.main.async {
            UIApplication.shared.isIdleTimerDisabled = self.originalIdleTimerDisabled
            print("🌟 系统idle timer已恢复为: \(self.originalIdleTimerDisabled ? "禁用" : "启用")")
        }

        // 如果当前是息屏状态，也恢复
        if isDimmed {
            restoreBrightness()
        }

        // 取消任何活动的定时器
        cancelDimmingTimer()
    }

    // 测试方法：立即触发息屏（用于调试）
    func testDimmingImmediately() {
        print("🧪 测试方法：立即触发息屏")
        dimScreen()
    }

    // 测试方法：启动短时间倒计时（用于调试）
    func testDimmingWithShortDelay() {
        print("🧪 测试方法：启动2秒倒计时")
        cancelDimmingTimer()
        isTimerActive = true

        dimmingTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { [weak self] timer in
            print("🧪 测试定时器触发！")
            self?.dimScreen()
        }

        print("🧪 测试定时器已启动，2秒后应该触发")
    }
    
    // 强制息屏（立即）
    func dimScreenImmediately() {
        cancelDimmingTimer()
        dimScreen()
    }
    
    // 强制恢复亮度（立即）
    func restoreBrightnessImmediately() {
        cancelDimmingTimer()

        if isDimmed {
            if isSimulator {
                showDimmingOverlay = false
                print("🌟 模拟器立即恢复完成")
            } else {
                UIScreen.main.brightness = originalBrightness
                print("🌟 真机立即恢复完成")
            }
            isDimmed = false
        }
    }
    
    // MARK: - 通知处理
    
    @objc private func appDidBecomeActive() {
        print("🌟 应用进入前台，恢复亮度")
        restoreBrightnessImmediately()
    }
    
    @objc private func appDidEnterBackground() {
        print("🌟 应用进入后台，取消息屏定时器")
        cancelDimmingTimer()
    }
    
    @objc private func userDidInteract() {
        handleUserInteraction()
    }
    
    // 获取当前状态描述
    var statusDescription: String {
        if isDimmed {
            return "已息屏"
        } else if isTimerActive {
            return "倒计时中"
        } else {
            return "正常"
        }
    }
}
