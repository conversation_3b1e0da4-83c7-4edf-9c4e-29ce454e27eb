#!/bin/bash

echo "=========================================="
echo "  CRS 全组件打包脚本"
echo "  打包前端、后端、iOS客户端到桌面"
echo "=========================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 获取项目根目录和桌面路径
PROJECT_ROOT=$(pwd)
DESKTOP_PATH="$HOME/Desktop"
PACKAGES_DIR="$PROJECT_ROOT/packages"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DESKTOP_OUTPUT_DIR="$DESKTOP_PATH/CRS_Packages_$TIMESTAMP"

echo -e "${CYAN}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${CYAN}桌面路径: $DESKTOP_PATH${NC}"
echo -e "${CYAN}输出目录: $DESKTOP_OUTPUT_DIR${NC}"
echo ""

# 创建桌面输出目录
mkdir -p "$DESKTOP_OUTPUT_DIR"

# 设置Java版本
echo -e "${CYAN}=== 设置Java环境 ===${NC}"
if command -v sdk &> /dev/null; then
    echo -e "${YELLOW}使用sdkman设置Java 17...${NC}"
    source "$HOME/.sdkman/bin/sdkman-init.sh"
    sdk use java 17.0.15-amzn
    java -version
else
    echo -e "${YELLOW}sdkman未找到，使用当前Java版本...${NC}"
    java -version
fi
echo ""

# 1. 打包后端
echo -e "${CYAN}=== 打包后端 JAR ===${NC}"
cd "$PROJECT_ROOT/backend"

echo -e "${YELLOW}运行: mvn clean package -DskipTests${NC}"
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    echo -e "${RED}后端打包失败${NC}"
    exit 1
fi

# 查找并复制JAR文件
JAR_FILE=$(find target -name "*.jar" -not -name "*.original" | head -1)
if [ -n "$JAR_FILE" ]; then
    cp "$JAR_FILE" "$DESKTOP_OUTPUT_DIR/crs-backend.jar"
    echo -e "${GREEN}后端JAR已复制到: $DESKTOP_OUTPUT_DIR/crs-backend.jar${NC}"
else
    echo -e "${RED}未找到后端JAR文件${NC}"
    exit 1
fi

# 2. 打包前端
echo ""
echo -e "${CYAN}=== 打包前端 ===${NC}"
cd "$PROJECT_ROOT/frontend"

echo -e "${YELLOW}运行: npm run build${NC}"
npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}前端打包失败${NC}"
    exit 1
fi

# 创建前端ZIP包
echo -e "${YELLOW}创建前端ZIP包...${NC}"
cd dist
zip -r "$DESKTOP_OUTPUT_DIR/crs-frontend.zip" ./*
echo -e "${GREEN}前端ZIP已创建: $DESKTOP_OUTPUT_DIR/crs-frontend.zip${NC}"

# 3. 打包iOS应用
echo ""
echo -e "${CYAN}=== 打包iOS应用 ===${NC}"
cd "$PROJECT_ROOT/ios-app"

# 设置Xcode开发者路径
export DEVELOPER_DIR="/Applications/Xcode.app/Contents/Developer"

# 检查Xcode是否正确安装
if [ ! -d "$DEVELOPER_DIR" ]; then
    echo -e "${RED}❌ 错误: 未找到Xcode，请确保已安装Xcode${NC}"
    echo -e "${YELLOW}跳过iOS应用打包...${NC}"
else
    echo -e "${GREEN}✅ Xcode路径: $DEVELOPER_DIR${NC}"
    
    # 清理之前的构建
    echo -e "${YELLOW}🧹 清理之前的构建...${NC}"
    rm -rf build/
    rm -rf *.ipa
    
    # 创建构建目录
    mkdir -p build
    
    # 构建应用 (Release配置)
    echo ""
    echo -e "${YELLOW}🔨 构建Release版本...${NC}"
    xcodebuild -project ChildRewardApp.xcodeproj \
               -scheme ChildRewardApp \
               -configuration Release \
               -destination 'generic/platform=iOS' \
               -archivePath build/ChildRewardApp.xcarchive \
               archive
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ iOS构建失败${NC}"
        echo -e "${YELLOW}继续处理其他组件...${NC}"
    else
        echo -e "${GREEN}✅ Archive构建成功！${NC}"
        
        # 导出IPA
        echo ""
        echo -e "${YELLOW}📦 导出IPA文件...${NC}"
        
        # 创建导出配置文件
        cat > build/ExportOptions.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>development</string>
    <key>teamID</key>
    <string>FAKETEAMID</string>
    <key>compileBitcode</key>
    <false/>
    <key>stripSwiftSymbols</key>
    <true/>
    <key>thinning</key>
    <string>&lt;none&gt;</string>
</dict>
</plist>
EOF
        
        # 导出IPA
        xcodebuild -exportArchive \
                   -archivePath build/ChildRewardApp.xcarchive \
                   -exportPath build/ \
                   -exportOptionsPlist build/ExportOptions.plist
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ IPA导出成功！${NC}"
            
            # 移动IPA文件到桌面
            if [ -f "build/ChildRewardApp.ipa" ]; then
                cp "build/ChildRewardApp.ipa" "$DESKTOP_OUTPUT_DIR/作业小助手.ipa"
                echo -e "${GREEN}📱 IPA文件已复制到: $DESKTOP_OUTPUT_DIR/作业小助手.ipa${NC}"
            else
                echo -e "${RED}❌ 未找到生成的IPA文件${NC}"
            fi
        else
            echo -e "${RED}❌ IPA导出失败${NC}"
        fi
    fi
fi

# 4. 创建说明文件
echo ""
echo -e "${CYAN}=== 创建说明文件 ===${NC}"
cat > "$DESKTOP_OUTPUT_DIR/README.txt" << EOF
CRS 儿童奖励系统 - 打包文件说明
构建时间: $(date)

包含文件:
1. crs-backend.jar - 后端服务 (Spring Boot可执行JAR)
   运行方式: java -jar crs-backend.jar

2. crs-frontend.zip - 前端静态文件
   部署方式: 解压到Web服务器目录 (如nginx)

3. 作业小助手.ipa - iOS客户端应用 (如果构建成功)
   安装方式: 使用巨魔签名工具签名后安装到iPad

系统要求:
- 后端: Java 17+
- 前端: 任何Web服务器 (nginx, apache等)
- iOS客户端: iOS 16.6+

注意事项:
- 后端需要配置数据库连接
- 前端需要配置API地址
- iOS应用需要签名后才能安装
EOF

# 5. 显示结果
echo ""
echo -e "${GREEN}=========================================="
echo -e "  打包完成！"
echo -e "==========================================${NC}"
echo ""
echo -e "${CYAN}所有文件已保存到: $DESKTOP_OUTPUT_DIR${NC}"
echo ""
echo -e "${YELLOW}生成的文件:${NC}"
ls -la "$DESKTOP_OUTPUT_DIR"

echo ""
echo -e "${GREEN}🎉 全部打包完成！${NC}"
echo -e "${CYAN}💡 提示: 请查看桌面上的 CRS_Packages_$TIMESTAMP 文件夹${NC}"

# 返回项目根目录
cd "$PROJECT_ROOT"
