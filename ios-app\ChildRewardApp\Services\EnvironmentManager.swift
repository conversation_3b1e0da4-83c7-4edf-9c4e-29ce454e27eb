import Foundation
import Combine

// 环境管理器
class EnvironmentManager: ObservableObject {
    static let shared = EnvironmentManager()
    
    // 环境类型
    enum Environment: String, CaseIterable {
        case development = "开发环境"
        case developmentBackup = "开发环境（备用）"
        case production = "生产环境"

        var baseURL: String {
            switch self {
            case .development:
                return "http://192.168.31.23:18080"  // 本地开发环境
            case .developmentBackup:
                return "http://192.168.31.142:18080"  // 备用开发环境
            case .production:
                return "http://192.168.31.75:18080"  // 生产环境
            }
        }

        var displayName: String {
            return self.rawValue
        }
    }
    
    // 当前环境 - 默认使用开发环境，连接本地后端服务
    @Published var currentEnvironment: Environment = .development
    
    // 当前基础URL
    var currentBaseURL: String {
        return currentEnvironment.baseURL
    }
    
    private init() {
        // 从UserDefaults加载保存的环境设置
        if let savedEnv = UserDefaults.standard.string(forKey: "selectedEnvironment"),
           let environment = Environment(rawValue: savedEnv) {
            self.currentEnvironment = environment
            print("🔄 从UserDefaults加载环境: \(savedEnv)")
            print("🌐 当前API基础URL: \(currentBaseURL)")
        } else {
            print("⚠️ UserDefaults中没有保存的环境设置，使用默认环境: \(currentEnvironment.rawValue)")
            print("🌐 默认API基础URL: \(currentBaseURL)")
        }
    }
    
    // 切换环境
    func switchEnvironment(to environment: Environment) {
        currentEnvironment = environment
        // 保存到UserDefaults
        UserDefaults.standard.set(environment.rawValue, forKey: "selectedEnvironment")
        // 强制同步UserDefaults，确保立即保存
        UserDefaults.standard.synchronize()

        print("🔄 环境已切换到: \(environment.rawValue)")
        print("🌐 API基础URL: \(currentBaseURL)")

        // 验证保存是否成功
        if let savedEnv = UserDefaults.standard.string(forKey: "selectedEnvironment") {
            print("✅ 环境设置已保存: \(savedEnv)")
        } else {
            print("❌ 环境设置保存失败!")
        }
    }
    
    // 获取当前环境显示名称
    var currentEnvironmentName: String {
        return currentEnvironment.rawValue
    }
}
