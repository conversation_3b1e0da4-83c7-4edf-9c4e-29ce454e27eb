import SwiftUI

// MARK: - 兑换商品卡片组件
struct ExchangeItemCard: View {
    let item: ExchangeItem
    let userPoints: Int
    let onExchange: () -> Void
    
    private var canExchange: Bool {
        return userPoints >= item.requiredPoints && item.canExchange
    }
    
    private var categoryIcon: String {
        switch item.category {
        case "游戏时间": return "gamecontroller.fill"
        case "特权": return "crown.fill"
        case "实物奖励": return "gift.fill"
        case "零花钱": return "dollarsign.circle.fill"
        default: return "star.fill"
        }
    }
    
    private var categoryColor: Color {
        switch item.category {
        case "游戏时间": return .blue
        case "特权": return .purple
        case "实物奖励": return .orange
        case "零花钱": return .green
        default: return .gray
        }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // 商品图标和名称
            VStack(spacing: 8) {
                Image(systemName: categoryIcon)
                    .font(.system(size: 32))
                    .foregroundColor(categoryColor)
                
                Text(item.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            
            // 商品描述
            if let description = item.description, !description.isEmpty {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            // 商品信息
            VStack(spacing: 8) {
                HStack {
                    Text("所需积分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(item.requiredPoints)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
                
                if let stockText = item.stockText {
                    HStack {
                        Text("库存状态")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text(stockText)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(item.hasStock ? .green : .red)
                    }
                }
            }
            
            // 兑换按钮
            Button(action: onExchange) {
                HStack {
                    Image(systemName: "cart.fill")
                        .font(.subheadline)
                    
                    Text(getButtonText())
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(getButtonColor())
                .cornerRadius(12)
            }
            .disabled(!canExchange)
            .buttonStyle(PlainButtonStyle())
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .scaleEffect(canExchange ? 1.0 : 0.95)
        .opacity(canExchange ? 1.0 : 0.7)
        .animation(.easeInOut(duration: 0.2), value: canExchange)
    }
    
    private func getButtonText() -> String {
        if !item.isActive {
            return "已下架"
        } else if !item.hasStock {
            return "缺货"
        } else if userPoints < item.requiredPoints {
            return "积分不足"
        } else {
            return "立即兑换"
        }
    }
    
    private func getButtonColor() -> Color {
        if canExchange {
            return categoryColor
        } else {
            return .gray
        }
    }
}

// MARK: - 预览
struct ExchangeItemCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ExchangeItemCard(
                item: ExchangeItem(
                    id: 1,
                    name: "游戏时间",
                    description: "额外30分钟游戏时间",
                    requiredPoints: 50,
                    stock: 10,
                    category: "游戏时间",
                    imageUrl: nil,
                    isActive: true,
                    sortOrder: 1,
                    createdTime: nil,
                    updatedTime: nil
                ),
                userPoints: 100,
                onExchange: {}
            )
            
            ExchangeItemCard(
                item: ExchangeItem(
                    id: 2,
                    name: "小玩具",
                    description: "精美小玩具一个",
                    requiredPoints: 200,
                    stock: 0,
                    category: "实物奖励",
                    imageUrl: nil,
                    isActive: true,
                    sortOrder: 2,
                    createdTime: nil,
                    updatedTime: nil
                ),
                userPoints: 50,
                onExchange: {}
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
