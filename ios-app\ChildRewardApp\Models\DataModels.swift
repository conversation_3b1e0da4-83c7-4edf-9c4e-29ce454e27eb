import SwiftUI
import Foundation

// MARK: - 基础数据模型

// 积分统计
struct PointStatistics {
    let currentPoints: Int
    let todayEarned: Int
    let weekEarned: Int
    let totalEarned: Int
    let todayCompleted: Int
    let weekCompleted: Int
    let totalCompleted: Int
    

}

// 任务模型
struct Task: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let points: Int
    let isCompleted: Bool
    let category: String
    let dueDate: Date?
    

}

// 奖励模型
struct Reward: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let pointsRequired: Int
    let icon: String
    let color: Color
    let canClaim: Bool
    let isCompleted: Bool
    

}

// 奖励统计
struct RewardStatistics {
    let totalClaimed: Int
    let availableCount: Int
    let totalValue: Int
    

}

// 兑换商品模型
struct ExchangeItem: Identifiable {
    let id = UUID()
    let name: String
    let description: String
    let pointsCost: Int
    let icon: String
    let color: Color
    let category: String
    let canExchange: Bool
    let stockText: String?
    

}

// 兑换分类
enum ExchangeCategory: String, CaseIterable {
    case all = "全部"
    case gameTime = "游戏时间"
    case privileges = "特权"
    case physicalRewards = "实物奖励"
    case allowance = "零花钱"
    
    var displayName: String {
        return self.rawValue
    }
}

// 积分记录
struct PointRecord: Identifiable, Codable {
    let id = UUID()
    let description: String
    let pointsChange: Int
    let date: Date
    let type: PointRecordType
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
    

}

// 积分记录类型
enum PointRecordType: Codable {
    case earned
    case spent
    
    var icon: String {
        switch self {
        case .earned: return "plus.circle.fill"
        case .spent: return "minus.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .earned: return DesignSystem.Colors.success
        case .spent: return DesignSystem.Colors.error
        }
    }
}
