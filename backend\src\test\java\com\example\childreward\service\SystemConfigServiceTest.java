package com.example.childreward.service;

import com.example.childreward.entity.SystemConfig;
import com.example.childreward.repository.SystemConfigRepository;
import com.example.childreward.service.impl.SystemConfigServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SystemConfigServiceTest {

    @Mock
    private SystemConfigRepository systemConfigRepository;

    @InjectMocks
    private SystemConfigServiceImpl systemConfigService;

    @Captor
    private ArgumentCaptor<SystemConfig> configCaptor;

    private SystemConfig testConfig;

    @BeforeEach
    void setUp() {
        testConfig = SystemConfig.builder()
                .id(1L)
                .configKey("TEST_KEY")
                .configValue("test_value")
                .description("测试配置")
                .updateTime(LocalDateTime.now())
                .build();
    }

    @Test
    void getAllConfigs_shouldReturnAllConfigs() {
        // Given
        List<SystemConfig> configs = Arrays.asList(testConfig);
        when(systemConfigRepository.findAll()).thenReturn(configs);

        // When
        List<SystemConfig> result = systemConfigService.getAllConfigs();

        // Then
        assertEquals(configs, result);
    }

    @Test
    void getConfigByKey_shouldReturnConfigWhenExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("TEST_KEY")).thenReturn(Optional.of(testConfig));

        // When
        SystemConfig result = systemConfigService.getConfigByKey("TEST_KEY");

        // Then
        assertEquals(testConfig, result);
    }

    @Test
    void getConfigByKey_shouldReturnNullWhenNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NON_EXISTENT")).thenReturn(Optional.empty());

        // When
        SystemConfig result = systemConfigService.getConfigByKey("NON_EXISTENT");

        // Then
        assertNull(result);
    }

    @Test
    void createConfig_shouldSaveAndReturnConfig() {
        // Given
        when(systemConfigRepository.save(any(SystemConfig.class))).thenReturn(testConfig);

        // When
        SystemConfig result = systemConfigService.createConfig(testConfig);

        // Then
        assertEquals(testConfig, result);
        verify(systemConfigRepository).save(testConfig);
    }

    @Test
    void updateConfig_shouldUpdateExistingConfig() {
        // Given
        when(systemConfigRepository.findByConfigKey("TEST_KEY")).thenReturn(Optional.of(testConfig));
        when(systemConfigRepository.save(any(SystemConfig.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        SystemConfig result = systemConfigService.updateConfig("TEST_KEY", "new_value", "新描述");

        // Then
        assertEquals("new_value", result.getConfigValue());
        assertEquals("新描述", result.getDescription());
        verify(systemConfigRepository).save(configCaptor.capture());
        assertEquals("TEST_KEY", configCaptor.getValue().getConfigKey());
    }

    @Test
    void updateConfig_shouldCreateConfigWhenNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NEW_KEY")).thenReturn(Optional.empty());
        when(systemConfigRepository.save(any(SystemConfig.class))).thenAnswer(i -> {
            SystemConfig savedConfig = (SystemConfig) i.getArguments()[0];
            savedConfig.setId(2L);
            return savedConfig;
        });

        // When
        SystemConfig result = systemConfigService.updateConfig("NEW_KEY", "new_value", "新配置");

        // Then
        assertEquals("NEW_KEY", result.getConfigKey());
        assertEquals("new_value", result.getConfigValue());
        assertEquals("新配置", result.getDescription());
        verify(systemConfigRepository).save(configCaptor.capture());
        assertEquals("NEW_KEY", configCaptor.getValue().getConfigKey());
    }

    @Test
    void deleteConfig_shouldDeleteExistingConfig() {
        // Given
        when(systemConfigRepository.findByConfigKey("TEST_KEY")).thenReturn(Optional.of(testConfig));

        // When
        systemConfigService.deleteConfig("TEST_KEY");

        // Then
        verify(systemConfigRepository).delete(testConfig);
    }

    @Test
    void deleteConfig_shouldDoNothingWhenConfigNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NON_EXISTENT")).thenReturn(Optional.empty());

        // When
        systemConfigService.deleteConfig("NON_EXISTENT");

        // Then
        verify(systemConfigRepository, never()).delete(any());
    }

    @Test
    void set_shouldUpdateExistingConfig() {
        // Given
        when(systemConfigRepository.findByConfigKey("TEST_KEY")).thenReturn(Optional.of(testConfig));
        when(systemConfigRepository.save(any(SystemConfig.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        systemConfigService.set("TEST_KEY", "set_value", "通过set方法设置");

        // Then
        verify(systemConfigRepository).save(configCaptor.capture());
        SystemConfig savedConfig = configCaptor.getValue();
        assertEquals("TEST_KEY", savedConfig.getConfigKey());
        assertEquals("set_value", savedConfig.getConfigValue());
        assertEquals("通过set方法设置", savedConfig.getDescription());
    }

    @Test
    void set_shouldCreateConfigWhenNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NEW_KEY")).thenReturn(Optional.empty());
        when(systemConfigRepository.save(any(SystemConfig.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        systemConfigService.set("NEW_KEY", "new_value", "新配置");

        // Then
        verify(systemConfigRepository).save(configCaptor.capture());
        SystemConfig savedConfig = configCaptor.getValue();
        assertEquals("NEW_KEY", savedConfig.getConfigKey());
        assertEquals("new_value", savedConfig.getConfigValue());
        assertEquals("新配置", savedConfig.getDescription());
    }

    @Test
    void get_shouldReturnValueWhenExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("TEST_KEY")).thenReturn(Optional.of(testConfig));

        // When
        String result = systemConfigService.get("TEST_KEY", "default");

        // Then
        assertEquals("test_value", result);
    }

    @Test
    void get_shouldReturnDefaultWhenNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NON_EXISTENT")).thenReturn(Optional.empty());

        // When
        String result = systemConfigService.get("NON_EXISTENT", "default_value");

        // Then
        assertEquals("default_value", result);
    }

    @Test
    void getInt_shouldReturnIntValueWhenExists() {
        // Given
        SystemConfig intConfig = SystemConfig.builder()
                .configKey("INT_KEY")
                .configValue("123")
                .build();
        when(systemConfigRepository.findByConfigKey("INT_KEY")).thenReturn(Optional.of(intConfig));

        // When
        Integer result = systemConfigService.getInt("INT_KEY", 0);

        // Then
        assertEquals(123, result);
    }

    @Test
    void getInt_shouldReturnDefaultWhenNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NON_EXISTENT")).thenReturn(Optional.empty());

        // When
        Integer result = systemConfigService.getInt("NON_EXISTENT", 999);

        // Then
        assertEquals(999, result);
    }

    @Test
    void getInt_shouldReturnDefaultWhenValueIsNotInteger() {
        // Given
        SystemConfig invalidConfig = SystemConfig.builder()
                .configKey("INVALID_INT")
                .configValue("not_a_number")
                .build();
        when(systemConfigRepository.findByConfigKey("INVALID_INT")).thenReturn(Optional.of(invalidConfig));

        // When
        Integer result = systemConfigService.getInt("INVALID_INT", 999);

        // Then
        assertEquals(999, result);
    }

    @Test
    void getBoolean_shouldReturnTrueForTrueString() {
        // Given
        SystemConfig trueConfig = SystemConfig.builder()
                .configKey("BOOL_KEY")
                .configValue("true")
                .build();
        when(systemConfigRepository.findByConfigKey("BOOL_KEY")).thenReturn(Optional.of(trueConfig));

        // When
        Boolean result = systemConfigService.getBoolean("BOOL_KEY", false);

        // Then
        assertTrue(result);
    }

    @Test
    void getBoolean_shouldReturnTrueForOneString() {
        // Given
        SystemConfig oneConfig = SystemConfig.builder()
                .configKey("BOOL_KEY")
                .configValue("1")
                .build();
        when(systemConfigRepository.findByConfigKey("BOOL_KEY")).thenReturn(Optional.of(oneConfig));

        // When
        Boolean result = systemConfigService.getBoolean("BOOL_KEY", false);

        // Then
        assertTrue(result);
    }

    @Test
    void getBoolean_shouldReturnFalseForOtherStrings() {
        // Given
        SystemConfig falseConfig = SystemConfig.builder()
                .configKey("BOOL_KEY")
                .configValue("false")
                .build();
        when(systemConfigRepository.findByConfigKey("BOOL_KEY")).thenReturn(Optional.of(falseConfig));

        // When
        Boolean result = systemConfigService.getBoolean("BOOL_KEY", true);

        // Then
        assertFalse(result);
    }

    @Test
    void getBoolean_shouldReturnDefaultWhenNotExists() {
        // Given
        when(systemConfigRepository.findByConfigKey("NON_EXISTENT")).thenReturn(Optional.empty());

        // When
        Boolean result = systemConfigService.getBoolean("NON_EXISTENT", true);

        // Then
        assertTrue(result);
    }
} 