import Foundation

// MARK: - 网络服务
class NetworkService: ObservableObject {
    static let shared = NetworkService()

    private let environmentManager = EnvironmentManager.shared

    private init() {}

    // 获取当前API基础URL
    private var baseURL: String {
        return environmentManager.apiBaseURL
    }

    // MARK: - 任务相关API

    /// 获取今日任务列表
    func fetchTodayTasks() async throws -> [TaskModel] {
        let url = URL(string: "\(baseURL)\(APIEndpoint.todayTasks.path)")!
        print("=== NetworkService: 发起网络请求: \(url) ===")

        do {
            let (data, response) = try await URLSession.shared.data(from: url)
            print("=== NetworkService: 收到响应，数据长度: \(data.count) ===")

            guard let httpResponse = response as? HTTPURLResponse else {
                print("=== NetworkService: 响应不是HTTP响应 ===")
                throw NetworkError.invalidResponse
            }

            print("=== NetworkService: HTTP状态码: \(httpResponse.statusCode) ===")
            guard httpResponse.statusCode == 200 else {
                print("=== NetworkService: HTTP状态码错误: \(httpResponse.statusCode) ===")
                throw NetworkError.invalidResponse
            }

            let decoder = JSONDecoder()
            let tasks = try decoder.decode([TaskModel].self, from: data)
            print("=== NetworkService: 解析成功，任务数量: \(tasks.count) ===")
            return tasks
        } catch {
            print("=== NetworkService: 网络请求失败: \(error) ===")
            throw error
        }
    }

    /// 获取过期任务列表
    func fetchOverdueTasks() async throws -> [TaskModel] {
        let url = URL(string: "\(baseURL)/tasks/overdue")!

        let (data, response) = try await URLSession.shared.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }

        let decoder = JSONDecoder()
        let tasks = try decoder.decode([TaskModel].self, from: data)
        return tasks
    }

    /// 获取指定日期的任务
    func fetchTasksByDate(_ date: String) async throws -> [TaskModel] {
        let url = URL(string: "\(baseURL)/tasks/date/\(date)")!

        let (data, response) = try await URLSession.shared.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }

        let decoder = JSONDecoder()
        let tasks = try decoder.decode([TaskModel].self, from: data)
        return tasks
    }

    /// 获取任务详情
    func fetchTaskDetail(_ taskId: Int) async throws -> TaskModel {
        let url = URL(string: "\(baseURL)\(APIEndpoint.taskDetail(taskId).path)")!
        print("=== NetworkService: 获取任务详情: \(url) ===")

        let (data, response) = try await URLSession.shared.data(from: url)
        print("=== NetworkService: 任务详情响应数据长度: \(data.count) ===")

        guard let httpResponse = response as? HTTPURLResponse else {
            print("=== NetworkService: 任务详情响应不是HTTP响应 ===")
            throw NetworkError.invalidResponse
        }

        print("=== NetworkService: 任务详情HTTP状态码: \(httpResponse.statusCode) ===")
        guard httpResponse.statusCode == 200 else {
            print("=== NetworkService: 任务详情HTTP状态码错误: \(httpResponse.statusCode) ===")
            throw NetworkError.invalidResponse
        }

        // 打印原始JSON数据
        if let jsonString = String(data: data, encoding: .utf8) {
            print("=== NetworkService: 任务详情JSON数据: \(jsonString) ===")
        }

        let decoder = JSONDecoder()
        let task = try decoder.decode(TaskModel.self, from: data)
        print("=== NetworkService: 任务详情解析成功: ID=\(task.id), 描述=\(task.description ?? "无") ===")
        return task
    }

    /// 开始任务
    func startTask(_ taskId: Int) async throws -> TaskModel {
        let url = URL(string: "\(baseURL)\(APIEndpoint.startTask(taskId).path)")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }

        let decoder = JSONDecoder()
        let task = try decoder.decode(TaskModel.self, from: data)
        return task
    }

    /// 完成任务
    func completeTask(_ taskId: Int) async throws -> TaskModel {
        let url = URL(string: "\(baseURL)\(APIEndpoint.completeTask(taskId).path)")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }

        let decoder = JSONDecoder()
        let task = try decoder.decode(TaskModel.self, from: data)
        return task
    }
    
    // MARK: - 积分相关API

    /// 获取用户总积分
    func fetchUserPoints() async throws -> Int {
        let url = URL(string: "\(baseURL)/points/total")!

        let (data, response) = try await URLSession.shared.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }

        let decoder = JSONDecoder()
        let pointsData = try decoder.decode([String: Int].self, from: data)
        return pointsData["totalPoints"] ?? 0
    }

    /// 获取今日积分变化
    func fetchTodayPointChange() async throws -> Int {
        let url = URL(string: "\(baseURL)/points/today")!

        let (data, response) = try await URLSession.shared.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }

        let decoder = JSONDecoder()
        let pointsData = try decoder.decode([String: Int].self, from: data)
        return pointsData["todayChange"] ?? 0
    }
}

// MARK: - 网络错误
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case noData
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .noData:
            return "没有数据"
        case .apiError(let message):
            return message
        }
    }
}

// MARK: - 辅助扩展
extension DateFormatter {
    static let taskDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        return formatter
    }()

    static let taskTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter
    }()
}
