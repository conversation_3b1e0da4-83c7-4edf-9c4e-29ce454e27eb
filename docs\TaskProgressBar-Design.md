# 任务进度条设计文档

## 概述

为儿童端今日任务页面设计了美观的进度条组件，让小孩能够直观地看到当前的任务完成进度。

## 设计特点

### 1. 符合现有UI风格
- **毛玻璃背景** (`.ultraThinMaterial`) - 与现有卡片保持一致
- **16px圆角** - 符合DesignSystem.CornerRadius.card规范
- **iOS系统色彩** - 使用蓝色到绿色的渐变
- **标准阴影** - 遵循DesignSystem.Shadow.card规范

### 2. 纯图形化设计
- **主进度条** - 横向填充式进度条，带发光效果
- **分段指示器** - 小圆点显示每个任务的完成状态
- **渐变色彩** - 根据完成度使用不同颜色
  - 0-50%: 橙色到黄色渐变
  - 50-100%: 蓝色到青色渐变
  - 100%: 绿色渐变

### 3. 动画效果
- **进度填充动画** - 0.8秒缓动动画
- **分段指示器动画** - 每个圆点依次放大，带弹性效果
- **进度变化动画** - 任务完成时平滑更新

## 组件结构

### iOS端组件

#### 1. TaskProgressBar.swift
完整版进度条，用于任务页面顶部
- 显示进度信息（已完成/总数、完成率）
- 主进度条带发光效果
- 分段指示器显示每个任务状态

#### 2. CompactProgressBar.swift
紧凑版进度条，用于主页任务卡片
- 只显示进度条和分段指示器
- 更小的尺寸，适合卡片内嵌

### Web端组件

#### 1. TaskProgressBar.jsx
完整版进度条，对应iOS版本
- 毛玻璃背景效果
- 响应式设计
- Framer Motion动画

#### 2. CompactProgressBar.jsx
紧凑版进度条
- 轻量级设计
- 可选择是否显示分段指示器

## 集成位置

### 1. 任务页面 (TasksView.swift / TaskBoard.jsx)
```swift
// iOS
TaskProgressBar(
    totalTasks: taskViewModel.todayTotalCount,
    completedTasks: taskViewModel.todayCompletedCount
)
```

```jsx
// Web
<TaskProgressBar 
  totalTasks={tasks.length}
  completedTasks={completedTasks}
/>
```

### 2. 主页任务卡片 (ContentView.swift / ChildDashboard.jsx)
```swift
// iOS
CompactProgressBar(
    totalTasks: tasks.count,
    completedTasks: completedTasks
)
```

```jsx
// Web
<CompactProgressBar 
  totalTasks={total}
  completedTasks={completed}
  showSegments={total <= 8}
/>
```

## 视觉效果

### 进度条状态
1. **未开始** (0%) - 灰色背景轨道
2. **进行中** (1-99%) - 渐变色填充
3. **已完成** (100%) - 绿色填充，可能有庆祝动画

### 分段指示器
- **未完成任务** - 灰色小圆点
- **已完成任务** - 彩色小圆点，略微放大
- **动画效果** - 完成时依次点亮，带弹性动画

### 颜色方案
- **主色调** - iOS系统蓝 (#007AFF)
- **成功色** - iOS系统绿 (#34C759)
- **警告色** - iOS系统橙 (#FF9500)
- **背景色** - 系统四级填充色

## 用户体验

### 激励效果
1. **视觉反馈** - 完成任务时进度条平滑增长
2. **成就感** - 接近100%时颜色变为绿色
3. **清晰度** - 一眼就能看出完成进度

### 交互设计
- **非交互式** - 进度条本身不可点击
- **信息展示** - 纯粹的状态显示组件
- **动画引导** - 通过动画吸引注意力

## 技术实现

### iOS端
- 使用SwiftUI的GeometryReader实现响应式宽度
- LinearGradient创建渐变效果
- withAnimation实现平滑动画
- @State管理动画状态

### Web端
- Styled-components处理样式
- Framer Motion处理动画
- CSS渐变和毛玻璃效果
- React Hooks管理状态

## 性能考虑

### 优化措施
1. **动画节流** - 避免频繁的状态更新
2. **条件渲染** - 只在有任务时显示进度条
3. **轻量组件** - 最小化DOM/View层级
4. **缓存计算** - 缓存颜色和渐变计算结果

### 内存管理
- iOS端使用@State避免内存泄漏
- Web端正确清理定时器和事件监听器

## 未来扩展

### 可能的增强功能
1. **积分进度** - 显示积分获取进度
2. **时间进度** - 显示任务时间完成情况
3. **周/月进度** - 长期进度跟踪
4. **个性化主题** - 允许自定义颜色
5. **庆祝动画** - 完成100%时的特殊效果

### 适配考虑
- 支持更多设备尺寸
- 适配暗色模式
- 支持无障碍功能
- 国际化支持
