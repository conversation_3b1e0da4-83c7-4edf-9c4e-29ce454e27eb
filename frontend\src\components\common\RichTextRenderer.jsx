import React, { useState } from 'react';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';

/**
 * 富文本渲染组件
 * 支持HTML富文本、图片、链接等内容的渲染
 */
const RichTextRenderer = ({ content, maxHeight = '300px' }) => {
  const [imageErrors, setImageErrors] = useState(new Set());
  const [expandedImages, setExpandedImages] = useState(new Set());

  if (!content || content.trim() === '' || content === '<p><br></p>') {
    return (
      <div style={{
        color: AppleDesignSystem.colors.semantic.secondaryLabel,
        fontStyle: 'italic',
        padding: AppleDesignSystem.spacing.md
      }}>
        暂无内容
      </div>
    );
  }

  // 检查是否是HTML内容
  const isHtmlContent = /<[^>]+>/.test(content);

  if (isHtmlContent) {
    // 处理HTML富文本内容
    return (
      <div
        style={{
          maxHeight: maxHeight === 'none' ? 'none' : maxHeight,
          overflowY: maxHeight === 'none' ? 'visible' : 'auto',
          fontSize: AppleDesignSystem.typography.textStyles.body.fontSize,
          lineHeight: AppleDesignSystem.typography.textStyles.body.lineHeight,
          color: AppleDesignSystem.colors.semantic.label,
          padding: AppleDesignSystem.spacing.sm,
          // HTML富文本样式
          '& h1, & h2, & h3': {
            margin: '16px 0 8px 0',
            fontWeight: '600'
          },
          '& h1': { fontSize: '1.5em' },
          '& h2': { fontSize: '1.3em' },
          '& h3': { fontSize: '1.1em' },
          '& p': {
            margin: '8px 0'
          },
          '& ul, & ol': {
            margin: '8px 0',
            paddingLeft: '20px'
          },
          '& li': {
            margin: '4px 0'
          },
          '& img': {
            maxWidth: '60% !important',
            width: '60% !important',
            height: 'auto',
            borderRadius: AppleDesignSystem.borderRadius.medium,
            margin: '12px 0',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            display: 'block'
          },
          '& a': {
            color: AppleDesignSystem.colors.semantic.link,
            textDecoration: 'none'
          },
          '& strong': {
            fontWeight: '600'
          }
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }

  // 处理图片加载错误
  const handleImageError = (imageSrc) => {
    setImageErrors(prev => new Set([...prev, imageSrc]));
  };

  // 切换图片展开状态
  const toggleImageExpanded = (imageSrc) => {
    setExpandedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageSrc)) {
        newSet.delete(imageSrc);
      } else {
        newSet.add(imageSrc);
      }
      return newSet;
    });
  };

  // 解析内容中的图片URL
  const parseContent = (text) => {
    if (typeof text !== 'string') return text;

    // 匹配图片URL的正则表达式
    const imageRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp|bmp)(?:\?[^\s]*)?)/gi;
    const urlRegex = /(https?:\/\/[^\s]+)/gi;

    const parts = [];
    let lastIndex = 0;
    let match;

    // 首先处理图片
    while ((match = imageRegex.exec(text)) !== null) {
      // 添加图片前的文本
      if (match.index > lastIndex) {
        const beforeText = text.slice(lastIndex, match.index);
        if (beforeText.trim()) {
          parts.push({
            type: 'text',
            content: beforeText,
            key: `text-${lastIndex}`
          });
        }
      }

      // 添加图片
      parts.push({
        type: 'image',
        content: match[0],
        key: `image-${match.index}`
      });

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余文本
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      if (remainingText.trim()) {
        parts.push({
          type: 'text',
          content: remainingText,
          key: `remaining-text-${lastIndex}`
        });
      }
    }

    // 如果没有找到任何特殊内容，返回原始文本
    if (parts.length === 0) {
      parts.push({
        type: 'text',
        content: text,
        key: 'original-text'
      });
    }

    return parts;
  };

  const renderContent = () => {
    const parsedContent = parseContent(content);
    
    if (typeof parsedContent === 'string') {
      return (
        <div style={{
          fontSize: AppleDesignSystem.typography.textStyles.body.fontSize,
          lineHeight: AppleDesignSystem.typography.textStyles.body.lineHeight,
          color: AppleDesignSystem.colors.semantic.label,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}>
          {parsedContent}
        </div>
      );
    }

    return parsedContent.map((part) => {
      switch (part.type) {
        case 'image':
          const isExpanded = expandedImages.has(part.content);
          const hasError = imageErrors.has(part.content);
          
          if (hasError) {
            return (
              <div
                key={part.key}
                style={{
                  margin: `${AppleDesignSystem.spacing.md} 0`,
                  padding: AppleDesignSystem.spacing.md,
                  background: AppleDesignSystem.colors.semantic.systemFill,
                  borderRadius: AppleDesignSystem.borderRadius.medium,
                  textAlign: 'center',
                  color: AppleDesignSystem.colors.semantic.secondaryLabel
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>🖼️</div>
                <div style={{ fontSize: '14px' }}>图片加载失败</div>
                <div style={{ 
                  fontSize: '12px', 
                  marginTop: '4px',
                  wordBreak: 'break-all',
                  opacity: 0.7
                }}>
                  {part.content}
                </div>
              </div>
            );
          }

          return (
            <div
              key={part.key}
              style={{
                margin: `${AppleDesignSystem.spacing.md} 0`,
                textAlign: 'center'
              }}
            >
              <img
                src={part.content}
                alt="任务图片"
                style={{
                  maxWidth: '60%',
                  width: '60%',
                  height: 'auto',
                  maxHeight: isExpanded ? 'none' : '240px',
                  objectFit: 'contain',
                  borderRadius: AppleDesignSystem.borderRadius.medium,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'block',
                  margin: '12px 0'
                }}
                onClick={() => toggleImageExpanded(part.content)}
                onError={() => handleImageError(part.content)}
                loading="lazy"
              />
              <div style={{
                marginTop: AppleDesignSystem.spacing.sm,
                fontSize: AppleDesignSystem.typography.textStyles.caption.fontSize,
                color: AppleDesignSystem.colors.semantic.secondaryLabel
              }}>
                {isExpanded ? '点击收起' : '点击查看大图'}
              </div>
            </div>
          );

        case 'text':
        default:
          return (
            <span
              key={part.key}
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {part.content}
            </span>
          );
      }
    });
  };

  return (
    <div style={{
      maxHeight: maxHeight === 'none' ? 'none' : maxHeight,
      overflowY: maxHeight === 'none' ? 'visible' : 'auto',
      fontSize: AppleDesignSystem.typography.textStyles.body.fontSize,
      lineHeight: AppleDesignSystem.typography.textStyles.body.lineHeight,
      color: AppleDesignSystem.colors.semantic.label,
      padding: AppleDesignSystem.spacing.sm
    }}>
      {renderContent()}
    </div>
  );
};

export default RichTextRenderer;