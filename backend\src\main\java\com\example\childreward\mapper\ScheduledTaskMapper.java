package com.example.childreward.mapper;

import com.example.childreward.dto.ScheduledTaskSummaryDto;
import com.example.childreward.entity.ScheduledTask;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 计划任务映射器 - 用于ScheduledTask实体和DTO之间的转换
 * 优化性能：列表查询时不包含description字段（图片base64数据）
 */
@Component
public class ScheduledTaskMapper {
    
    /**
     * 将ScheduledTask实体转换为ScheduledTaskSummaryDto
     * 不包含description字段，用于列表查询优化
     */
    public ScheduledTaskSummaryDto toSummaryDto(ScheduledTask scheduledTask) {
        if (scheduledTask == null) {
            return null;
        }
        
        return ScheduledTaskSummaryDto.builder()
                .id(scheduledTask.getId())
                .title(scheduledTask.getTitle())
                .points(scheduledTask.getPoints())
                .type(scheduledTask.getType())
                .taskRequirementType(scheduledTask.getTaskRequirementType())
                .cronExpression(scheduledTask.getCronExpression())
                .expectedMinutes(scheduledTask.getExpectedMinutes())
                .dueTime(scheduledTask.getDueTime())
                .executionTime(scheduledTask.getExecutionTime())
                .createdByUserId(scheduledTask.getCreatedByUserId())
                .active(scheduledTask.isActive())
                .directToReview(scheduledTask.getDirectToReview())
                .build();
    }
    
    /**
     * 批量转换ScheduledTask列表为ScheduledTaskSummaryDto列表
     */
    public List<ScheduledTaskSummaryDto> toSummaryDtoList(List<ScheduledTask> scheduledTasks) {
        if (scheduledTasks == null) {
            return null;
        }
        
        return scheduledTasks.stream()
                .map(this::toSummaryDto)
                .collect(Collectors.toList());
    }
}