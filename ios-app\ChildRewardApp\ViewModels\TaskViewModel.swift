import Foundation
import Combine
import SwiftUI

// 避免与Swift内置Task冲突
typealias AppTask = TaskModel

// 任务视图模型
class TaskViewModel: ObservableObject {
    @Published var todayTasks: [TaskSummary] = []
    @Published var yesterdayTasks: [TaskSummary] = []
    @Published var overdueTasks: [TaskSummary] = []
    @Published var selectedTask: TaskDetail?  // 改为TaskDetail类型
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showTaskDetail = false
    @Published var showError = false
    @Published var networkStatus: String = "未连接"

    private let taskService = TaskService()
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        print("=== TaskViewModel: 初始化开始 ===")

        // 先测试简单的网络连接
        testNetworkConnection()

        loadAllTasks()

        // 监听刷新通知
        NotificationCenter.default.publisher(for: .refreshData)
            .sink { [weak self] _ in
                print("=== TaskViewModel: 收到刷新通知 ===")
                self?.loadAllTasks()
            }
            .store(in: &cancellables)

        // 监听环境切换通知
        NotificationCenter.default.publisher(for: .environmentDidChange)
            .sink { [weak self] notification in
                if let environment = notification.object as? Environment {
                    print("=== TaskViewModel: 环境切换到 \(environment.displayName) ===")
                    self?.loadAllTasks()
                }
            }
            .store(in: &cancellables)
    }

    private func testNetworkConnection() {
        print("=== TaskViewModel: 测试网络连接 ===")
        networkStatus = "正在连接..."
        let url = URL(string: "http://**************:18080/api/tasks/today")!
        print("=== TaskViewModel: 测试URL: \(url) ===")

        URLSession.shared.dataTask(with: url) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("=== TaskViewModel: 网络连接失败: \(error) ===")
                    self.networkStatus = "连接失败: \(error.localizedDescription)"
                } else if let httpResponse = response as? HTTPURLResponse {
                    print("=== TaskViewModel: 网络连接成功，状态码: \(httpResponse.statusCode) ===")
                    self.networkStatus = "连接成功: \(httpResponse.statusCode)"
                    if let data = data {
                        print("=== TaskViewModel: 收到数据长度: \(data.count) bytes ===")
                        self.networkStatus += " (数据: \(data.count) bytes)"
                    }
                } else {
                    print("=== TaskViewModel: 收到非HTTP响应 ===")
                    self.networkStatus = "非HTTP响应"
                }
            }
        }.resume()
    }
    
    // MARK: - 数据加载
    
    func loadAllTasks() {
        print("=== TaskViewModel: 开始加载所有任务 ===")
        isLoading = true
        errorMessage = nil

        taskService.fetchTodayTasks()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] tasks in
                    self?.todayTasks = tasks
                    self?.yesterdayTasks = [] // 暂时为空
                    self?.overdueTasks = [] // 暂时为空
                    print("=== TaskViewModel: 成功加载 \(tasks.count) 个任务 ===")
                }
            )
            .store(in: &cancellables)
    }
    
    // 移除了不再使用的方法
    
    // MARK: - 任务操作
    
    func loadTaskDetail(taskId: Int) {
        isLoading = true
        errorMessage = nil

        taskService.fetchTaskDetail(taskId: taskId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] taskDetail in
                    self?.selectedTask = taskDetail
                    self?.showTaskDetail = true
                    print("=== TaskViewModel: 成功加载任务详情 - \(taskDetail.title) ===")
                }
            )
            .store(in: &cancellables)
    }
    
    func startTask(taskId: Int) {
        isLoading = true
        errorMessage = nil

        taskService.startTask(taskId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] result in
                    print("=== TaskViewModel: 任务开始成功 - \(result.message ?? "") ===")
                    self?.showSuccessMessage("任务已开始！")
                    self?.loadAllTasks() // 刷新任务列表
                    // 如果当前显示的是这个任务的详情，也刷新详情
                    if let selectedTask = self?.selectedTask, selectedTask.id == taskId {
                        self?.loadTaskDetail(taskId: taskId)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func completeTask(taskId: Int) {
        isLoading = true
        errorMessage = nil

        taskService.completeTask(taskId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] result in
                    let points = result.points ?? 0
                    print("=== TaskViewModel: 任务完成成功 - \(result.message ?? "") ===")
                    self?.showSuccessMessage("任务完成！获得 \(points) 积分")
                    self?.loadAllTasks() // 刷新任务列表
                    // 如果当前显示的是这个任务的详情，也刷新详情
                    if let selectedTask = self?.selectedTask, selectedTask.id == taskId {
                        self?.loadTaskDetail(taskId: taskId)
                    }
                    // 发送积分变化通知，通知主页面刷新积分数据
                    NotificationCenter.default.post(name: .pointsDidChange, object: nil)
                    print("🔔 TaskViewModel: 发送积分变化通知")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 计算属性
    
    var todayCompletedCount: Int {
        return todayTasks.filter { $0.status == .completed || $0.status == .approved }.count
    }
    
    var todayTotalCount: Int {
        return todayTasks.count
    }
    
    var todayCompletionRate: Double {
        guard todayTotalCount > 0 else { return 0 }
        return Double(todayCompletedCount) / Double(todayTotalCount)
    }
    
    var overdueCount: Int {
        return overdueTasks.count
    }
    
    var hasOverdueTasks: Bool {
        return overdueCount > 0
    }
    
    // MARK: - 辅助方法

    private func handleError(_ error: APIError) {
        errorMessage = error.localizedDescription
        showError = true
        print("❌ TaskViewModel: API错误 - \(error.localizedDescription)")
    }

    private func showSuccessMessage(_ message: String) {
        // 这里可以实现成功提示的逻辑
        // 例如显示Toast或者更新UI状态
        print("✅ \(message)")
    }

    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    func closeTaskDetail() {
        showTaskDetail = false
        selectedTask = nil
    }
    
    // MARK: - 任务筛选
    
    func getTasksByStatus(_ status: TaskStatus) -> [TaskSummary] {
        return todayTasks.filter { $0.status == status }
    }
    
    func getPendingTasks() -> [TaskSummary] {
        return getTasksByStatus(.pending)
    }
    
    func getInProgressTasks() -> [TaskSummary] {
        return getTasksByStatus(.inProgress)
    }
    
    func getCompletedTasks() -> [TaskSummary] {
        return todayTasks.filter { $0.status == .completed || $0.status == .approved }
    }
}

// MARK: - 扩展：任务统计
extension TaskViewModel {
    
    struct TaskStatistics {
        let totalTasks: Int
        let completedTasks: Int
        let inProgressTasks: Int
        let pendingTasks: Int
        let overdueTasks: Int
        let completionRate: Double
        
        var formattedCompletionRate: String {
            return String(format: "%.1f%%", completionRate * 100)
        }
    }
    
    var statistics: TaskStatistics {
        let completed = todayCompletedCount
        let inProgress = getInProgressTasks().count
        let pending = getPendingTasks().count
        let overdue = overdueCount
        let total = todayTotalCount
        
        return TaskStatistics(
            totalTasks: total,
            completedTasks: completed,
            inProgressTasks: inProgress,
            pendingTasks: pending,
            overdueTasks: overdue,
            completionRate: todayCompletionRate
        )
    }

    func getTodayEarnedPoints() -> Int {
        return getCompletedTasks().reduce(0) { total, task in
            total + task.basePoints
        }
    }

    // MARK: - 今日总用时计算

    /// 计算今日已完成任务的总用时（分钟）
    func getTodayTotalUsedMinutes() -> Int {
        return todayTasks.filter { task in
            // 只要不是未开始和进行中就算已用时
            task.status != .notStarted && task.status != .inProgress
        }.reduce(0) { total, task in
            // 使用实际用时，如果没有实际用时则使用预计用时
            let usedMinutes = task.actualMinutes ?? task.expectedMinutes
            return total + usedMinutes
        }
    }

    /// 格式化今日总用时显示
    var formattedTodayTotalUsedTime: String {
        let totalMinutes = getTodayTotalUsedMinutes()

        if totalMinutes == 0 {
            return "0分钟"
        }

        if totalMinutes < 60 {
            return "\(totalMinutes)分钟"
        } else {
            let hours = totalMinutes / 60
            let minutes = totalMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }
}
