-- 清理Flyway相关的数据库表和数据
-- 注意：执行此脚本前请备份数据库！

USE cps;

-- 1. 删除Flyway schema history表（如果存在）
DROP TABLE IF EXISTS flyway_schema_history;

-- 2. 可选：如果你想完全重新开始，可以删除所有表
-- 注意：这会删除所有数据！请谨慎使用
-- DROP TABLE IF EXISTS point_balance;
-- DROP TABLE IF EXISTS point_record;
-- DROP TABLE IF EXISTS reward_items;
-- DROP TABLE IF EXISTS reward_pools;
-- DROP TABLE IF EXISTS scheduled_task;
-- DROP TABLE IF EXISTS system_configs;
-- DROP TABLE IF EXISTS tasks;

-- 3. 查看当前数据库中的表
SHOW TABLES;
