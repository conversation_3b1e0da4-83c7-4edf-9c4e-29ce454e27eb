# 儿童奖励系统 - 一体化Docker构建脚本
param (
    [string]$Version = "1.0.0",
    [string]$Registry = "registry.cn-hangzhou.aliyuncs.com",
    [string]$Namespace = "zlean",
    [string]$Repository = "crs-system"
)

$IMAGE_NAME = "${Namespace}/${Repository}:${Version}"
$FULL_IMAGE_NAME = "${Registry}/${Namespace}/${Repository}:${Version}"

function Build-System {
    Write-Host "`n========================================" -ForegroundColor Green
    Write-Host "  儿童奖励系统 - 一体化构建" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "版本: $Version" -ForegroundColor Cyan
    Write-Host "镜像: $FULL_IMAGE_NAME" -ForegroundColor Cyan
    Write-Host ""
    
    # 1. 构建后端
    Write-Host "步骤1: 构建后端JAR包..." -ForegroundColor Yellow
    Set-Location backend
    mvn clean package -DskipTests -q
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: 后端构建失败" -ForegroundColor Red
        return $false
    }
    Set-Location ..
    Write-Host "✅ 后端构建成功" -ForegroundColor Green
    
    # 2. 构建前端
    Write-Host "`n步骤2: 构建前端..." -ForegroundColor Yellow
    Set-Location frontend
    if (!(Test-Path "node_modules")) {
        Write-Host "安装前端依赖..." -ForegroundColor Cyan
        npm install --registry=https://registry.npmmirror.com --silent
    }
    npm run build --silent
    if (!(Test-Path "dist")) {
        Write-Host "ERROR: 前端构建失败" -ForegroundColor Red
        return $false
    }
    Set-Location ..
    Write-Host "✅ 前端构建成功" -ForegroundColor Green
    
    # 3. 构建Docker镜像
    Write-Host "`n步骤3: 构建Docker镜像..." -ForegroundColor Yellow
    Write-Host "执行: docker build -f Dockerfile.all-in-one -t $IMAGE_NAME ." -ForegroundColor Cyan
    docker build --progress=plain -f Dockerfile.all-in-one -t $IMAGE_NAME .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Docker镜像构建失败" -ForegroundColor Red
        return $false
    }
    Write-Host "✅ Docker镜像构建成功" -ForegroundColor Green
    
    # 4. 标记镜像
    Write-Host "`n步骤4: 标记镜像..." -ForegroundColor Yellow
    docker tag $IMAGE_NAME $FULL_IMAGE_NAME
    Write-Host "✅ 镜像标记完成: $FULL_IMAGE_NAME" -ForegroundColor Green
    
    # 5. 推送镜像 (可选)
    Write-Host "`n步骤5: 推送镜像到仓库..." -ForegroundColor Yellow
    $push = Read-Host "是否推送到远程仓库? (y/N)"
    if ($push -eq "y" -or $push -eq "Y") {
        docker push $FULL_IMAGE_NAME
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 镜像推送成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 镜像推送失败" -ForegroundColor Red
        }
    } else {
        Write-Host "⏭️  跳过镜像推送" -ForegroundColor Yellow
    }
    
    return $true
}

function Show-Results {
    Write-Host "`n========================================" -ForegroundColor Green
    Write-Host "  构建完成!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    # 显示镜像信息
    Write-Host "`n📦 镜像信息:" -ForegroundColor Cyan
    docker images | Select-String $Repository | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    
    Write-Host "`n🚀 本地测试:" -ForegroundColor Cyan
    Write-Host "  docker run -d -p 8080:80 --name crs-test $IMAGE_NAME" -ForegroundColor White
    Write-Host "  访问: http://localhost:8080" -ForegroundColor White
    
    Write-Host "`n🏠 NAS部署:" -ForegroundColor Cyan
    Write-Host "  1. 上传 docker-compose-single.yml 到NAS" -ForegroundColor White
    Write-Host "  2. 修改环境变量 (数据库配置)" -ForegroundColor White
    Write-Host "  3. 运行: docker-compose -f docker-compose-single.yml up -d" -ForegroundColor White
    
    Write-Host "`n📊 容器管理:" -ForegroundColor Cyan
    Write-Host "  启动: docker-compose -f docker-compose-single.yml up -d" -ForegroundColor White
    Write-Host "  停止: docker-compose -f docker-compose-single.yml down" -ForegroundColor White
    Write-Host "  日志: docker-compose -f docker-compose-single.yml logs -f" -ForegroundColor White
    Write-Host "  状态: docker-compose -f docker-compose-single.yml ps" -ForegroundColor White
    
    Write-Host "`n🌐 访问地址:" -ForegroundColor Cyan
    Write-Host "  家长端: http://NAS_IP:8080" -ForegroundColor White
    Write-Host "  儿童端: http://NAS_IP:8080/child" -ForegroundColor White
    Write-Host "  健康检查: http://NAS_IP:8080/actuator/health" -ForegroundColor White
    
    Write-Host "`n💡 特性:" -ForegroundColor Cyan
    Write-Host "  ✅ 单一容器部署" -ForegroundColor Green
    Write-Host "  ✅ Nginx + Spring Boot 一体化" -ForegroundColor Green
    Write-Host "  ✅ 自动重启和健康检查" -ForegroundColor Green
    Write-Host "  ✅ NAS内存优化 (256MB-512MB)" -ForegroundColor Green
    Write-Host "  ✅ 统一端口访问 (80端口)" -ForegroundColor Green
}

# 主执行逻辑
if (Build-System) {
    Show-Results
} else {
    Write-Host "`n❌ 构建失败，请检查错误信息" -ForegroundColor Red
}