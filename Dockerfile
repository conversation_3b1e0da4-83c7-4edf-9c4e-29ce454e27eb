FROM docker.m.daocloud.io/openjdk:21-jdk-slim

WORKDIR /app

# 复制JAR文件
COPY backend/target/childreward-0.0.1-SNAPSHOT.jar /app/app.jar
COPY docker-entrypoint.sh /app/docker-entrypoint.sh

# 处理脚本，并更换为国内镜像源以加速，同时安装字体库
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && \
    apt-get install -y --no-install-recommends fontconfig libfreetype6 dos2unix && \
    dos2unix /app/docker-entrypoint.sh && \
    chmod +x /app/docker-entrypoint.sh && \
    rm -rf /var/lib/apt/lists/*

# 环境变量
ENV SPRING_PROFILES_ACTIVE=prod
ENV MYSQL_HOST=*************
ENV MYSQL_PORT=3307
ENV MYSQL_DATABASE=crs
ENV MYSQL_USERNAME=root
ENV MYSQL_PASSWORD=123456

# 暴露端口
EXPOSE 18080

# 启动命令
ENTRYPOINT ["/app/docker-entrypoint.sh"]