import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { childApi } from '../../api/childApi';
import { createChildApiClient } from '../../api/apiConfig';
import { toast } from '../../components/common/Toast';

const PageContainer = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 32px;
  color: white;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
`;

const Subtitle = styled.p`
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
`;

const PointsDisplay = styled(motion.div)`
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 16px 24px;
  margin: 24px auto;
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-size: 20px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
`;

const CategoryFilter = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  justify-content: center;
`;

const CategoryButton = styled(motion.button)`
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.active ? `
    background: white;
    color: #667eea;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  ` : `
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
  `}
`;

const ItemsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 24px;
`;

const ItemCard = styled(motion.div)`
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.6 : 1};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b35, #f7931e);
  }
`;

const ItemIcon = styled.div`
  font-size: 48px;
  text-align: center;
  margin-bottom: 16px;
`;

const ItemName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  text-align: center;
`;

const ItemDescription = styled.p`
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
  text-align: center;
`;

const ItemFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
`;

const PointsRequired = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  color: #ff6b35;
  font-size: 16px;
`;

const Stock = styled.div`
  font-size: 12px;
  color: #999;
`;

const ExchangeButton = styled(motion.button)`
  width: 100%;
  padding: 12px;
  border-radius: 12px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  
  ${props => props.disabled ? `
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  ` : `
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
  `}
`;

const Modal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled(motion.div)`
  background: white;
  border-radius: 20px;
  padding: 32px;
  width: 90%;
  max-width: 400px;
  text-align: center;
`;

const ModalIcon = styled.div`
  font-size: 64px;
  margin-bottom: 16px;
`;

const ModalTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
`;

const ModalDescription = styled.p`
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  margin: 0 0 24px 0;
`;

const ModalActions = styled.div`
  display: flex;
  gap: 12px;
`;

const ModalButton = styled(motion.button)`
  flex: 1;
  padding: 12px;
  border-radius: 12px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  
  ${props => props.variant === 'primary' && `
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
  `}
  
  ${props => props.variant === 'secondary' && `
    background: #f5f5f5;
    color: #666;
  `}
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: white;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: white;
`;

const ExchangeStore = () => {
  const [items, setItems] = useState([]);
  const [currentPoints, setCurrentPoints] = useState(0);
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [exchanging, setExchanging] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchItems();
  }, [selectedCategory]);

  const fetchData = async () => {
    try {
      // 获取当前积分
      const pointsResponse = await childApi.getPointBalance();
      if (pointsResponse && pointsResponse.data) {
        setCurrentPoints(pointsResponse.data.totalPoints || 0);
      }

      // 获取分类
      const client = createChildApiClient();
      const categoriesResponse = await client.get('/exchange/categories');
      if (categoriesResponse && categoriesResponse.data) {
        setCategories(categoriesResponse.data);
      }

      // 获取商品
      await fetchItems();
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchItems = async () => {
    try {
      const client = createChildApiClient();
      const url = selectedCategory
        ? `/exchange/items?category=${encodeURIComponent(selectedCategory)}`
        : '/exchange/items';

      const response = await client.get(url);
      if (response && response.data) {
        setItems(response.data);
      }
    } catch (error) {
      console.error('获取兑换商品失败:', error);
    }
  };

  const getItemIcon = (category) => {
    switch (category) {
      case '游戏时间': return '🎮';
      case '特权': return '👑';
      case '实物奖励': return '🎁';
      case '零花钱': return '💰';
      default: return '⭐';
    }
  };

  const handleExchange = (item) => {
    // 检查是否可以兑换（积分足够且有库存）
    if (!canAfford(item.requiredPoints) || !hasStock(item)) {
      return; // 积分不足或无库存时不执行任何操作
    }

    setSelectedItem(item);
    setShowModal(true);
  };

  const confirmExchange = async () => {
    if (!selectedItem || exchanging) return;

    setExchanging(true);
    try {
      const client = createChildApiClient();
      const response = await client.post(`/exchange/exchange/${selectedItem.id}`);

      if (response && response.data) {
        toast.success(`🎉 兑换成功！获得：${selectedItem.name}`, 4000);
        setShowModal(false);
        // 刷新数据
        fetchData();
      } else {
        toast.error('兑换失败，请重试');
      }
    } catch (error) {
      console.error('兑换失败:', error);
      if (error.response && error.response.data && error.response.data.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error('兑换失败，请重试');
      }
    } finally {
      setExchanging(false);
    }
  };

  const canAfford = (requiredPoints) => {
    return currentPoints >= requiredPoints;
  };

  const hasStock = (item) => {
    return item.stock === -1 || item.stock > 0;
  };

  if (loading) {
    return (
      <PageContainer>
        <LoadingSpinner>加载中...</LoadingSpinner>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <Header>
        <Title>🎁 兑换商店</Title>
        <Subtitle>用积分兑换你喜欢的奖励吧！</Subtitle>
      </Header>

      <PointsDisplay
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <span>⭐</span>
        <span>当前积分：{currentPoints}</span>
      </PointsDisplay>

      <CategoryFilter>
        <CategoryButton
          active={selectedCategory === ''}
          onClick={() => setSelectedCategory('')}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          全部
        </CategoryButton>
        {categories.map((category) => (
          <CategoryButton
            key={category}
            active={selectedCategory === category}
            onClick={() => setSelectedCategory(category)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {category}
          </CategoryButton>
        ))}
      </CategoryFilter>

      {items.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎁</div>
          <h3>暂无可兑换的商品</h3>
          <p>请稍后再来看看吧！</p>
        </EmptyState>
      ) : (
        <ItemsGrid>
          <AnimatePresence>
            {items.map((item) => {
              const isDisabled = !canAfford(item.requiredPoints) || !hasStock(item);
              return (
                <ItemCard
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  whileHover={!isDisabled ? { scale: 1.02, y: -4 } : {}}
                  onClick={() => handleExchange(item)}
                  disabled={isDisabled}
                >
                  <ItemIcon>{getItemIcon(item.category)}</ItemIcon>
                  <ItemName>{item.name}</ItemName>
                  <ItemDescription>{item.description}</ItemDescription>

                  <ItemFooter>
                    <PointsRequired>
                      <span>⭐</span>
                      {item.requiredPoints}
                    </PointsRequired>
                    <Stock>
                      {item.stock === -1 ? '无限库存' : `库存 ${item.stock}`}
                    </Stock>
                  </ItemFooter>

                  <ExchangeButton
                    disabled={!canAfford(item.requiredPoints) || !hasStock(item)}
                    whileHover={canAfford(item.requiredPoints) && hasStock(item) ? { scale: 1.02 } : {}}
                    whileTap={canAfford(item.requiredPoints) && hasStock(item) ? { scale: 0.98 } : {}}
                  >
                    {!hasStock(item) ? '缺货' :
                     !canAfford(item.requiredPoints) ? '积分不足' : '立即兑换'}
                  </ExchangeButton>
                </ItemCard>
              );
            })}
          </AnimatePresence>
        </ItemsGrid>
      )}

      <AnimatePresence>
        {showModal && selectedItem && (
          <Modal
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => e.target === e.currentTarget && setShowModal(false)}
          >
            <ModalContent
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <ModalIcon>{getItemIcon(selectedItem.category)}</ModalIcon>
              <ModalTitle>确认兑换</ModalTitle>
              <ModalDescription>
                你确定要用 <strong>{selectedItem.requiredPoints} 积分</strong> 兑换<br/>
                <strong>"{selectedItem.name}"</strong> 吗？
              </ModalDescription>

              <ModalActions>
                <ModalButton
                  variant="secondary"
                  onClick={() => setShowModal(false)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  取消
                </ModalButton>
                <ModalButton
                  variant="primary"
                  onClick={confirmExchange}
                  disabled={exchanging}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {exchanging ? '兑换中...' : '确认兑换'}
                </ModalButton>
              </ModalActions>
            </ModalContent>
          </Modal>
        )}
      </AnimatePresence>
    </PageContainer>
  );
};

export default ExchangeStore;
