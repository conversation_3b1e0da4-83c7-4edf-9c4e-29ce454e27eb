# CRS 构建说明

## 快速构建

### 一键构建所有包
```powershell
.\build-package.ps1
```

生成文件：
- `packages/crs-backend.jar` - 后端服务JAR包
- `packages/crs-frontend.zip` - 前端静态文件ZIP包

### 单独构建后端
```batch
cd backend
.\build-simple.bat
```

### 单独构建前端
```batch
cd frontend
.\build-simple.bat
```

## 部署说明

### 后端部署
1. 确保已安装Java 21或更高版本
2. 运行JAR包：
   ```bash
   java -jar crs-backend.jar
   ```
3. 或指定配置文件：
   ```bash
   java -jar -Dspring.profiles.active=prod crs-backend.jar
   ```

### 前端部署
1. 解压 `crs-frontend.zip` 到Web服务器目录
2. 配置Web服务器支持SPA路由（如nginx的try_files）
3. 确保前端能够访问后端API地址

## 配置说明

### 数据库配置
修改 `backend/src/main/resources/application-prod.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: *****************************************
    username: your-username
    password: your-password
```

### 前端API配置
前端默认连接到 `http://localhost:18080`，生产环境需要修改API地址。

## 注意事项
1. 首次启动会自动创建数据库表结构
2. 默认家长PIN码: 0907
3. 建议在生产环境中修改默认配置
4. 确保MySQL数据库已创建并可访问
