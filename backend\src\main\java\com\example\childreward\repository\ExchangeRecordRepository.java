package com.example.childreward.repository;

import com.example.childreward.entity.ExchangeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 兑换记录Repository
 */
@Repository
public interface ExchangeRecordRepository extends JpaRepository<ExchangeRecord, Long> {
    
    /**
     * 按兑换时间倒序查找所有兑换记录
     */
    List<ExchangeRecord> findAllByOrderByExchangeTimeDesc();
    
    /**
     * 按状态查找兑换记录
     */
    List<ExchangeRecord> findByStatusOrderByExchangeTimeDesc(ExchangeRecord.ExchangeStatus status);
    
    /**
     * 查找指定时间范围内的兑换记录
     */
    List<ExchangeRecord> findByExchangeTimeBetweenOrderByExchangeTimeDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找未使用的兑换记录
     */
    List<ExchangeRecord> findByStatusOrderByExchangeTimeAsc(ExchangeRecord.ExchangeStatus status);
    
    /**
     * 统计今日兑换次数
     */
    @Query("SELECT COUNT(e) FROM ExchangeRecord e WHERE DATE(e.exchangeTime) = CURRENT_DATE")
    Long countTodayExchanges();
    
    /**
     * 统计指定商品的兑换次数
     */
    Long countByExchangeItemId(Long exchangeItemId);
    
    /**
     * 查找过期的兑换记录
     */
    @Query("SELECT e FROM ExchangeRecord e WHERE e.status = 'UNUSED' AND e.expireTime IS NOT NULL AND e.expireTime < :now")
    List<ExchangeRecord> findExpiredRecords(@Param("now") LocalDateTime now);
}
