import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Canvas, useFrame } from '@react-three/fiber';
import { Float, Stars } from '@react-three/drei';
import { childTheme } from '../../utils/themes';

// 3D背景浮动元素
const FloatingObject = ({ position, size, color, speed, rotationFactor }) => {
  const mesh = useRef();
  
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    mesh.current.rotation.x = Math.sin(time * speed * 0.4) * rotationFactor;
    mesh.current.rotation.y = Math.cos(time * speed * 0.7) * rotationFactor;
    mesh.current.position.y = position[1] + Math.sin(time * speed) * 0.3;
  });
  
  return (
    <mesh ref={mesh} position={position}>
      <boxGeometry args={size} />
      <meshStandardMaterial color={color} transparent opacity={0.7} />
    </mesh>
  );
};

// 星星组件
const Star = ({ position, size, color, speed }) => {
  const mesh = useRef();
  
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    mesh.current.scale.x = Math.abs(Math.sin(time * speed * 0.5)) * size + size * 0.5;
    mesh.current.scale.y = Math.abs(Math.sin(time * speed * 0.5)) * size + size * 0.5;
    mesh.current.scale.z = Math.abs(Math.sin(time * speed * 0.5)) * size + size * 0.5;
  });
  
  return (
    <mesh ref={mesh} position={position}>
      <octahedronGeometry args={[1, 0]} />
      <meshStandardMaterial color={color} emissive={color} emissiveIntensity={0.5} />
    </mesh>
  );
};

// 3D场景
const Scene = () => {
  return (
    <>
      <ambientLight intensity={0.3} />
      <directionalLight position={[10, 10, 5]} intensity={0.5} />
      
      <Stars radius={50} depth={50} count={1000} factor={4} fade speed={1} />
      
      <Float speed={2} rotationIntensity={0.5} floatIntensity={1}>
        <FloatingObject 
          position={[-3, 0, -5]} 
          size={[0.5, 0.5, 0.5]} 
          color={childTheme.primaryColor} 
          speed={0.5}
          rotationFactor={0.3}
        />
      </Float>
      
      <Float speed={1.5} rotationIntensity={0.7} floatIntensity={1.2}>
        <FloatingObject 
          position={[4, 2, -7]} 
          size={[0.7, 0.7, 0.7]} 
          color={childTheme.secondaryColor} 
          speed={0.7}
          rotationFactor={0.5}
        />
      </Float>
      
      <Float speed={2.2} rotationIntensity={0.3} floatIntensity={0.8}>
        <Star 
          position={[-5, 3, -10]} 
          size={0.3}
          color="#FFD700" 
          speed={1.2}
        />
      </Float>
      
      <Float speed={1.8} rotationIntensity={0.4} floatIntensity={1}>
        <Star 
          position={[6, -2, -8]} 
          size={0.2}
          color="#FF6B6B" 
          speed={0.9}
        />
      </Float>
    </>
  );
};

// 主背景组件
const ChildBackground = () => {
  return (
    <BackgroundContainer>
      <GradientOverlay />
      <CanvasContainer>
        <Canvas camera={{ position: [0, 0, 10], fov: 60 }}>
          <Scene />
        </Canvas>
      </CanvasContainer>
      <BubbleContainer>
        {Array.from({ length: 15 }).map((_, i) => (
          <Bubble 
            key={i} 
            $size={Math.random() * 50 + 20} 
            $left={Math.random() * 100} 
            $animationDuration={Math.random() * 20 + 15}
            $delay={Math.random() * 10}
          />
        ))}
      </BubbleContainer>
    </BackgroundContainer>
  );
};

const BackgroundContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
`;

const GradientOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(240, 249, 255, 0.7) 0%, rgba(229, 247, 255, 0.7) 50%, rgba(214, 240, 253, 0.7) 100%);
  z-index: 1;
`;

const CanvasContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
`;

const BubbleContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
`;

const Bubble = styled.div`
  position: absolute;
  bottom: -100px;
  left: ${props => props.$left}%;
  width: ${props => props.$size}px;
  height: ${props => props.$size}px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.1));
  animation: float ${props => props.$animationDuration}s ease-in-out infinite;
  animation-delay: ${props => props.$delay}s;
  
  @keyframes float {
    0% { transform: translateY(0); opacity: 0; }
    10% { opacity: 0.5; }
    50% { transform: translateY(-${window.innerHeight * 1.2}px); }
    100% { transform: translateY(-${window.innerHeight * 1.5}px); opacity: 0; }
  }
`;

export default ChildBackground; 