import SwiftUI
import Combine

// 简单的网络测试视图
struct TestNetworkView: View {
    @State private var testResult = "未开始测试"
    @State private var isLoading = false
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        VStack(spacing: 20) {
            Text("网络连接测试")
                .font(.title)
                .fontWeight(.bold)
            
            Text(testResult)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            
            if isLoading {
                ProgressView("测试中...")
            } else {
                Button("开始测试") {
                    testNetworkConnection()
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding()
    }
    
    private func testNetworkConnection() {
        isLoading = true
        testResult = "正在测试网络连接..."
        
        // 测试API连接
        let apiService = ChildAPIService.shared
        
        print("🧪 开始网络测试")
        print("🌐 API基础URL: \(APIConfig.baseURL)")
        
        apiService.getTodayTasks()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    switch completion {
                    case .finished:
                        print("✅ 网络请求完成")
                    case .failure(let error):
                        print("❌ 网络请求失败: \(error)")
                        self.testResult = "❌ 网络请求失败:\n\(error.localizedDescription)"
                    }
                },
                receiveValue: { tasks in
                    print("✅ 成功获取 \(tasks.count) 个任务")
                    self.testResult = "✅ 网络连接成功!\n获取到 \(tasks.count) 个任务"
                    
                    // 打印前3个任务的详细信息
                    for (index, task) in tasks.prefix(3).enumerated() {
                        print("📋 任务\(index + 1): \(task.title) - 状态: \(task.status.rawValue)")
                    }
                }
            )
            .store(in: &cancellables)
    }
}

#Preview {
    TestNetworkView()
}
