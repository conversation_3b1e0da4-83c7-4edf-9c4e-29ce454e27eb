# 今日任务总用时显示功能

## 功能描述
在"今日任务"页面的右边新增一个字段，显示"当前已用时：xx小时xx分钟"，让用户能清楚知道今天完成任务已经总共用了多少时间。

## 实现位置
1. **主要显示位置**: `ContentView.swift` 中的任务页面头部工具栏
2. **备用显示位置**: `TaskStatisticsCard.swift` 中的统计卡片（如果使用该组件）

## 修改的文件

### 1. ContentView.swift
- **位置**: 任务页面头部工具栏（第1140-1187行）
- **修改内容**:
  - 在"今日任务"标题右边添加了"当前已用时"显示区域
  - 添加了计算今日总用时的方法 `todayTotalUsedMinutes`
  - 添加了格式化显示的方法 `formattedTodayTotalUsedTime`

### 2. TaskViewModel.swift
- **位置**: 扩展部分（第291-323行）
- **修改内容**:
  - 添加了 `getTodayTotalUsedMinutes()` 方法计算今日已完成任务的总用时
  - 添加了 `formattedTodayTotalUsedTime` 计算属性格式化时间显示

### 3. TaskStatisticsCard.swift
- **位置**: 统计信息区域（第47-67行）
- **修改内容**:
  - 在"今日任务"统计项右边添加了"当前已用时"显示

## 功能逻辑

### 计算规则
1. **筛选任务**: 只计算状态为 `completed` 或 `approved` 的任务
2. **时间计算**: 
   - 优先使用 `actualMinutes`（实际用时）
   - 如果没有实际用时，则使用 `expectedMinutes`（预计用时）
3. **累加**: 将所有符合条件的任务用时相加

### 显示格式
- **0分钟**: 显示 "0分钟"
- **小于60分钟**: 显示 "xx分钟"
- **大于等于60分钟**: 
  - 整小时: 显示 "x小时"
  - 有余分钟: 显示 "x小时x分钟"

## UI设计
- **标题**: "当前已用时"（灰色，11pt字体）
- **时间**: 橙色高亮显示（13pt粗体字体）
- **位置**: 在"今日任务"标题右边，刷新按钮左边
- **对齐**: 右对齐

## 数据源
- 使用 `tasks` 数组中的 `TaskSummary` 对象
- 每个任务包含 `actualMinutes` 和 `expectedMinutes` 字段
- 任务状态通过 `status` 字段判断

## 更新机制
- 当任务数据更新时，总用时会自动重新计算
- 支持实时刷新，无需手动触发

## 测试建议
1. 创建几个测试任务，设置不同的预计用时
2. 完成部分任务，验证总用时计算是否正确
3. 测试不同时间格式的显示（分钟、小时、小时+分钟）
4. 验证只有已完成的任务才被计入总用时
