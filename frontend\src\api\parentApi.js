import { createParentApiClient } from './apiConfig.js';

// 家长端专用API服务
export const parentApi = {
  // 任务相关
  tasks: {
    // 获取今日任务
    getToday: () => {
      const client = createParentApiClient();
      return client.get('/tasks/today');
    },

    // 获取待审批任务
    getPending: () => {
      const client = createParentApiClient();
      return client.get('/tasks/pending');
    },

    // 获取指定日期任务
    getByDate: (date) => {
      const client = createParentApiClient();
      return client.get(`/tasks/date/${date}`);
    },

    // 创建任务
    create: (taskData) => {
      const client = createParentApiClient();
      return client.post('/tasks', taskData);
    },

    // 更新任务
    update: (taskId, taskData) => {
      const client = createParentApiClient();
      return client.put(`/tasks/${taskId}`, taskData);
    },

    // 删除任务
    delete: (taskId) => {
      const client = createParentApiClient();
      return client.delete(`/tasks/${taskId}`);
    },

    // 审批任务
    approve: (taskId, actualPoints) => {
      const client = createParentApiClient();
      return client.post(`/tasks/${taskId}/approve`, null, {
        params: { actualPoints }
      });
    },

    // 拒绝任务
    reject: (taskId, reason) => {
      const client = createParentApiClient();
      return client.post(`/tasks/${taskId}/reject`, null, {
        params: { reason }
      });
    }
  },

  // 积分相关
  points: {
    // 获取总积分
    getTotal: () => {
      const client = createParentApiClient();
      return client.get('/points/total');
    },

    // 获取今日积分变化
    getTodayChange: () => {
      const client = createParentApiClient();
      return client.get('/points/today');
    },

    // 获取积分记录
    getRecords: (startTime, endTime) => {
      const client = createParentApiClient();
      
      if (!startTime || !endTime) {
        return client.get('/points/records');
      }
      
      return client.get('/points/records', {
        params: {
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString()
        }
      });
    },

    // 获取最近积分记录
    getRecent: (days = 7) => {
      const client = createParentApiClient();
      return client.get(`/points/recent?days=${days}`);
    },

    // 手动加分
    add: (points, description) => {
      const client = createParentApiClient();
      return client.post('/points/add', null, { 
        params: { points, description } 
      });
    },

    // 手动扣分
    deduct: (points, description) => {
      const client = createParentApiClient();
      return client.post('/points/deduct', null, { 
        params: { points, description } 
      });
    }
  },

  // 奖励相关
  rewards: {
    // 获取所有奖品池
    getPools: () => {
      const client = createParentApiClient();
      return client.get('/rewards/pools');
    },

    // 获取奖品池详情
    getPool: (poolId) => {
      const client = createParentApiClient();
      return client.get(`/rewards/pools/${poolId}`);
    },

    // 创建奖品池
    createPool: (poolData) => {
      const client = createParentApiClient();
      return client.post('/rewards/pools', poolData);
    },

    // 更新奖品池
    updatePool: (poolId, poolData) => {
      const client = createParentApiClient();
      return client.put(`/rewards/pools/${poolId}`, poolData);
    },

    // 删除奖品池
    deletePool: (poolId) => {
      const client = createParentApiClient();
      return client.delete(`/rewards/pools/${poolId}`);
    }
  },

  // 系统配置相关
  config: {
    // 获取所有配置
    getAll: () => {
      const client = createParentApiClient();
      return client.get('/configs');
    },

    // 获取配置详情
    get: (key) => {
      const client = createParentApiClient();
      return client.get(`/configs/${key}`);
    },

    // 更新配置
    update: (key, value, description) => {
      const client = createParentApiClient();
      return client.put(`/configs/${key}`, { value, description });
    }
  }
};
