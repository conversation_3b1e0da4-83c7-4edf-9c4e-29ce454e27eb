-- 修复数据库表结构，添加缺失的字段
-- 执行此脚本来修复任务生成问题

-- 1. 为task表添加scheduled_date字段
ALTER TABLE task ADD COLUMN scheduled_date DATE COMMENT '计划日期';

-- 2. 为task表添加task_type字段（如果不存在）
ALTER TABLE task ADD COLUMN task_type VARCHAR(20) DEFAULT 'REWARD' COMMENT '任务类型';

-- 3. 为task表添加description字段（如果不存在）
ALTER TABLE task ADD COLUMN description TEXT COMMENT '任务描述';

-- 4. 为task表添加created_time字段（如果不存在，替换created_at）
ALTER TABLE task ADD COLUMN created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 5. 添加索引以提高查询性能
CREATE INDEX idx_task_scheduled_date ON task(scheduled_date);
CREATE INDEX idx_task_source_template_scheduled ON task(source_template_id, scheduled_date);
CREATE INDEX idx_task_status ON task(status);
CREATE INDEX idx_task_scheduled_date_status ON task(scheduled_date, status);

-- 6. 验证表结构
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'crs' AND TABLE_NAME = 'task'
-- ORDER BY ORDINAL_POSITION;
