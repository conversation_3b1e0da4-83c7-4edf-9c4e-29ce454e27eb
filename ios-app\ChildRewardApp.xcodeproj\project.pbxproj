// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		X1QDWC255ZS4WF1IV5A93LP3 /* SpinWheelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = PIBB920PMD3DX4LLUQ5IZV1I /* SpinWheelView.swift */; };
		74PXLE8UMGHO2POXVX7WSVH9 /* PenaltyModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = WVSCMWLRNBNTHGBCXTZ8ZXUN /* PenaltyModels.swift */; };
		AOK9TQ8KIA6S38KMGKEW6DLS /* EnhancedRewardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = XKHO86I65IEN749XGPL3LNHP /* EnhancedRewardView.swift */; };
		D2206294A8C54D85A4BCECFA /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34B81E9D14BC4B45AE7AB029 /* SettingsView.swift */; };
		4A3963440EB64796B9B22FAC /* ScreenDimmingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7159DD11A7EF466783E7CE8D /* ScreenDimmingManager.swift */; };
		8E8C5736BBCB4BB0B265361A /* SettingsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6EAACAF4C714271BD2D9761 /* SettingsManager.swift */; };
		******** /* ChildRewardApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******** /* ChildRewardApp.swift */; };
		******** /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******** /* ContentView.swift */; };
		******** /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = ******** /* Assets.xcassets */; };
		******** /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000008 /* Preview Assets.xcassets */; };
		A1000020 /* EnvironmentManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000021 /* EnvironmentManager.swift */; };
		A1000030 /* APIConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000031 /* APIConfig.swift */; };
		A1000032 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000033 /* NetworkManager.swift */; };
		A1000034 /* ChildAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000035 /* ChildAPIService.swift */; };
		A1000036 /* Task.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000037 /* Task.swift */; };
		A1000038 /* Point.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000039 /* Point.swift */; };
		A1000050 /* HTMLTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000051 /* HTMLTextView.swift */; };
		A1000053 /* RewardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000052 /* RewardViewModel.swift */; };
		852B878FFAE04D14BCC25B00 /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 632160B9F69E4A04A2921B89 /* ImagePicker.swift */; };
		43B8EDF63CE944AAA2C3113D /* AvatarManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = B93F36FED59B4EC5978F8D5A /* AvatarManager.swift */; };
		TASKPROGRESS001 /* TaskProgressBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = TASKPROGRESS002 /* TaskProgressBar.swift */; };
		SEGMENTPROGRESS001 /* SegmentedProgressBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = SEGMENTPROGRESS002 /* SegmentedProgressBar.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		PIBB920PMD3DX4LLUQ5IZV1I /* SpinWheelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Views/Components/SpinWheelView.swift"; sourceTree = "<group>"; };
		WVSCMWLRNBNTHGBCXTZ8ZXUN /* PenaltyModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Models/PenaltyModels.swift"; sourceTree = "<group>"; };
		XKHO86I65IEN749XGPL3LNHP /* EnhancedRewardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Views/EnhancedRewardView.swift"; sourceTree = "<group>"; };
		34B81E9D14BC4B45AE7AB029 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Views/Settings/SettingsView.swift"; sourceTree = "<group>"; };
		7159DD11A7EF466783E7CE8D /* ScreenDimmingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Services/ScreenDimmingManager.swift"; sourceTree = "<group>"; };
		D6EAACAF4C714271BD2D9761 /* SettingsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Services/SettingsManager.swift"; sourceTree = "<group>"; };
		A1000009 /* 作业小助手.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "作业小助手.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		******** /* ChildRewardApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChildRewardApp.swift; sourceTree = "<group>"; };
		******** /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		******** /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1000008 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1000021 /* EnvironmentManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnvironmentManager.swift; sourceTree = "<group>"; };
		A1000031 /* APIConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIConfig.swift; sourceTree = "<group>"; };
		A1000033 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		A1000035 /* ChildAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChildAPIService.swift; sourceTree = "<group>"; };
		A1000037 /* Task.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Task.swift; sourceTree = "<group>"; };
		A1000039 /* Point.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Point.swift; sourceTree = "<group>"; };
		A1000051 /* HTMLTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HTMLTextView.swift; sourceTree = "<group>"; };
		A1000052 /* RewardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/ViewModels/RewardViewModel.swift"; sourceTree = "<group>"; };
		A1000010 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		632160B9F69E4A04A2921B89 /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Views/ImagePicker.swift"; sourceTree = "<group>"; };
		B93F36FED59B4EC5978F8D5A /* AvatarManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Services/AvatarManager.swift"; sourceTree = "<group>"; };
		TASKPROGRESS002 /* TaskProgressBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Views/Components/TaskProgressBar.swift"; sourceTree = "<group>"; };
		SEGMENTPROGRESS002 /* SegmentedProgressBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ChildRewardApp/Views/Components/SegmentedProgressBar.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A100000A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A100000B /* ChildRewardApp */ = {
			isa = PBXGroup;
			children = (
				******** /* ChildRewardApp.swift */,
				******** /* ContentView.swift */,
				A1000051 /* HTMLTextView.swift */,
				A1000022 /* Services */,
				A1000040 /* Models */,
				******** /* Assets.xcassets */,
				A1000010 /* Info.plist */,
				A100000D /* Preview Content */,
			);
			path = ChildRewardApp;
			sourceTree = "<group>";
		};
		A100000C /* Products */ = {
			isa = PBXGroup;
			children = (
				A1000009 /* 作业小助手.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A100000D /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1000008 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A100000E = {
			isa = PBXGroup;
			children = (
				A100000B /* ChildRewardApp */,
				A100000C /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000022 /* Services */ = {
			isa = PBXGroup;
			children = (
				A1000021 /* EnvironmentManager.swift */,
				A1000031 /* APIConfig.swift */,
				A1000033 /* NetworkManager.swift */,
				A1000035 /* ChildAPIService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1000040 /* Models */ = {
			isa = PBXGroup;
			children = (
				A1000037 /* Task.swift */,
				A1000039 /* Point.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A100000F /* ChildRewardApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = ******** /* Build configuration list for PBXNativeTarget "ChildRewardApp" */;
			buildPhases = (
				A1000011 /* Sources */,
				A100000A /* Frameworks */,
				A1000013 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ChildRewardApp;
			productName = ChildRewardApp;
			productReference = A1000009 /* 作业小助手.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1000014 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					A100000F = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = ******** /* Build configuration list for PBXProject "ChildRewardApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A100000E;
			productRefGroup = A100000C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A100000F /* ChildRewardApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1000013 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				******** /* Preview Assets.xcassets in Resources */,
				******** /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1000011 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				******** /* ContentView.swift in Sources */,
				******** /* ChildRewardApp.swift in Sources */,
				A1000050 /* HTMLTextView.swift in Sources */,
				A1000053 /* RewardViewModel.swift in Sources */,
				A1000020 /* EnvironmentManager.swift in Sources */,
				A1000030 /* APIConfig.swift in Sources */,
				A1000032 /* NetworkManager.swift in Sources */,
				A1000034 /* ChildAPIService.swift in Sources */,
				A1000036 /* Task.swift in Sources */,
				A1000038 /* Point.swift in Sources */,
				852B878FFAE04D14BCC25B00 /* ImagePicker.swift in Sources */,
				43B8EDF63CE944AAA2C3113D /* AvatarManager.swift in Sources */,
				8E8C5736BBCB4BB0B265361A /* SettingsManager.swift in Sources */,
				4A3963440EB64796B9B22FAC /* ScreenDimmingManager.swift in Sources */,
				D2206294A8C54D85A4BCECFA /* SettingsView.swift in Sources */,
				X1QDWC255ZS4WF1IV5A93LP3 /* SpinWheelView.swift in Sources */,
				74PXLE8UMGHO2POXVX7WSVH9 /* PenaltyModels.swift in Sources */,
				AOK9TQ8KIA6S38KMGKEW6DLS /* EnhancedRewardView.swift in Sources */,
				TASKPROGRESS001 /* TaskProgressBar.swift in Sources */,
				SEGMENTPROGRESS001 /* SegmentedProgressBar.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		******** /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		******** /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		******** /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ChildRewardApp/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = ChildRewardApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.childreward.ChildRewardApp;
				PRODUCT_NAME = "作业小助手";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		******** /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ChildRewardApp/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = ChildRewardApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.childreward.ChildRewardApp;
				PRODUCT_NAME = "作业小助手";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		******** /* Build configuration list for PBXNativeTarget "ChildRewardApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				******** /* Debug */,
				******** /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		******** /* Build configuration list for PBXProject "ChildRewardApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				******** /* Debug */,
				******** /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1000014 /* Project object */;
}
