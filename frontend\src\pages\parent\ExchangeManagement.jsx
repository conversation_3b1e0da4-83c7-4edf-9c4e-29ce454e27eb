import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { createParentApiClient } from '../../api/apiConfig';

const PageContainer = styled.div`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
`;

const AddButton = styled(motion.button)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ItemsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 24px;
`;

const ItemCard = styled(motion.div)`
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  position: relative;
`;

const ItemHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
`;

const ItemName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  flex: 1;
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  ${props => props.active ? `
    background: #e8f5e8;
    color: #2d7d32;
  ` : `
    background: #ffebee;
    color: #c62828;
  `}
`;

const ItemDescription = styled.p`
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 8px 0;
`;

const ItemDetails = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0;
`;

const PointsRequired = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  color: #ff6b35;
`;

const Stock = styled.div`
  font-size: 14px;
  color: #666;
`;

const Category = styled.div`
  display: inline-block;
  background: #f5f5f5;
  color: #666;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  margin-bottom: 12px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const ActionButton = styled(motion.button)`
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  flex: 1;
  
  ${props => props.variant === 'edit' && `
    background: #e3f2fd;
    color: #1976d2;
  `}
  
  ${props => props.variant === 'toggle' && `
    background: ${props.active ? '#ffebee' : '#e8f5e8'};
    color: ${props.active ? '#c62828' : '#2d7d32'};
  `}
  
  ${props => props.variant === 'delete' && `
    background: #ffebee;
    color: #c62828;
  `}
`;

const Modal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled(motion.div)`
  background: white;
  border-radius: 16px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  
  &:hover {
    background: #f5f5f5;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  min-height: 80px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const ModalActions = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 24px;
`;

const ModalButton = styled(motion.button)`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  
  ${props => props.variant === 'primary' && `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  `}
  
  ${props => props.variant === 'secondary' && `
    background: #f5f5f5;
    color: #666;
  `}
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
`;

const ExchangeManagement = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    requiredPoints: '',
    stock: '',
    category: '',
    imageUrl: '',
    sortOrder: 0
  });

  const categories = ['游戏时间', '特权', '实物奖励', '零花钱', '其他'];

  useEffect(() => {
    fetchItems();
  }, []);

  const fetchItems = async () => {
    try {
      const client = createParentApiClient();
      const response = await client.get('/exchange/items/admin');
      if (response && response.data) {
        setItems(response.data);
      }
    } catch (error) {
      console.error('获取兑换商品失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingItem(null);
    setFormData({
      name: '',
      description: '',
      requiredPoints: '',
      stock: '',
      category: '',
      imageUrl: '',
      sortOrder: 0
    });
    setShowModal(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      description: item.description || '',
      requiredPoints: item.requiredPoints.toString(),
      stock: item.stock === -1 ? '' : item.stock.toString(),
      category: item.category || '',
      imageUrl: item.imageUrl || '',
      sortOrder: item.sortOrder || 0
    });
    setShowModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      requiredPoints: parseInt(formData.requiredPoints),
      stock: formData.stock === '' ? -1 : parseInt(formData.stock),
      sortOrder: parseInt(formData.sortOrder) || 0,
      isActive: true
    };

    try {
      const client = createParentApiClient();
      let response;

      if (editingItem) {
        response = await client.put(`/exchange/items/${editingItem.id}`, submitData);
      } else {
        response = await client.post('/exchange/items', submitData);
      }

      if (response) {
        setShowModal(false);
        fetchItems();
      } else {
        alert('操作失败，请重试');
      }
    } catch (error) {
      console.error('提交失败:', error);
      alert('操作失败，请重试');
    }
  };

  const handleToggleStatus = async (item) => {
    try {
      const client = createParentApiClient();
      const response = await client.patch(`/exchange/items/${item.id}/toggle`);

      if (response) {
        fetchItems();
      } else {
        alert('操作失败，请重试');
      }
    } catch (error) {
      console.error('切换状态失败:', error);
      alert('操作失败，请重试');
    }
  };

  const handleDelete = async (item) => {
    if (!window.confirm(`确定要删除"${item.name}"吗？`)) {
      return;
    }

    try {
      const client = createParentApiClient();
      const response = await client.delete(`/exchange/items/${item.id}`);

      if (response) {
        fetchItems();
      } else {
        alert('删除失败，请重试');
      }
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败，请重试');
    }
  };

  if (loading) {
    return <LoadingSpinner>加载中...</LoadingSpinner>;
  }

  return (
    <PageContainer>
      <Header>
        <Title>兑换商品管理</Title>
        <AddButton
          onClick={handleAdd}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <span>➕</span>
          添加商品
        </AddButton>
      </Header>

      {items.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎁</div>
          <h3>还没有兑换商品</h3>
          <p>点击"添加商品"创建第一个兑换商品吧！</p>
        </EmptyState>
      ) : (
        <ItemsGrid>
          <AnimatePresence>
            {items.map((item) => (
              <ItemCard
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <ItemHeader>
                  <ItemName>{item.name}</ItemName>
                  <StatusBadge active={item.isActive}>
                    {item.isActive ? '启用' : '禁用'}
                  </StatusBadge>
                </ItemHeader>

                {item.category && <Category>{item.category}</Category>}
                
                <ItemDescription>{item.description}</ItemDescription>

                <ItemDetails>
                  <PointsRequired>
                    <span>⭐</span>
                    {item.requiredPoints} 积分
                  </PointsRequired>
                  <Stock>
                    库存: {item.stock === -1 ? '无限' : item.stock}
                  </Stock>
                </ItemDetails>

                <ActionButtons>
                  <ActionButton
                    variant="edit"
                    onClick={() => handleEdit(item)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    编辑
                  </ActionButton>
                  <ActionButton
                    variant="toggle"
                    active={item.isActive}
                    onClick={() => handleToggleStatus(item)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {item.isActive ? '禁用' : '启用'}
                  </ActionButton>
                  <ActionButton
                    variant="delete"
                    onClick={() => handleDelete(item)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    删除
                  </ActionButton>
                </ActionButtons>
              </ItemCard>
            ))}
          </AnimatePresence>
        </ItemsGrid>
      )}

      <AnimatePresence>
        {showModal && (
          <Modal
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => e.target === e.currentTarget && setShowModal(false)}
          >
            <ModalContent
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <ModalHeader>
                <ModalTitle>{editingItem ? '编辑商品' : '添加商品'}</ModalTitle>
                <CloseButton onClick={() => setShowModal(false)}>×</CloseButton>
              </ModalHeader>

              <form onSubmit={handleSubmit}>
                <FormGroup>
                  <Label>商品名称 *</Label>
                  <Input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                    placeholder="请输入商品名称"
                  />
                </FormGroup>

                <FormGroup>
                  <Label>商品描述</Label>
                  <TextArea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="请输入商品描述"
                  />
                </FormGroup>

                <FormGroup>
                  <Label>所需积分 *</Label>
                  <Input
                    type="number"
                    value={formData.requiredPoints}
                    onChange={(e) => setFormData({ ...formData, requiredPoints: e.target.value })}
                    required
                    min="1"
                    placeholder="请输入所需积分"
                  />
                </FormGroup>

                <FormGroup>
                  <Label>库存数量</Label>
                  <Input
                    type="number"
                    value={formData.stock}
                    onChange={(e) => setFormData({ ...formData, stock: e.target.value })}
                    min="0"
                    placeholder="留空表示无限库存"
                  />
                </FormGroup>

                <FormGroup>
                  <Label>商品分类</Label>
                  <Select
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  >
                    <option value="">请选择分类</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label>排序顺序</Label>
                  <Input
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData({ ...formData, sortOrder: e.target.value })}
                    min="0"
                    placeholder="数字越小排序越靠前"
                  />
                </FormGroup>

                <ModalActions>
                  <ModalButton
                    type="button"
                    variant="secondary"
                    onClick={() => setShowModal(false)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    取消
                  </ModalButton>
                  <ModalButton
                    type="submit"
                    variant="primary"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {editingItem ? '更新' : '创建'}
                  </ModalButton>
                </ModalActions>
              </form>
            </ModalContent>
          </Modal>
        )}
      </AnimatePresence>
    </PageContainer>
  );
};

export default ExchangeManagement;
