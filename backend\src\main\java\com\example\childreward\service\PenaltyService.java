package com.example.childreward.service;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.repository.TaskRepository;
import com.example.childreward.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * @author: <your name>
 * @date: 2024-06-22 17:35:49
 * @description: 惩罚服务，用于处理自动化的惩罚逻辑
 */
@Service
@Slf4j
public class PenaltyService {

    private final TaskRepository taskRepository;
    private final SystemConfigService systemConfigService;
    private final PointService pointService;

    public PenaltyService(TaskRepository taskRepository, SystemConfigService systemConfigService, PointService pointService) {
        this.taskRepository = taskRepository;
        this.systemConfigService = systemConfigService;
        this.pointService = pointService;
    }

    /**
     * 每日检查未完成任务并根据配置应用惩罚。
     * 默认在每天的凌晨0点5分执行，检查前一天的任务完成情况。
     */
    @Scheduled(cron = "0 5 0 * * ?")
    @Transactional
    public void applyPenaltyForUncompletedTasks() {
        log.info("开始执行每日未完成任务惩罚检查...");

        try {
            // 首先检查过期任务惩罚总开关
            boolean penaltyEnabled = systemConfigService.getBoolean("OVERDUE_PENALTY_ENABLED", false);
            if (!penaltyEnabled) {
                log.info("过期任务惩罚功能已关闭。跳过本次检查。");
                return;
            }

            // 从系统配置中获取惩罚阈值和惩罚分数
            int penaltyThreshold = systemConfigService.getInt("PENALTY_THRESHOLD", 3);
            int penaltyPoints = systemConfigService.getInt("PENALTY_POINTS", 10);

            if (penaltyThreshold <= 0 || penaltyPoints <= 0) {
                log.info("任务惩罚功能配置不正确（阈值或分数小于等于0）。跳过本次检查。");
                return;
            }

            // 检查前一天的未完成任务
            LocalDate yesterday = LocalDate.now().minusDays(1);
            List<Task> uncompletedTasks = taskRepository.findByDueDateAndStatusIn(
                    yesterday,
                    Arrays.asList(TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS)
            );

            log.info("检查日期: {}, 发现 {} 个未完成的任务。", yesterday, uncompletedTasks.size());

            if (uncompletedTasks.size() > penaltyThreshold) {
                log.warn("未完成任务数量 ({}) 已超过阈值 ({})，将执行扣分惩罚。", uncompletedTasks.size(), penaltyThreshold);

                // 执行扣分
                pointService.deductPoints(
                        penaltyPoints,
                        PointRecord.ChangeType.TASK_PENALTY,
                        String.format("每日任务未完成惩罚： %d 个任务未完成", uncompletedTasks.size()),
                        null
                );

                log.info("成功扣除 {} 积分。", penaltyPoints);

            } else {
                log.info("未完成任务数量未超过阈值，不执行扣分。");
            }

        } catch (Exception e) {
            log.error("执行未完成任务惩罚检查时发生异常。", e);
        }

        log.info("每日未完成任务惩罚检查结束。");
    }
} 