import Foundation

// MARK: - 任务状态枚举
enum TaskStatus: String, CaseIterable, Codable {
    case notStarted = "NOT_STARTED"
    case pending = "PENDING"
    case inProgress = "IN_PROGRESS"
    case completed = "COMPLETED"
    case approved = "APPROVED"
    case overdue = "OVERDUE"
    case cancelled = "CANCELLED"
    case rejected = "REJECTED"
    case penaltyApproval = "PENALTY_APPROVAL"

    var displayName: String {
        switch self {
        case .notStarted: return "未开始"
        case .pending: return "待开始"
        case .inProgress: return "进行中"
        case .completed: return "已完成"
        case .approved: return "已批准"
        case .overdue: return "已过期"
        case .cancelled: return "已取消"
        case .rejected: return "已作废"
        case .penaltyApproval: return "惩罚审批"
        }
    }

    var color: String {
        switch self {
        case .notStarted: return "#FFA500"
        case .pending: return "#FF9500"
        case .inProgress: return "#007AFF"
        case .completed: return "#34C759"
        case .approved: return "#30D158"
        case .overdue: return "#FF3B30"
        case .cancelled: return "#8E8E93"
        }
    }
}

// MARK: - 任务模型
struct TaskModel: Codable, Identifiable {
    let id: Int
    let sourceTemplateId: Int?
    let title: String
    let description: String?
    let expectedMinutes: Int
    let basePoints: Int
    let dueTime: String?
    let dueDate: String?
    let status: TaskStatus
    let taskType: String?
    let startTime: String?
    let endTime: String?
    let actualPoints: Int?
    let createdTime: String?
    let scheduledDate: String?
    let actualMinutes: Int?

    var formattedExpectedTime: String {
        if expectedMinutes < 60 {
            return "\(expectedMinutes)分钟"
        } else {
            let hours = expectedMinutes / 60
            let minutes = expectedMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }

    var isCompleted: Bool {
        return status == .completed || status == .approved
    }

    var canStart: Bool {
        return (status == .notStarted || status == .pending) && status != .rejected
    }

    var canComplete: Bool {
        return status == .inProgress && status != .rejected
    }

    var formattedDueDate: String? {
        guard let dueDate = dueDate else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        if let date = formatter.date(from: dueDate) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return dueDate
    }
}

// MARK: - 类型别名
typealias AppTask = TaskModel
// 注释掉TaskSummary别名，避免与Task.swift中的TaskSummary结构体冲突
// typealias TaskSummary = TaskModel
