import SwiftUI

// 分段进度条组件
struct SegmentedProgressBar: View {
    let segments: [ProgressSegment]
    let height: CGFloat
    let cornerRadius: CGFloat
    let spacing: CGFloat
    let animationDuration: Double
    
    init(
        segments: [ProgressSegment],
        height: CGFloat = 8,
        cornerRadius: CGFloat = 4,
        spacing: CGFloat = 2,
        animationDuration: Double = 0.8
    ) {
        self.segments = segments
        self.height = height
        self.cornerRadius = cornerRadius
        self.spacing = spacing
        self.animationDuration = animationDuration
    }
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: spacing) {
                ForEach(Array(segments.enumerated()), id: \.offset) { index, segment in
                    SegmentView(
                        segment: segment,
                        width: segmentWidth(for: geometry.size.width),
                        height: height,
                        cornerRadius: cornerRadius,
                        animationDelay: Double(index) * 0.1
                    )
                }
            }
        }
        .frame(height: height)
    }
    
    private func segmentWidth(for totalWidth: CGFloat) -> CGFloat {
        guard segments.count > 0 else { return 0 }
        let totalSpacing = spacing * CGFloat(segments.count - 1)
        return (totalWidth - totalSpacing) / CGFloat(segments.count)
    }
}

// 单个进度段
struct SegmentView: View {
    let segment: ProgressSegment
    let width: CGFloat
    let height: CGFloat
    let cornerRadius: CGFloat
    let animationDelay: Double
    
    @State private var animatedProgress: CGFloat = 0
    
    var body: some View {
        ZStack(alignment: .leading) {
            // 背景
            Rectangle()
                .fill(Color(.systemGray5))
                .frame(width: width, height: height)
                .cornerRadius(cornerRadius)
            
            // 进度填充
            Rectangle()
                .fill(segment.color)
                .frame(width: width * animatedProgress, height: height)
                .cornerRadius(cornerRadius)
                .animation(.easeInOut(duration: 0.6).delay(animationDelay), value: animatedProgress)
        }
        .onAppear {
            animatedProgress = segment.isCompleted ? 1.0 : 0.0
        }
        .onChange(of: segment.isCompleted) { newValue in
            withAnimation(.easeInOut(duration: 0.4)) {
                animatedProgress = newValue ? 1.0 : 0.0
            }
        }
    }
}

// 进度段数据模型
struct ProgressSegment: Identifiable {
    let id = UUID()
    let isCompleted: Bool
    let color: Color
    let status: TaskStatus
    
    init(status: TaskStatus) {
        self.status = status
        
        switch status {
        case .notStarted:
            self.isCompleted = false
            self.color = Color(.systemGray4)
        case .pending:
            self.isCompleted = true
            self.color = .orange // 待审核 - 黄色/橙色
        case .inProgress:
            self.isCompleted = false
            self.color = .blue
        case .completed, .approved:
            self.isCompleted = true
            self.color = .green // 已完成 - 绿色
        case .overdue:
            self.isCompleted = true
            self.color = .red // 审核未通过 - 红色
        case .cancelled:
            self.isCompleted = false
            self.color = Color(.systemGray4)
        case .rejected:
            self.isCompleted = true
            self.color = .red // 已作废 - 红色
        case .penaltyApproval:
            self.isCompleted = true
            self.color = .purple // 惩罚审批 - 紫色
        }
    }
}

// 今日任务进度条
struct DailyTaskProgressBar: View {
    let tasks: [TaskSummary]
    let showPercentage: Bool

    init(tasks: [TaskSummary], showPercentage: Bool = true) {
        self.tasks = tasks
        self.showPercentage = showPercentage
    }

    private var progressSegments: [ProgressSegment] {
        return tasks.map { task in
            ProgressSegment(status: task.displayStatus)
        }
    }

    // 计算完成百分比
    private var completionPercentage: Int {
        guard !tasks.isEmpty else { return 0 }
        // 只要不是未开始和进行中就算进度
        let completedTasks = tasks.filter { task in
            task.displayStatus != .notStarted && task.displayStatus != .inProgress
        }
        return Int(round(Double(completedTasks.count) / Double(tasks.count) * 100))
    }

    // 计算各状态任务数量
    private var statusCounts: (completed: Int, pending: Int, failed: Int, notStarted: Int) {
        var completed = 0
        var pending = 0
        var failed = 0
        var notStarted = 0

        for task in tasks {
            switch task.displayStatus {
            case .completed, .approved:
                completed += 1
            case .pending:
                pending += 1
            case .overdue:
                failed += 1
            case .notStarted, .cancelled:
                notStarted += 1
            case .inProgress:
                notStarted += 1 // 进行中算作未完成
            case .rejected:
                failed += 1 // 已作废算作失败
            case .penaltyApproval:
                pending += 1 // 惩罚审批算作待处理
            }
        }

        return (completed, pending, failed, notStarted)
    }

    var body: some View {
        VStack(spacing: 8) {
            if !tasks.isEmpty {
                // 进度条
                SegmentedProgressBar(
                    segments: progressSegments,
                    height: 6,
                    cornerRadius: 3,
                    spacing: 1.5
                )
                .padding(.horizontal, 2)

                // 百分比和统计信息
                if showPercentage {
                    HStack(spacing: 16) {
                        // 完成百分比
                        Text("\(completionPercentage)%")
                            .font(.system(size: 16, weight: .semibold, design: .rounded))
                            .foregroundColor(.primary)

                        Spacer()

                        // 状态统计
                        HStack(spacing: 12) {
                            let counts = statusCounts

                            if counts.completed > 0 {
                                StatusBadge(count: counts.completed, color: .green, icon: "checkmark.circle.fill")
                            }

                            if counts.pending > 0 {
                                StatusBadge(count: counts.pending, color: .orange, icon: "clock.fill")
                            }

                            if counts.failed > 0 {
                                StatusBadge(count: counts.failed, color: .red, icon: "xmark.circle.fill")
                            }

                            if counts.notStarted > 0 {
                                StatusBadge(count: counts.notStarted, color: .gray, icon: "circle")
                            }
                        }
                    }
                    .padding(.horizontal, 4)
                    .padding(.top, 4)
                }
            }
        }
    }
}

// 状态徽章组件
struct StatusBadge: View {
    let count: Int
    let color: Color
    let icon: String

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(color)

            Text("\(count)")
                .font(.system(size: 12, weight: .medium, design: .rounded))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

// 预览
struct SegmentedProgressBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            // 示例1：基础进度条
            VStack(alignment: .leading, spacing: 8) {
                Text("基础进度条")
                    .font(.headline)
                SegmentedProgressBar(
                    segments: [
                        ProgressSegment(status: .approved),
                        ProgressSegment(status: .pending),
                        ProgressSegment(status: .notStarted),
                        ProgressSegment(status: .overdue),
                        ProgressSegment(status: .approved)
                    ]
                )
            }

            // 示例2：带百分比的完整组件
            VStack(alignment: .leading, spacing: 8) {
                Text("带统计信息的进度条")
                    .font(.headline)

                // 模拟任务数据
                let mockTasks = [
                    createMockTask(status: .approved),
                    createMockTask(status: .approved),
                    createMockTask(status: .pending),
                    createMockTask(status: .overdue),
                    createMockTask(status: .notStarted)
                ]

                DailyTaskProgressBar(tasks: mockTasks, showPercentage: true)
                    .padding()
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)
            }

            // 示例3：全部完成
            VStack(alignment: .leading, spacing: 8) {
                Text("全部完成")
                    .font(.headline)

                let completedTasks = Array(repeating: createMockTask(status: .approved), count: 4)

                DailyTaskProgressBar(tasks: completedTasks, showPercentage: true)
                    .padding()
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)
            }
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }

    // 创建模拟任务的辅助函数
    static func createMockTask(status: TaskStatus) -> TaskSummary {
        return TaskSummary(
            id: Int.random(in: 1...1000),
            sourceTemplateId: 1,
            title: "模拟任务",
            description: "这是一个模拟任务",
            expectedMinutes: 30,
            basePoints: 10,
            dueTime: "18:00",
            dueDate: "2024-01-15",
            status: status,
            taskType: "homework",
            startTime: nil,
            endTime: nil,
            actualPoints: nil,
            createdTime: "2024-01-15T10:00:00Z",
            scheduledDate: "2024-01-15",
            actualMinutes: status == .approved ? 25 : nil
        )
    }
}
