import Foundation

// 奖励池状态枚举
enum RewardPoolStatus: String, CaseIterable, Codable {
    case active = "ACTIVE"
    case inactive = "INACTIVE"
    case expired = "EXPIRED"
    
    var displayName: String {
        switch self {
        case .active: return "可用"
        case .inactive: return "不可用"
        case .expired: return "已过期"
        }
    }
}

// 奖励类型枚举
enum RewardType: String, CaseIterable, Codable {
    case points = "POINTS"
    case item = "ITEM"
    case privilege = "PRIVILEGE"
    case experience = "EXPERIENCE"
    
    var displayName: String {
        switch self {
        case .points: return "积分奖励"
        case .item: return "实物奖励"
        case .privilege: return "特权奖励"
        case .experience: return "体验奖励"
        }
    }
    
    var icon: String {
        switch self {
        case .points: return "⭐"
        case .item: return "🎁"
        case .privilege: return "👑"
        case .experience: return "🎪"
        }
    }
}

// 奖励项模型 - 匹配后端RewardItem实体
struct RewardItem: Codable, Identifiable {
    let id: Int
    let name: String
    let probability: Float
    let stock: Int
    let imageUrl: String?
    let createdTime: String?

    var formattedProbability: String {
        return String(format: "%.1f%%", probability * 100)
    }

    var hasStock: Bool {
        return stock > 0
    }

    var stockText: String {
        return "库存\(stock)个"
    }
}

// 奖励池类型枚举
enum PoolType: String, Codable {
    case reward = "REWARD"
    case penalty = "PENALTY"

    var displayName: String {
        switch self {
        case .reward: return "奖励池"
        case .penalty: return "惩罚池"
        }
    }
}

// 奖励池模型 - 匹配后端RewardPool实体
struct RewardPool: Codable, Identifiable {
    let id: Int
    let name: String
    let costPoints: Int
    let isEnabled: Bool
    let poolType: PoolType
    let createdTime: String?
    let rewardItems: [RewardItem]?

    // 计算属性
    var isAvailable: Bool {
        return isEnabled && poolType == .reward
    }

    var canDraw: Bool {
        return isAvailable
    }

    var formattedCost: String {
        return "\(costPoints)积分"
    }

    var itemCount: Int {
        return rewardItems?.count ?? 0
    }
}

// 抽奖结果模型
struct DrawResult: Codable, Identifiable {
    let id: Int
    let rewardItem: RewardItem
    let drawTime: String
    let poolId: Int
    let pointsSpent: Int
    
    var formattedDrawTime: String {
        guard let date = ISO8601DateFormatter().date(from: drawTime) else {
            return drawTime
        }
        return DateFormatter.rewardDate.string(from: date)
    }
    
    var isSpecialReward: Bool {
        return rewardItem.isSpecial
    }
}

// 兑换商品模型 - 匹配后端ExchangeItem实体
struct ExchangeItem: Codable, Identifiable {
    let id: Int
    let name: String
    let description: String?
    let requiredPoints: Int
    let stock: Int?
    let category: String?
    let imageUrl: String?
    let isActive: Bool
    let sortOrder: Int?
    let createdTime: String?
    let updatedTime: String?

    var formattedCost: String {
        return "\(requiredPoints)积分"
    }

    var hasStock: Bool {
        return stock == nil || stock == -1 || stock! > 0
    }

    var canExchange: Bool {
        return isActive && hasStock
    }

    var stockText: String? {
        guard let stock = stock, stock != -1 else { return nil }
        return stock > 0 ? "库存\(stock)件" : "缺货"
    }
}

// 兑换状态枚举
enum ExchangeStatus: String, Codable {
    case unused = "UNUSED"
    case used = "USED"
    case expired = "EXPIRED"

    var displayName: String {
        switch self {
        case .unused: return "待使用"
        case .used: return "已使用"
        case .expired: return "已过期"
        }
    }

    var color: Color {
        switch self {
        case .unused: return .orange
        case .used: return .green
        case .expired: return .red
        }
    }
}

// 兑换记录模型 - 匹配后端ExchangeRecord实体
struct ExchangeRecord: Codable, Identifiable {
    let id: Int
    let exchangeItemId: Int
    let itemName: String
    let pointsUsed: Int
    let exchangeTime: String
    let status: ExchangeStatus
    let usedTime: String?
    let expireTime: String?
    let notes: String?

    var formattedExchangeTime: String {
        guard let date = ISO8601DateFormatter().date(from: exchangeTime) else {
            return exchangeTime
        }
        return DateFormatter.rewardDate.string(from: date)
    }

    var formattedExpireTime: String? {
        guard let expireTime = expireTime,
              let date = ISO8601DateFormatter().date(from: expireTime) else {
            return nil
        }
        return DateFormatter.rewardDate.string(from: date)
    }
}

// 抽奖结果模型
struct RewardDrawResult: Codable, Identifiable {
    let id: Int
    let rewardItem: RewardItem
    let drawTime: String
    let pointsSpent: Int

    var formattedDrawTime: String {
        guard let date = ISO8601DateFormatter().date(from: drawTime) else {
            return drawTime
        }
        return DateFormatter.rewardDate.string(from: date)
    }
}

// 日期格式化扩展
extension DateFormatter {
    static let rewardDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}
