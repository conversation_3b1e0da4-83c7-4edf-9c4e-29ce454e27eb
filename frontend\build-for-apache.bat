@echo off
echo 正在构建前端项目用于Apache部署（子目录/crs）...
echo.

REM 设置生产环境
set NODE_ENV=production

REM 执行构建
npm run build:prod

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo.
    echo 📁 构建文件位置: dist/
    echo 🌐 API地址配置: http://*************:18080/api
    echo 📂 部署路径配置: /crs/
    echo.
    echo 📦 创建Apache部署压缩包...
    
    REM 创建Apache部署压缩包
    if exist "crs-apache-deploy.zip" del "crs-apache-deploy.zip"
    powershell "Compress-Archive -Path 'dist\*' -DestinationPath 'crs-apache-deploy.zip'"
    
    if exist "crs-apache-deploy.zip" (
        echo ✅ Apache部署压缩包创建成功: crs-apache-deploy.zip
        echo.
        echo 🚀 Apache部署说明:
        echo 1. 将 crs-apache-deploy.zip 上传到服务器
        echo 2. 在Apache网站根目录创建 crs 文件夹
        echo 3. 解压文件到 crs 文件夹中
        echo 4. 访问地址: http://your-server/crs/
        echo 5. 确保后端服务运行在 *************:18080
        echo.
        echo 📋 Apache配置建议:
        echo - 确保启用了 mod_rewrite 模块
        echo - 在 crs 目录下创建 .htaccess 文件配置路由
    ) else (
        echo ❌ 压缩包创建失败
    )
) else (
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause