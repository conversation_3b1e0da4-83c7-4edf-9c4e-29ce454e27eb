/**
 * 简单测试页面 - 用于调试
 */

import React from 'react';

const TestPage = () => {
  return (
    <div style={{
      padding: '20px',
      fontFamily: 'system-ui, sans-serif',
      background: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#007AFF', marginBottom: '20px' }}>
        🍎 测试页面
      </h1>
      
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h2>基础测试</h2>
        <p>如果你能看到这个页面，说明React应用正在正常运行。</p>
        
        <div style={{ marginTop: '20px' }}>
          <button style={{
            background: '#007AFF',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '8px',
            cursor: 'pointer',
            marginRight: '10px'
          }}>
            测试按钮
          </button>
          
          <button 
            style={{
              background: '#34C759',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
            onClick={() => alert('按钮点击成功！')}
          >
            点击测试
          </button>
        </div>
      </div>
      
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <h3>导航测试</h3>
        <p>尝试访问以下页面：</p>
        <ul>
          <li><a href="/child" style={{ color: '#007AFF' }}>儿童端页面</a></li>
          <li><a href="/parent" style={{ color: '#007AFF' }}>家长端页面</a></li>
        </ul>
      </div>
    </div>
  );
};

export default TestPage;
