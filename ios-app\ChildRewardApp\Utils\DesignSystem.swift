import SwiftUI

// 统一设计系统 - 基于苹果Human Interface Guidelines
struct DesignSystem {

    // MARK: - 颜色系统 (基于iOS系统色彩)
    struct Colors {
        // 主色调 - iOS系统蓝
        static let primary = Color(red: 0/255, green: 122/255, blue: 255/255) // iOS Blue
        static let primaryLight = Color(red: 64/255, green: 156/255, blue: 255/255)
        static let primaryDark = Color(red: 0/255, green: 64/255, blue: 221/255)

        // 辅助色 - iOS系统绿
        static let secondary = Color(red: 52/255, green: 199/255, blue: 89/255) // iOS Green
        static let accent = Color(red: 255/255, green: 149/255, blue: 0/255) // iOS Orange

        // 状态色
        static let success = Color(red: 52/255, green: 199/255, blue: 89/255) // iOS Green
        static let warning = Color(red: 255/255, green: 149/255, blue: 0/255) // iOS Orange
        static let error = Color(red: 255/255, green: 59/255, blue: 48/255) // iOS Red

        // 背景色 - 使用系统动态颜色
        static let background = Color(.systemBackground)
        static let secondaryBackground = Color(.secondarySystemBackground)
        static let tertiaryBackground = Color(.tertiarySystemBackground)
        static let groupedBackground = Color(.systemGroupedBackground)

        // 卡片背景
        static let cardBackground = Color(.systemBackground)
        static let cardSecondaryBackground = Color(.secondarySystemBackground)

        // 文字色
        static let text = Color(.label)
        static let secondaryText = Color(.secondaryLabel)
        static let tertiaryText = Color(.tertiaryLabel)

        // 分割线和边框
        static let separator = Color(.separator)
        static let border = Color(.systemGray4)

        // 特殊色彩
        static let tint = Color(.tintColor)
        static let fill = Color(.systemFill)
        static let secondaryFill = Color(.secondarySystemFill)
        static let tertiaryFill = Color(.tertiarySystemFill)
    }

    // MARK: - 字体系统 (基于iOS系统字体)
    struct Typography {
        // 使用系统字体规范
        static let largeTitle = Font.largeTitle.weight(.bold)
        static let title1 = Font.title.weight(.bold)
        static let title2 = Font.title2.weight(.bold)
        static let title3 = Font.title3.weight(.semibold)

        static let headline = Font.headline
        static let body = Font.body
        static let callout = Font.callout
        static let subheadline = Font.subheadline
        static let footnote = Font.footnote
        static let caption1 = Font.caption
        static let caption2 = Font.caption2

        // 特殊用途字体
        static let navigationTitle = Font.title2.weight(.bold)
        static let cardTitle = Font.headline
        static let cardSubtitle = Font.subheadline
        static let buttonText = Font.body.weight(.semibold)
        static let tabBarText = Font.caption
    }

    // MARK: - 间距系统 (基于8pt网格)
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48

        // 标准间距
        static let padding: CGFloat = 16
        static let margin: CGFloat = 20
        static let section: CGFloat = 32
    }

    // MARK: - 圆角系统
    struct CornerRadius {
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20

        // 标准圆角
        static let button: CGFloat = 12
        static let card: CGFloat = 16
        static let sheet: CGFloat = 20
    }

    // MARK: - 阴影系统 (苹果风格)
    struct Shadow {
        static let card = (
            color: Color.black.opacity(0.05),
            radius: CGFloat(8),
            x: CGFloat(0),
            y: CGFloat(2)
        )

        static let button = (
            color: Color.black.opacity(0.1),
            radius: CGFloat(4),
            x: CGFloat(0),
            y: CGFloat(2)
        )

        static let modal = (
            color: Color.black.opacity(0.15),
            radius: CGFloat(20),
            x: CGFloat(0),
            y: CGFloat(10)
        )
    }

    // MARK: - 动画系统
    struct Animation {
        static let quick = SwiftUI.Animation.easeInOut(duration: 0.2)
        static let smooth = SwiftUI.Animation.easeInOut(duration: 0.3)
        static let spring = SwiftUI.Animation.spring(response: 0.5, dampingFraction: 0.8)
    }

    // MARK: - 布局系统 (iPad横屏优化)
    struct Layout {
        // iPad横屏布局参数
        static let maxContentWidth: CGFloat = 1200
        static let sidebarWidth: CGFloat = 300
        static let mainContentWidth: CGFloat = 900

        // 网格布局
        static let taskGridColumns = 2
        static let rewardGridColumns = 1
        static let exchangeGridColumns = 3
        static let historyGridColumns = 1

        // 间距
        static let horizontalPadding: CGFloat = 32
        static let verticalPadding: CGFloat = 24
        static let cardSpacing: CGFloat = 20
        static let sectionSpacing: CGFloat = 40

        // 卡片尺寸
        static let cardMinHeight: CGFloat = 120
        static let cardMaxHeight: CGFloat = 200
        static let exchangeCardHeight: CGFloat = 240
    }
}

// MARK: - 视图修饰符扩展
extension View {
    // 标准卡片样式
    func cardStyle() -> some View {
        self
            .background(DesignSystem.Colors.cardBackground)
            .cornerRadius(DesignSystem.CornerRadius.card)
            .shadow(
                color: DesignSystem.Shadow.card.color,
                radius: DesignSystem.Shadow.card.radius,
                x: DesignSystem.Shadow.card.x,
                y: DesignSystem.Shadow.card.y
            )
    }

    // 主要按钮样式
    func primaryButtonStyle() -> some View {
        self
            .foregroundColor(.white)
            .font(DesignSystem.Typography.buttonText)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.primary)
            .cornerRadius(DesignSystem.CornerRadius.button)
            .shadow(
                color: DesignSystem.Shadow.button.color,
                radius: DesignSystem.Shadow.button.radius,
                x: DesignSystem.Shadow.button.x,
                y: DesignSystem.Shadow.button.y
            )
    }

    // 次要按钮样式
    func secondaryButtonStyle() -> some View {
        self
            .foregroundColor(DesignSystem.Colors.primary)
            .font(DesignSystem.Typography.buttonText)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.cardBackground)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.button)
                    .stroke(DesignSystem.Colors.primary, lineWidth: 1)
            )
            .cornerRadius(DesignSystem.CornerRadius.button)
    }

    // 毛玻璃效果
    func glassBackground() -> some View {
        self
            .background(.ultraThinMaterial)
            .cornerRadius(DesignSystem.CornerRadius.card)
    }
}
