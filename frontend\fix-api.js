const fs = require('fs');
const path = require('path');

// 读取文件
const filePath = path.join(__dirname, 'src/api/apiService.js');
let content = fs.readFileSync(filePath, 'utf8');

// 替换所有的 apiClient. 为 getApiClient().
// 但是要小心，不要替换已经正确的部分
content = content.replace(/return apiClient\./g, 'const client = getApiClient();\n    return client.');

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');

console.log('API修复完成！');
