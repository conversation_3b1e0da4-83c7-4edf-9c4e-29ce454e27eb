import React from 'react';
import styled from 'styled-components';

const DisplayContainer = styled.div`
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  
  /* 标题样式 */
  h1, h2, h3 {
    margin: 16px 0 8px 0;
    font-weight: 600;
  }
  
  h1 { font-size: 1.5em; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.1em; }
  
  /* 段落样式 */
  p {
    margin: 8px 0;
  }
  
  /* 列表样式 */
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
  }
  
  li {
    margin: 4px 0;
  }
  
  /* 图片样式 */
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 12px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: block;
  }
  
  /* 链接样式 */
  a {
    color: #007bff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  /* 强调样式 */
  strong {
    font-weight: 600;
  }
  
  em {
    font-style: italic;
  }
  
  u {
    text-decoration: underline;
  }
  
  s {
    text-decoration: line-through;
  }
  
  /* 对齐样式 */
  .ql-align-center {
    text-align: center;
  }
  
  .ql-align-right {
    text-align: right;
  }
  
  .ql-align-justify {
    text-align: justify;
  }
  
  /* 背景色和文字颜色 */
  .ql-color-red { color: #e74c3c; }
  .ql-color-orange { color: #f39c12; }
  .ql-color-yellow { color: #f1c40f; }
  .ql-color-green { color: #27ae60; }
  .ql-color-blue { color: #3498db; }
  .ql-color-purple { color: #9b59b6; }
  
  .ql-bg-red { background-color: #ffebee; }
  .ql-bg-orange { background-color: #fff3e0; }
  .ql-bg-yellow { background-color: #fffde7; }
  .ql-bg-green { background-color: #e8f5e8; }
  .ql-bg-blue { background-color: #e3f2fd; }
  .ql-bg-purple { background-color: #f3e5f5; }
`;

const RichTextDisplay = ({ content, className }) => {
  // 如果内容为空，显示默认文本
  if (!content || content.trim() === '' || content === '<p><br></p>') {
    return (
      <DisplayContainer className={className}>
        <p style={{ color: '#999', fontStyle: 'italic' }}>暂无描述</p>
      </DisplayContainer>
    );
  }

  return (
    <DisplayContainer 
      className={className}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default RichTextDisplay;
