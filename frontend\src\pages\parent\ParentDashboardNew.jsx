import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { AppleDesign } from '../../styles/appleDesignSystem';
import { pointApi, taskApi, rewardApi } from '../../api/apiService';
import {
  ParentContainer,
  ParentHeader,
  ParentTitle,
  ParentSubtitle,
  ParentGrid,
  ParentCard,
  ParentCardTitle,
  ParentCardContent,
  StatNumber,
  StatLabel,
  ParentButton,
  ParentActionGroup
} from '../../components/parent/ParentLayout';

const ParentDashboardNew = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalPoints: 0,
    todayTasks: 0,
    completedTasks: 0,
    pendingApprovals: 0,
    weeklyProgress: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // 获取统计数据
      const [pointsRes, tasksRes, approvalsRes] = await Promise.all([
        pointApi.getTotalPoints(),
        taskApi.getTodayTasks(),
        taskApi.getPendingApprovals()
      ]);

      const todayTasks = tasksRes.data || [];
      const completedTasks = todayTasks.filter(task => task.status === 'COMPLETED');
      
      setStats({
        totalPoints: pointsRes.data || 0,
        todayTasks: todayTasks.length,
        completedTasks: completedTasks.length,
        pendingApprovals: approvalsRes.data?.length || 0,
        weeklyProgress: todayTasks.length > 0 ? Math.round((completedTasks.length / todayTasks.length) * 100) : 0
      });

      // 获取最近活动
      const activitiesRes = await pointApi.getRecentActivities();
      setRecentActivities(activitiesRes.data?.slice(0, 5) || []);

    } catch (error) {
      console.error('获取仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: '任务管理',
      description: '查看和管理孩子的任务',
      icon: '📝',
      color: 'primary',
      path: '/parent/tasks'
    },
    {
      title: '审批中心',
      description: `${stats.pendingApprovals} 个待审批`,
      icon: '✅',
      color: 'warning',
      path: '/parent/approvals'
    },
    {
      title: '积分记录',
      description: '查看积分变化历史',
      icon: '📊',
      color: 'success',
      path: '/parent/points'
    },
    {
      title: '奖励设置',
      description: '管理奖励项目',
      icon: '🎁',
      color: 'secondary',
      path: '/parent/rewards'
    },
    {
      title: '计划管理',
      description: '设置定时任务',
      icon: '⏰',
      color: 'primary',
      path: '/parent/scheduled-tasks'
    },
    {
      title: '系统设置',
      description: '配置系统参数',
      icon: '⚙️',
      color: 'default',
      path: '/parent/settings'
    }
  ];

  if (loading) {
    return (
      <ParentContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            border: `4px solid ${AppleDesign.colors.systemGray5}`,
            borderTop: `4px solid ${AppleDesign.colors.parent.primary}`,
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
        </div>
      </ParentContainer>
    );
  }

  return (
    <ParentContainer>
      {/* 头部 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <ParentHeader>
          <div>
            <ParentTitle>家长控制台</ParentTitle>
            <ParentSubtitle>管理孩子的任务和奖励系统</ParentSubtitle>
          </div>
          <ParentActionGroup>
            <ParentButton 
              variant="primary"
              onClick={() => navigate('/parent/tasks/new')}
            >
              ➕ 新建任务
            </ParentButton>
            <ParentButton 
              variant="outline"
              onClick={() => window.location.reload()}
            >
              🔄 刷新
            </ParentButton>
          </ParentActionGroup>
        </ParentHeader>
      </motion.div>

      {/* 统计卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <ParentGrid>
          <ParentCard>
            <ParentCardTitle>
              💰 总积分
            </ParentCardTitle>
            <StatNumber variant="primary">{stats.totalPoints}</StatNumber>
            <StatLabel>当前积分余额</StatLabel>
          </ParentCard>

          <ParentCard>
            <ParentCardTitle>
              📋 今日任务
            </ParentCardTitle>
            <StatNumber variant="success">{stats.completedTasks}/{stats.todayTasks}</StatNumber>
            <StatLabel>已完成任务</StatLabel>
          </ParentCard>

          <ParentCard>
            <ParentCardTitle>
              ⏳ 待审批
            </ParentCardTitle>
            <StatNumber variant="warning">{stats.pendingApprovals}</StatNumber>
            <StatLabel>需要处理</StatLabel>
          </ParentCard>

          <ParentCard>
            <ParentCardTitle>
              📈 本周进度
            </ParentCardTitle>
            <StatNumber variant="primary">{stats.weeklyProgress}%</StatNumber>
            <StatLabel>任务完成率</StatLabel>
          </ParentCard>
        </ParentGrid>
      </motion.div>

      {/* 快捷操作 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <ParentCard fullWidth style={{ marginTop: AppleDesign.spacing.lg }}>
          <ParentCardTitle>
            🚀 快捷操作
          </ParentCardTitle>
          <ParentGrid>
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
              >
                <ParentCard 
                  style={{ 
                    cursor: 'pointer',
                    border: `2px solid transparent`,
                    transition: `all ${AppleDesign.animation.duration.fast} ${AppleDesign.animation.easing.easeOut}`
                  }}
                  onClick={() => navigate(action.path)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = AppleDesign.colors.parent[action.color] || AppleDesign.colors.systemGray4;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'transparent';
                  }}
                >
                  <ParentCardTitle>
                    <span style={{ fontSize: '24px' }}>{action.icon}</span>
                    {action.title}
                  </ParentCardTitle>
                  <ParentCardContent>
                    {action.description}
                  </ParentCardContent>
                </ParentCard>
              </motion.div>
            ))}
          </ParentGrid>
        </ParentCard>
      </motion.div>

      {/* 最近活动 */}
      {recentActivities.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <ParentCard fullWidth style={{ marginTop: AppleDesign.spacing.lg }}>
            <ParentCardTitle>
              📋 最近活动
            </ParentCardTitle>
            <ParentCardContent>
              {recentActivities.map((activity, index) => (
                <div 
                  key={index}
                  style={{
                    padding: AppleDesign.spacing.md,
                    borderBottom: index < recentActivities.length - 1 ? `1px solid ${AppleDesign.colors.separator}` : 'none',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <div>
                    <div style={{ fontWeight: AppleDesign.typography.fontWeight.medium }}>
                      {activity.description || activity.title}
                    </div>
                    <div style={{ 
                      fontSize: AppleDesign.typography.fontSize.footnote,
                      color: AppleDesign.colors.tertiaryLabel,
                      marginTop: AppleDesign.spacing.xs
                    }}>
                      {new Date(activity.createdAt).toLocaleString()}
                    </div>
                  </div>
                  <div style={{
                    color: activity.points > 0 ? AppleDesign.colors.parent.success : AppleDesign.colors.parent.danger,
                    fontWeight: AppleDesign.typography.fontWeight.semibold
                  }}>
                    {activity.points > 0 ? '+' : ''}{activity.points}
                  </div>
                </div>
              ))}
            </ParentCardContent>
          </ParentCard>
        </motion.div>
      )}

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </ParentContainer>
  );
};

export default ParentDashboardNew;
