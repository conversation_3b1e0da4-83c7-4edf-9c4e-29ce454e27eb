package com.example.childreward.repository;

import com.example.childreward.entity.RewardPool;
import com.example.childreward.entity.RewardPool.PoolType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RewardPoolRepository extends JpaRepository<RewardPool, Long> {
    
    List<RewardPool> findByIsEnabledTrue();
    
    List<RewardPool> findByIsEnabledTrueAndPoolType(PoolType poolType);
    
    RewardPool findByIdAndIsEnabledTrue(Long id);
} 