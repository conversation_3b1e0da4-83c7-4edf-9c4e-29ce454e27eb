# 儿童行为激励系统 (Child Reward System)

一个通过游戏化任务和奖励机制，帮助家长正面引导和激励儿童养成良好行为习惯的Web应用程序。

---

## 目录

- [1. 项目简介](#1-项目简介)
- [2. 核心功能](#2-核心功能)
- [3. 技术架构](#3-技术架构)
  - [3.1. 后端架构](#31-后端架构)
  - [3.2. 前端架构](#32-前端架构)
- [4. 快速开始](#4-快速开始)
  - [4.1. 环境准备](#41-环境准备)
  - [4.2. 本地开发](#42-本地开发)
  - [4.3. 生产部署](#43-生产部署)
- [5. 项目结构](#5-项目结构)
- [6. 构建和部署](#6-构建和部署)

---

## 1. 项目简介

**儿童行为激励系统** 是一个现代化的Web应用程序，采用前后端分离架构设计。系统通过游戏化的任务管理和积分奖励机制，帮助家长更好地引导孩子养成良好的行为习惯。

### 主要特色
- 🎮 **游戏化体验** - 通过任务、积分、奖励等机制提升孩子参与度
- 👨‍👩‍👧‍👦 **双角色设计** - 家长端管理，儿童端执行，界面分别优化
- 📱 **响应式设计** - 支持手机、平板、电脑等多种设备
- 🎨 **精美界面** - 采用现代化UI设计，动画效果丰富
- 📊 **数据可视化** - 直观展示孩子的成长轨迹和任务完成情况

## 2. 核心功能

### 👨‍👩‍👧‍👦 家长端功能
- **任务管理** - 创建、编辑、删除日常任务，设置任务类型（必做/选做）、积分奖励、预计用时等
- **奖励商城管理** - 上架各类奖励商品，设定兑换所需积分，管理商品状态
- **审批中心** - 审核孩子完成的任务，批准或拒绝任务完成申请
- **数据统计** - 查看孩子的任务完成情况、积分变化趋势、成长数据分析
- **定时任务** - 设置每日自动分配的重复性任务

### 👶 儿童端功能
- **任务看板** - 查看今日任务列表，按状态分类显示（未开始/进行中/已完成等）
- **任务执行** - 开始任务、完成任务，记录实际用时
- **积分查看** - 实时查看当前积分余额和积分变化记录
- **奖励兑换** - 浏览奖励商城，使用积分兑换心仪的奖励
- **成长轨迹** - 查看个人任务完成统计和成长数据

### 🎮 系统特色
- **游戏化设计** - 积分奖励、等级系统、成就徽章等游戏化元素
- **智能提醒** - 任务截止提醒、积分变化通知
- **数据可视化** - 图表展示任务完成趋势、积分变化曲线
- **响应式界面** - 适配手机、平板、电脑等多种设备

## 3. 技术架构

本项目采用现代化的前后端分离架构，确保高性能、高可维护性和良好的扩展性。

### 3.1. 后端技术栈

基于 **Spring Boot** 构建的 RESTful API 服务

| 技术 | 版本 | 用途 |
|------|------|------|
| **Java** | 8+ | 开发语言 |
| **Spring Boot** | 2.7.18 | 核心框架 |
| **Spring Data JPA** | - | 数据持久层 |
| **MySQL** | 8.0+ | 关系型数据库 |
| **Maven** | 3.6+ | 构建工具 |
| **Lombok** | - | 减少样板代码 |
| **Hibernate** | - | ORM框架 |

### 3.2. 前端技术栈

基于 **React** 构建的现代化单页面应用

| 技术 | 版本 | 用途 |
|------|------|------|
| **React** | 18 | 核心框架 |
| **Vite** | - | 构建工具 |
| **React Router** | - | 路由管理 |
| **Styled Components** | - | CSS-in-JS样式 |
| **Framer Motion** | - | 动画效果 |
| **Axios** | - | HTTP客户端 |
| **Recharts** | - | 数据可视化 |
| **React Three Fiber** | - | 3D渲染 |
| **React Beautiful DnD** | - | 拖拽功能 |

### 3.3. 架构特点

- **前后端分离** - 独立开发、部署和扩展
- **RESTful API** - 标准化的接口设计
- **响应式设计** - 适配多种设备屏幕
- **组件化开发** - 可复用的UI组件
- **状态管理** - 高效的数据流管理

## 4. 快速开始

### 4.1. 环境准备

在开始之前，请确保您的开发环境已安装以下软件：

| 软件 | 版本要求 | 用途 |
|------|----------|------|
| **Java JDK** | 21+ | 后端运行环境 |
| **Maven** | 3.6+ | 后端构建工具 |
| **Node.js** | 18+ | 前端运行环境 |
| **MySQL** | 8.0+ | 数据库 |

### 4.2. 本地开发

#### 1. 克隆项目
```bash
git clone <repository-url>
cd child-reward-system
```

#### 2. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE crs CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'crs_user'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON crs.* TO 'crs_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. 启动后端服务
```bash
cd backend
mvn spring-boot:run
```
后端服务将在 `http://localhost:18080` 启动

#### 4. 启动前端服务
```bash
cd frontend
npm install
npm run dev
```
前端应用将在 `http://localhost:5173` 启动

#### 5. 访问应用
- 前端应用：http://localhost:5173
- 后端API：http://localhost:18080/api
- API文档：http://localhost:18080/swagger-ui.html（如果已配置）

### 4.3. 生产部署

#### 方式一：一键构建部署包
```bash
# Windows PowerShell
.\build-package.ps1

# 生成文件：
# - packages/crs-backend.jar
# - packages/crs-frontend.zip
```

#### 方式二：分别构建
```bash
# 构建后端
cd backend
.\build-simple.bat

# 构建前端
cd frontend
npm run build
```

#### 部署到服务器
1. **后端部署**：
   ```bash
   # 上传 crs-backend.jar 到服务器
   java -jar crs-backend.jar

   # 或指定配置文件
   java -jar -Dspring.profiles.active=prod crs-backend.jar
   ```

2. **前端部署**：
   ```bash
   # 解压 crs-frontend.zip 到 Web 服务器目录
   # 配置 nginx 或 Apache 支持 SPA 路由
   ```

详细构建说明请参考 [BUILD.md](BUILD.md)

## 5. 项目结构

```
child-reward-system/
├── backend/                    # 后端 Spring Boot 项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/example/childreward/
│   │   │   │   ├── controller/     # REST API 控制器
│   │   │   │   ├── service/        # 业务逻辑层
│   │   │   │   ├── entity/         # 数据实体
│   │   │   │   ├── repository/     # 数据访问层
│   │   │   │   └── config/         # 配置类
│   │   │   └── resources/
│   │   │       ├── application.yml # 应用配置
│   │   │       └── data.sql        # 初始化数据
│   │   └── test/                   # 单元测试
│   ├── pom.xml                     # Maven 配置
│   └── build-simple.bat            # 构建脚本
│
├── frontend/                   # 前端 React 项目
│   ├── src/
│   │   ├── components/         # 可复用组件
│   │   ├── pages/              # 页面组件
│   │   │   ├── parent/         # 家长端页面
│   │   │   └── child/          # 儿童端页面
│   │   ├── api/                # API 接口
│   │   ├── utils/              # 工具函数
│   │   ├── styles/             # 样式文件
│   │   └── App.jsx             # 根组件
│   ├── public/                 # 静态资源
│   ├── package.json            # npm 配置
│   ├── vite.config.js          # Vite 配置
│   └── build-*.bat             # 构建脚本
│
├── packages/                   # 构建输出目录
│   ├── crs-backend.jar         # 后端 JAR 包
│   └── crs-frontend.zip        # 前端静态文件包
│
├── build-package.ps1           # 一键构建脚本
├── BUILD.md                    # 构建说明文档
└── README.md                   # 项目说明文档
```

## 6. 构建和部署

### 6.1. 快速构建

**一键构建所有包**：
```powershell
.\build-package.ps1
```

**单独构建**：
```bash
# 后端
cd backend && .\build-simple.bat

# 前端
cd frontend && npm run build
```

### 6.2. 配置说明

**后端配置** (`backend/src/main/resources/application.yml`)：
```yaml
spring:
  datasource:
    url: *******************************
    username: root
    password: 123456
server:
  port: 18080
```

**前端配置** (构建时自动配置API地址)：
- 开发环境：`http://localhost:18080/api`
- 生产环境：根据部署环境配置

### 6.3. 部署建议

1. **后端部署**：
   - 使用 `java -jar crs-backend.jar` 直接运行
   - 或配置为系统服务（systemd、Windows Service等）
   - 建议使用反向代理（nginx）

2. **前端部署**：
   - 解压到 Web 服务器目录
   - 配置 SPA 路由支持
   - 启用 gzip 压缩

3. **数据库**：
   - 创建专用数据库和用户
   - 定期备份数据
   - 配置适当的连接池

更多详细信息请参考 [BUILD.md](BUILD.md) 文档。