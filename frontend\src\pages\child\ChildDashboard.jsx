import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import { childApi, pointApi } from '../../api/apiService';
import {
  ChildContainer,
  ChildHeader,
  ChildTitle,
  ChildSubtitle,
  ChildGrid,
  ChildCard,
  ChildCardTitle,
  ChildCardContent,
  ChildButton,
  ChildTabBar,
  ChildTabItem,
  ChildBadge
} from '../../components/child/ChildAppleUI';
import { ApplePageTransition } from '../../components/apple/AppleAnimations';

const ChildDashboard = () => {
  const navigate = useNavigate();
  const [totalPoints, setTotalPoints] = useState(0);
  const [todayTasks, setTodayTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // 获取今日任务
      const tasksResponse = await childApi.getTodayTasks();
      setTodayTasks(tasksResponse.data || []);

      // 获取积分记录来计算总积分
      const pointsResponse = await pointApi.getRecentPointChanges(30);
      const pointsData = pointsResponse.data || [];
      const totalPoints = pointsData.reduce((sum, record) => sum + (record.points || 0), 0);
      setTotalPoints(Math.max(0, totalPoints));

    } catch (error) {
      console.error('获取仪表板数据失败:', error);
      // 设置默认值避免页面崩溃
      setTotalPoints(0);
      setTodayTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const handleNavigation = (path) => {
    navigate(path);
  };

  const getTaskStats = () => {
    const completed = todayTasks.filter(task => {
      const status = task.status ? task.status.toString().toUpperCase() : '';
      return ['COMPLETED', 'APPROVED', 'COMPLETE'].includes(status);
    }).length;
    const total = todayTasks.length;
    return { completed, total };
  };

  const { completed, total } = getTaskStats();

  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  if (loading) {
    return (
      <ChildContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          flexDirection: 'column',
          gap: AppleDesignSystem.spacing.md
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: `4px solid ${AppleDesignSystem.colors.semantic.systemFill}`,
            borderTop: `4px solid ${AppleDesignSystem.colors.child.primary}`,
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div style={{
            fontSize: AppleDesignSystem.typography.textStyles.headline.fontSize,
            color: AppleDesignSystem.colors.semantic.secondaryLabel
          }}>
            正在加载...
          </div>
        </div>

        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </ChildContainer>
    );
  }

  return (
    <ApplePageTransition>
      <ChildContainer>
        {/* 头部问候 */}
        <ChildHeader>
          <div>
            <ChildTitle>{getTimeBasedGreeting()}，小朋友！👋</ChildTitle>
            <ChildSubtitle>今天也要加油哦～</ChildSubtitle>
          </div>
          <div style={{
            fontSize: '48px',
            background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
            borderRadius: '50%',
            width: '64px',
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            👶
          </div>
        </ChildHeader>

        {/* 积分展示 */}
        <ChildCard style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          marginBottom: AppleDesignSystem.spacing.lg
        }}>
          <div style={{ color: 'white', textAlign: 'center' }}>
            <div style={{ fontSize: '36px', marginBottom: '8px' }}>💰</div>
            <ChildCardTitle style={{ color: 'white', margin: 0, fontSize: '32px' }}>
              {totalPoints}
            </ChildCardTitle>
            <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
              我的积分
            </ChildCardContent>
          </div>
        </ChildCard>

        {/* 主要功能区域 */}
        <ChildGrid>
          {/* 今日任务 */}
          <ChildCard
            interactive
            onClick={() => handleNavigation('/child/tasks')}
            style={{ cursor: 'pointer', position: 'relative' }}
          >
            {total > 0 && (
              <ChildBadge
                style={{
                  position: 'absolute',
                  top: AppleDesignSystem.spacing.sm,
                  right: AppleDesignSystem.spacing.sm
                }}
              >
                {completed}/{total}
              </ChildBadge>
            )}
            <div style={{
              fontSize: '48px',
              marginBottom: AppleDesignSystem.spacing.md,
              textAlign: 'center'
            }}>
              📝
            </div>
            <ChildCardTitle style={{ margin: 0, textAlign: 'center' }}>
              今日任务
            </ChildCardTitle>
            <ChildCardContent style={{ textAlign: 'center', margin: 0 }}>
              {total > 0 ? `${completed}/${total} 已完成` : '暂无任务'}
            </ChildCardContent>
          </ChildCard>

          {/* 奖励中心 */}
          <ChildCard
            interactive
            onClick={() => handleNavigation('/child/rewards')}
            style={{ cursor: 'pointer' }}
          >
            <div style={{
              fontSize: '48px',
              marginBottom: AppleDesignSystem.spacing.md,
              textAlign: 'center'
            }}>
              🎁
            </div>
            <ChildCardTitle style={{ margin: 0, textAlign: 'center' }}>
              奖励中心
            </ChildCardTitle>
            <ChildCardContent style={{ textAlign: 'center', margin: 0 }}>
              兑换心仪的奖励
            </ChildCardContent>
          </ChildCard>

          {/* 转盘游戏 */}
          <ChildCard
            interactive
            onClick={() => handleNavigation('/child/spin')}
            style={{ cursor: 'pointer' }}
          >
            <div style={{
              fontSize: '48px',
              marginBottom: AppleDesignSystem.spacing.md,
              textAlign: 'center'
            }}>
              🎯
            </div>
            <ChildCardTitle style={{ margin: 0, textAlign: 'center' }}>
              幸运转盘
            </ChildCardTitle>
            <ChildCardContent style={{ textAlign: 'center', margin: 0 }}>
              转转看有什么惊喜
            </ChildCardContent>
          </ChildCard>

          {/* 积分记录 */}
          <ChildCard
            interactive
            onClick={() => handleNavigation('/child/points')}
            style={{ cursor: 'pointer' }}
          >
            <div style={{
              fontSize: '48px',
              marginBottom: AppleDesignSystem.spacing.md,
              textAlign: 'center'
            }}>
              📊
            </div>
            <ChildCardTitle style={{ margin: 0, textAlign: 'center' }}>
              积分记录
            </ChildCardTitle>
            <ChildCardContent style={{ textAlign: 'center', margin: 0 }}>
              查看我的成长历程
            </ChildCardContent>
          </ChildCard>

          {/* 抽卡游戏 */}
          <ChildCard
            interactive
            onClick={() => handleNavigation('/child/cards')}
            style={{ cursor: 'pointer' }}
          >
            <div style={{
              fontSize: '48px',
              marginBottom: AppleDesignSystem.spacing.md,
              textAlign: 'center'
            }}>
              🃏
            </div>
            <ChildCardTitle style={{ margin: 0, textAlign: 'center' }}>
              抽卡游戏
            </ChildCardTitle>
            <ChildCardContent style={{ textAlign: 'center', margin: 0 }}>
              试试手气抽卡片
            </ChildCardContent>
          </ChildCard>
        </ChildGrid>

      {/* 底部导航 */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <ChildTabBar>
          <ChildTabItem
            active={true}
            onClick={() => handleNavigation('/child')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
              🏠
            </div>
            <div style={{ fontSize: '12px' }}>
              首页
            </div>
          </ChildTabItem>

          <ChildTabItem
            active={false}
            onClick={() => handleNavigation('/child/tasks')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
              📝
            </div>
            <div style={{ fontSize: '12px' }}>
              任务
            </div>
          </ChildTabItem>

          <ChildTabItem
            active={false}
            onClick={() => handleNavigation('/child/spin')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
              🎯
            </div>
            <div style={{ fontSize: '12px' }}>
              转盘
            </div>
          </ChildTabItem>

          <ChildTabItem
            active={false}
            onClick={() => handleNavigation('/child/points')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
              📊
            </div>
            <div style={{ fontSize: '12px' }}>
              记录
            </div>
          </ChildTabItem>
        </ChildTabBar>
      </motion.div>
      </ChildContainer>
    </ApplePageTransition>
  );
};

export default ChildDashboard;
