import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import { pointApi } from '../../api/apiService';
import {
  ChildContainer,
  ChildHeader,
  ChildTitle,
  ChildSubtitle,
  ChildGrid,
  ChildCard,
  ChildCardTitle,
  ChildCardContent,
  ChildButton,
  ChildTabBar,
  ChildTabItem
} from '../../components/child/ChildAppleUI';
import { ApplePageTransition } from '../../components/apple/AppleAnimations';

const PointHistoryNew = () => {
  const navigate = useNavigate();
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalEarned: 0,
    totalSpent: 0,
    currentBalance: 0
  });

  useEffect(() => {
    fetchPointHistory();
  }, []);

  const fetchPointHistory = async () => {
    try {
      setLoading(true);
      const response = await pointApi.getRecentPointChanges(30);
      const pointsData = response.data || [];
      
      setRecords(pointsData);
      
      // 计算统计数据
      const earned = pointsData.filter(record => record.points > 0).reduce((sum, record) => sum + record.points, 0);
      const spent = Math.abs(pointsData.filter(record => record.points < 0).reduce((sum, record) => sum + record.points, 0));
      const balance = pointsData.reduce((sum, record) => sum + record.points, 0);
      
      setStats({
        totalEarned: earned,
        totalSpent: spent,
        currentBalance: Math.max(0, balance)
      });
    } catch (error) {
      console.error('获取积分记录失败:', error);
      setRecords([]);
      setStats({ totalEarned: 0, totalSpent: 0, currentBalance: 0 });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return '未知时间';
    
    try {
      const date = new Date(dateStr);
      const now = new Date();
      const diffMs = now - date;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) return '今天';
      if (diffDays === 1) return '昨天';
      if (diffDays <= 7) return `${diffDays}天前`;
      return date.toLocaleDateString();
    } catch (error) {
      return '未知时间';
    }
  };

  const getRecordIcon = (points) => {
    if (points > 0) return '💰';
    if (points < 0) return '💸';
    return '📝';
  };

  const getRecordColor = (points) => {
    if (points > 0) return AppleDesignSystem.colors.semantic.systemGreen;
    if (points < 0) return AppleDesignSystem.colors.semantic.systemRed;
    return AppleDesignSystem.colors.semantic.systemGray;
  };

  if (loading) {
    return (
      <ChildContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          flexDirection: 'column',
          gap: AppleDesignSystem.spacing.md
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: `4px solid ${AppleDesignSystem.colors.semantic.systemFill}`,
            borderTop: `4px solid ${AppleDesignSystem.colors.child.primary}`,
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div style={{
            fontSize: AppleDesignSystem.typography.textStyles.headline.fontSize,
            color: AppleDesignSystem.colors.semantic.secondaryLabel
          }}>
            正在加载记录...
          </div>
        </div>
        
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </ChildContainer>
    );
  }

  return (
    <ApplePageTransition>
      <ChildContainer>
        <ChildHeader>
          <div>
            <ChildTitle>📊 积分记录</ChildTitle>
            <ChildSubtitle>查看我的成长历程</ChildSubtitle>
          </div>
          <div style={{
            fontSize: '48px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '50%',
            width: '64px',
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            📈
          </div>
        </ChildHeader>

        {/* 统计卡片 */}
        <ChildGrid>
          <ChildCard style={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '36px', marginBottom: '8px' }}>💰</div>
              <ChildCardTitle style={{ color: 'white', margin: 0 }}>
                {stats.currentBalance}
              </ChildCardTitle>
              <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
                当前积分
              </ChildCardContent>
            </div>
          </ChildCard>

          <ChildCard style={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '36px', marginBottom: '8px' }}>📈</div>
              <ChildCardTitle style={{ color: 'white', margin: 0 }}>
                {stats.totalEarned}
              </ChildCardTitle>
              <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
                总获得
              </ChildCardContent>
            </div>
          </ChildCard>

          <ChildCard style={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '36px', marginBottom: '8px' }}>📉</div>
              <ChildCardTitle style={{ color: 'white', margin: 0 }}>
                {stats.totalSpent}
              </ChildCardTitle>
              <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
                总消费
              </ChildCardContent>
            </div>
          </ChildCard>
        </ChildGrid>

        {/* 积分记录列表 */}
        <ChildCard fullWidth>
          <ChildCardTitle>
            📋 最近记录
          </ChildCardTitle>
          <ChildCardContent>
            {records.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: AppleDesignSystem.spacing.xl,
                color: AppleDesignSystem.colors.semantic.secondaryLabel
              }}>
                <div style={{ fontSize: '48px', marginBottom: AppleDesignSystem.spacing.md }}>📝</div>
                <div>暂无积分记录</div>
              </div>
            ) : (
              <div style={{ display: 'grid', gap: AppleDesignSystem.spacing.md }}>
                {records.slice(0, 10).map((record, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: AppleDesignSystem.spacing.md,
                      padding: AppleDesignSystem.spacing.md,
                      background: AppleDesignSystem.colors.semantic.systemFill,
                      borderRadius: AppleDesignSystem.borderRadius.medium,
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <div style={{
                      fontSize: '24px',
                      width: '40px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: getRecordColor(record.points),
                      borderRadius: AppleDesignSystem.borderRadius.small,
                      color: 'white'
                    }}>
                      {getRecordIcon(record.points)}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: AppleDesignSystem.typography.textStyles.subheadline.fontSize,
                        fontWeight: '600',
                        color: AppleDesignSystem.colors.semantic.label,
                        marginBottom: '2px'
                      }}>
                        {record.description || record.reason || '积分变动'}
                      </div>
                      <div style={{
                        fontSize: AppleDesignSystem.typography.textStyles.footnote.fontSize,
                        color: AppleDesignSystem.colors.semantic.secondaryLabel
                      }}>
                        {formatDate(record.createTime || record.createdAt)}
                      </div>
                    </div>
                    <div style={{
                      fontSize: AppleDesignSystem.typography.textStyles.subheadline.fontSize,
                      fontWeight: '600',
                      color: getRecordColor(record.points)
                    }}>
                      {record.points > 0 ? '+' : ''}{record.points}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ChildCardContent>
        </ChildCard>

        <ChildTabBar>
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🏠</div>
            <div style={{ fontSize: '12px' }}>首页</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child/tasks')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📝</div>
            <div style={{ fontSize: '12px' }}>任务</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child/spin')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🎯</div>
            <div style={{ fontSize: '12px' }}>转盘</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={true}
            onClick={() => navigate('/child/points')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
            <div style={{ fontSize: '12px' }}>记录</div>
          </ChildTabItem>
        </ChildTabBar>
      </ChildContainer>
    </ApplePageTransition>
  );
};

export default PointHistoryNew;