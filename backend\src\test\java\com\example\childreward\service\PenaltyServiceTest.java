package com.example.childreward.service;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.repository.TaskRepository;
import com.example.childreward.service.SystemConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PenaltyServiceTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private SystemConfigService systemConfigService;

    @Mock
    private PointService pointService;

    @InjectMocks
    private PenaltyService penaltyService;

    @BeforeEach
    void setUp() {
        // 每个测试用例执行前，清除mock的交互记录
        reset(taskRepository, systemConfigService, pointService);
    }

    @Test
    void applyPenalty_whenUncompletedTasksExceedThreshold_shouldDeductPoints() {
        // --- Given: 准备测试数据和mock行为 ---
        int penaltyThreshold = 3;
        int penaltyPoints = 10;
        int uncompletedTaskCount = 5;

        // 模拟惩罚功能已启用
        when(systemConfigService.getBoolean("OVERDUE_PENALTY_ENABLED", false)).thenReturn(true);
        // 模拟配置服务返回阈值和扣分值
        when(systemConfigService.getInt("PENALTY_THRESHOLD", 3)).thenReturn(penaltyThreshold);
        when(systemConfigService.getInt("PENALTY_POINTS", 10)).thenReturn(penaltyPoints);

        // 模拟仓库返回超过阈值数量的未完成任务
        List<Task> uncompletedTasks = IntStream.range(0, uncompletedTaskCount)
                .mapToObj(i -> new Task())
                .collect(Collectors.toList());
        when(taskRepository.findByDueDateAndStatusIn(any(LocalDate.class), anyList())).thenReturn(uncompletedTasks);

        // --- When: 执行被测试的方法 ---
        penaltyService.applyPenaltyForUncompletedTasks();

        // --- Then: 验证结果是否符合预期 ---
        // 验证pointService的deductPoints方法被调用了一次
        ArgumentCaptor<Integer> pointsCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> descriptionCaptor = ArgumentCaptor.forClass(String.class);

        verify(pointService, times(1)).deductPoints(
                pointsCaptor.capture(),
                eq(PointRecord.ChangeType.TASK_PENALTY),
                descriptionCaptor.capture(),
                eq(null)
        );

        // 验证扣除的分数和描述是否正确
        assertEquals(penaltyPoints, pointsCaptor.getValue());
        assertEquals(String.format("每日任务未完成惩罚： %d 个任务未完成", uncompletedTaskCount), descriptionCaptor.getValue());
    }

    @Test
    void applyPenalty_whenUncompletedTasksDoNotExceedThreshold_shouldNotDeductPoints() {
        // --- Given ---
        int penaltyThreshold = 3;
        int uncompletedTaskCount = 2; // 未超过阈值

        // 模拟惩罚功能已启用
        when(systemConfigService.getBoolean("OVERDUE_PENALTY_ENABLED", false)).thenReturn(true);
        when(systemConfigService.getInt("PENALTY_THRESHOLD", 3)).thenReturn(penaltyThreshold);
        when(systemConfigService.getInt("PENALTY_POINTS", 10)).thenReturn(10);

        List<Task> uncompletedTasks = IntStream.range(0, uncompletedTaskCount)
                .mapToObj(i -> new Task())
                .collect(Collectors.toList());
        when(taskRepository.findByDueDateAndStatusIn(any(LocalDate.class), anyList())).thenReturn(uncompletedTasks);

        // --- When ---
        penaltyService.applyPenaltyForUncompletedTasks();

        // --- Then ---
        // 验证pointService的deductPoints方法从未被调用
        verify(pointService, never()).deductPoints(anyInt(), any(), anyString(), any());
    }

    @Test
    void applyPenalty_whenPenaltyIsDisabledBySwitch_shouldDoNothing() {
        // --- Given ---
        // 模拟惩罚功能总开关被关闭
        when(systemConfigService.getBoolean("OVERDUE_PENALTY_ENABLED", false)).thenReturn(false);

        // --- When ---
        penaltyService.applyPenaltyForUncompletedTasks();

        // --- Then ---
        // 验证后续的数据库查询和扣分操作都未被执行
        verify(taskRepository, never()).findByDueDateAndStatusIn(any(), any());
        verify(pointService, never()).deductPoints(anyInt(), any(), anyString(), any());
        // 验证其他配置项也不会被查询
        verify(systemConfigService, never()).getInt("PENALTY_THRESHOLD", 3);
        verify(systemConfigService, never()).getInt("PENALTY_POINTS", 10);
    }

    @Test
    void applyPenalty_whenPenaltyConfigIsInvalid_shouldDoNothing() {
        // --- Given ---
        // 模拟惩罚功能已启用但配置无效 (阈值为0)
        when(systemConfigService.getBoolean("OVERDUE_PENALTY_ENABLED", false)).thenReturn(true);
        when(systemConfigService.getInt("PENALTY_THRESHOLD", 3)).thenReturn(0);
        when(systemConfigService.getInt("PENALTY_POINTS", 10)).thenReturn(10);

        // --- When ---
        penaltyService.applyPenaltyForUncompletedTasks();

        // --- Then ---
        // 验证后续的数据库查询和扣分操作都未被执行
        verify(taskRepository, never()).findByDueDateAndStatusIn(any(), any());
        verify(pointService, never()).deductPoints(anyInt(), any(), anyString(), any());
    }
} 