package com.example.childreward.repository;

import com.example.childreward.entity.PointBalance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PointBalanceRepository extends JpaRepository<PointBalance, Long> {

    // 由于积分余额只有一条记录，使用LIMIT 1优化查询
    @Query(value = "SELECT * FROM point_balance LIMIT 1", nativeQuery = true)
    Optional<PointBalance> findCurrentBalance();

    // 提供一个方便的方法获取当前积分，避免全表查询
    default PointBalance getCurrentBalance() {
        return findCurrentBalance().orElse(new PointBalance());
    }
}