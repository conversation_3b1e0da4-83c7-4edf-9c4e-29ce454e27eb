import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import { childApi } from '../../api/apiService';
import {
  ChildContainer,
  ChildHeader,
  ChildTitle,
  ChildSubtitle,
  ChildGrid,
  ChildCard,
  ChildCardTitle,
  ChildCardContent,
  ChildButton,
  ChildTabBar,
  ChildTabItem
} from '../../components/child/ChildAppleUI';
import { ApplePageTransition } from '../../components/apple/AppleAnimations';

const SpinWheelNew = () => {
  const navigate = useNavigate();
  const [isSpinning, setIsSpinning] = useState(false);
  const [result, setResult] = useState(null);
  const [rotation, setRotation] = useState(0);
  const [rewardPools, setRewardPools] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRewardPools();
  }, []);

  const fetchRewardPools = async () => {
    try {
      setLoading(true);
      const response = await childApi.getAvailableRewardPools();
      setRewardPools(response.data || []);
    } catch (error) {
      console.error('获取奖励池失败:', error);
      setRewardPools([]);
    } finally {
      setLoading(false);
    }
  };

  const spinWheel = async () => {
    if (isSpinning || rewardPools.length === 0) return;

    setIsSpinning(true);
    setResult(null);

    try {
      // 使用第一个奖励池进行抽奖
      const poolId = rewardPools[0].id;
      
      // 转盘动画
      const newRotation = rotation + 1440 + Math.random() * 720; // 至少转4圈
      setRotation(newRotation);

      // 等待转盘动画
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 调用抽奖API
      const drawResponse = await childApi.drawReward(poolId);
      
      if (drawResponse.data && drawResponse.data.reward) {
        setResult({
          success: true,
          reward: drawResponse.data.reward
        });
      } else {
        setResult({
          success: false,
          message: '很遗憾，这次没有中奖！'
        });
      }
    } catch (error) {
      console.error('抽奖失败:', error);
      setResult({
        success: false,
        message: '抽奖失败，请重试'
      });
    } finally {
      setIsSpinning(false);
    }
  };

  const resetSpin = () => {
    setResult(null);
  };

  if (loading) {
    return (
      <ChildContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          flexDirection: 'column',
          gap: AppleDesignSystem.spacing.md
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: `4px solid ${AppleDesignSystem.colors.semantic.systemFill}`,
            borderTop: `4px solid ${AppleDesignSystem.colors.child.primary}`,
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div style={{
            fontSize: AppleDesignSystem.typography.textStyles.headline.fontSize,
            color: AppleDesignSystem.colors.semantic.secondaryLabel
          }}>
            正在加载转盘...
          </div>
        </div>
        
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </ChildContainer>
    );
  }

  return (
    <ApplePageTransition>
      <ChildContainer>
        <ChildHeader>
          <div>
            <ChildTitle>🎯 幸运转盘</ChildTitle>
            <ChildSubtitle>转转看有什么惊喜！</ChildSubtitle>
          </div>
        </ChildHeader>

        <ChildGrid>
          <ChildCard fullWidth style={{ textAlign: 'center', padding: '40px' }}>
            {!result ? (
              <div>
                {/* 转盘 */}
                <motion.div
                  animate={{ rotate: rotation }}
                  transition={{ duration: 3, ease: "easeOut" }}
                  style={{
                    width: '200px',
                    height: '200px',
                    margin: '0 auto 30px',
                    borderRadius: '50%',
                    background: 'conic-gradient(from 0deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD, #FF6B6B)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '60px',
                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                    position: 'relative'
                  }}
                >
                  🎯
                  {/* 指针 */}
                  <div style={{
                    position: 'absolute',
                    top: '-10px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: '0',
                    height: '0',
                    borderLeft: '15px solid transparent',
                    borderRight: '15px solid transparent',
                    borderBottom: '30px solid #333',
                    zIndex: 10
                  }} />
                </motion.div>

                <ChildCardTitle>
                  {isSpinning ? '转盘转动中...' : '点击开始转盘'}
                </ChildCardTitle>
                
                <ChildCardContent>
                  {isSpinning ? '请稍等，正在为你抽取奖励' : 
                   rewardPools.length > 0 ? `当前奖励池：${rewardPools[0].name}` : '暂无可用奖励池'}
                </ChildCardContent>

                {!isSpinning && rewardPools.length > 0 && (
                  <ChildButton 
                    onClick={spinWheel}
                    style={{ 
                      marginTop: '20px',
                      background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
                      border: 'none',
                      color: 'white'
                    }}
                  >
                    开始转盘
                  </ChildButton>
                )}

                {rewardPools.length === 0 && (
                  <ChildCardContent style={{ 
                    marginTop: '20px',
                    color: AppleDesignSystem.colors.semantic.secondaryLabel 
                  }}>
                    暂时没有可用的奖励池，请联系家长设置
                  </ChildCardContent>
                )}
              </div>
            ) : (
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6 }}
              >
                <div style={{ fontSize: '120px', marginBottom: '20px' }}>
                  {result.success ? '🎉' : '😔'}
                </div>
                
                <ChildCardTitle>
                  {result.success ? '恭喜你获得了' : '很遗憾'}
                </ChildCardTitle>
                
                {result.success ? (
                  <div style={{ 
                    fontSize: '32px', 
                    margin: '20px 0',
                    padding: '20px',
                    background: 'linear-gradient(135deg, #FFD700, #FFA500)',
                    borderRadius: '16px',
                    color: 'white',
                    fontWeight: 'bold'
                  }}>
                    {result.reward.name}
                  </div>
                ) : (
                  <ChildCardContent style={{ margin: '20px 0' }}>
                    {result.message}
                  </ChildCardContent>
                )}
                
                <ChildButton 
                  onClick={resetSpin}
                  style={{ marginTop: '20px' }}
                >
                  再转一次
                </ChildButton>
              </motion.div>
            )}
          </ChildCard>
        </ChildGrid>

        <ChildTabBar>
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🏠</div>
            <div style={{ fontSize: '12px' }}>首页</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child/tasks')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📝</div>
            <div style={{ fontSize: '12px' }}>任务</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={true}
            onClick={() => navigate('/child/spin')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🎯</div>
            <div style={{ fontSize: '12px' }}>转盘</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child/points')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
            <div style={{ fontSize: '12px' }}>记录</div>
          </ChildTabItem>
        </ChildTabBar>
      </ChildContainer>
    </ApplePageTransition>
  );
};

export default SpinWheelNew;