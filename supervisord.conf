[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log
priority=100

[program:spring-boot]
command=java %(ENV_JAVA_OPTS)s -Dspring.datasource.url=********************************************************************************************************************************************************** -Dspring.datasource.username=%(ENV_MYSQL_USERNAME)s -Dspring.datasource.password=%(ENV_MYSQL_PASSWORD)s -Dspring.profiles.active=%(ENV_SPRING_PROFILES_ACTIVE)s -jar /app/backend.jar
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/spring-boot.err.log
stdout_logfile=/var/log/supervisor/spring-boot.out.log
priority=200
startsecs=30
startretries=3