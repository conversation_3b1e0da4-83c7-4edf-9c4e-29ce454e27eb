import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';

// 进度条容器
const ProgressContainer = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};
  margin: 0 ${AppleDesignSystem.spacing.xl};
  margin-bottom: ${AppleDesignSystem.spacing.lg};
`;

// 进度信息区域
const ProgressInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${AppleDesignSystem.spacing.md};
`;

const InfoSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

const InfoLabel = styled.span`
  font-size: ${AppleDesignSystem.typography.textStyles.caption1.fontSize};
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  font-weight: 500;
`;

const InfoValue = styled.span`
  font-size: ${AppleDesignSystem.typography.textStyles.title3.fontSize};
  font-weight: 700;
  color: ${props => props.color || AppleDesignSystem.colors.semantic.label};
`;

// 主进度条
const MainProgressBar = styled.div`
  position: relative;
  height: 12px;
  background: ${AppleDesignSystem.colors.semantic.quaternarySystemFill};
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: ${AppleDesignSystem.spacing.md};
`;

const ProgressFill = styled(motion.div)`
  height: 100%;
  border-radius: 8px;
  background: ${props => props.gradient};
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.3), transparent);
    border-radius: 8px 8px 0 0;
  }
`;

// 分段指示器
const SegmentIndicator = styled.div`
  display: flex;
  justify-content: center;
  gap: 4px;
  align-items: center;
`;

const SegmentDot = styled(motion.div)`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.completed ? props.color : AppleDesignSystem.colors.semantic.quaternarySystemFill};
`;

const TaskProgressBar = ({ totalTasks, completedTasks }) => {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  
  const progress = totalTasks > 0 ? completedTasks / totalTasks : 0;
  
  // 计算进度颜色
  const getProgressColor = () => {
    if (progress >= 1.0) {
      return AppleDesignSystem.colors.system.green;
    } else if (progress >= 0.5) {
      return AppleDesignSystem.colors.system.blue;
    } else {
      return AppleDesignSystem.colors.system.orange;
    }
  };
  
  // 计算渐变
  const getProgressGradient = () => {
    if (progress >= 1.0) {
      return `linear-gradient(90deg, ${AppleDesignSystem.colors.system.green}, ${AppleDesignSystem.colors.system.green}CC)`;
    } else if (progress >= 0.5) {
      return `linear-gradient(90deg, ${AppleDesignSystem.colors.system.blue}, ${AppleDesignSystem.colors.system.teal})`;
    } else {
      return `linear-gradient(90deg, ${AppleDesignSystem.colors.system.orange}, ${AppleDesignSystem.colors.system.yellow})`;
    }
  };
  
  // 动画效果
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progress);
    }, 100);
    
    return () => clearTimeout(timer);
  }, [progress]);
  
  return (
    <ProgressContainer
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      {/* 进度信息 */}
      <ProgressInfo>
        <InfoSection>
          <InfoLabel>今日进度</InfoLabel>
          <InfoValue>{completedTasks}/{totalTasks}</InfoValue>
        </InfoSection>
        
        <InfoSection style={{ alignItems: 'flex-end' }}>
          <InfoLabel>完成率</InfoLabel>
          <InfoValue color={getProgressColor()}>
            {Math.round(progress * 100)}%
          </InfoValue>
        </InfoSection>
      </ProgressInfo>
      
      {/* 主进度条 */}
      <MainProgressBar>
        <ProgressFill
          gradient={getProgressGradient()}
          initial={{ width: 0 }}
          animate={{ width: `${animatedProgress * 100}%` }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        />
      </MainProgressBar>
      
      {/* 分段指示器 */}
      {totalTasks > 0 && (
        <SegmentIndicator>
          {Array.from({ length: totalTasks }, (_, index) => (
            <SegmentDot
              key={index}
              completed={index < completedTasks}
              color={getProgressColor()}
              initial={{ scale: 0 }}
              animate={{ 
                scale: index < completedTasks ? 1.2 : 1.0 
              }}
              transition={{ 
                duration: 0.3,
                delay: index * 0.1,
                type: 'spring',
                stiffness: 300,
                damping: 20
              }}
            />
          ))}
        </SegmentIndicator>
      )}
    </ProgressContainer>
  );
};

export default TaskProgressBar;
