@echo off
echo 正在构建前端项目用于部署...
echo.

REM 设置生产环境
set NODE_ENV=production

REM 执行构建
npm run build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo.
    echo 📁 构建文件位置: dist/
    echo 🌐 API地址配置: http://*************:18080/api
    echo.
    echo 📦 创建部署压缩包...
    
    REM 创建部署压缩包
    if exist "crs-frontend-deploy.zip" del "crs-frontend-deploy.zip"
    powershell "Compress-Archive -Path 'dist\*' -DestinationPath 'crs-frontend-deploy.zip'"
    
    if exist "crs-frontend-deploy.zip" (
        echo ✅ 部署压缩包创建成功: crs-frontend-deploy.zip
        echo.
        echo 🚀 部署说明:
        echo 1. 将 crs-frontend-deploy.zip 上传到服务器
        echo 2. 解压到 nginx 网站根目录
        echo 3. 确保后端服务运行在 *************:18080
        echo 4. 配置 nginx 代理（可选）
    ) else (
        echo ❌ 压缩包创建失败
    )
) else (
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause