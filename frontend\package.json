{"name": "child-behavior-incentive-system", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:local": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "keywords": ["children", "reward", "tasks", "behavior"], "author": "", "license": "ISC", "dependencies": {"@react-three/drei": "^9.99.4", "@react-three/fiber": "^8.15.19", "axios": "^1.9.0", "date-fns": "^4.1.0", "framer-motion": "^11.0.8", "quill": "^2.0.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-router-dom": "^6.22.2", "recharts": "^2.12.3", "styled-components": "^6.1.8", "three": "^0.161.0"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.1.4"}}