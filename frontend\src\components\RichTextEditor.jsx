import React, { useMemo } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import styled from 'styled-components';

const EditorContainer = styled.div`
  .ql-editor {
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.5;
  }

  .ql-toolbar {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-radius: 4px 4px 0 0;
  }

  .ql-container {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-radius: 0 0 4px 4px;
  }

  .ql-editor img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }
`;

const RichTextEditor = ({ 
  value, 
  onChange, 
  placeholder = "请输入任务描述...",
  readOnly = false 
}) => {
  // 配置工具栏
  const modules = useMemo(() => ({
    toolbar: readOnly ? false : [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['clean']
    ],
    clipboard: {
      // 允许粘贴图片
      matchVisual: false,
    }
  }), [readOnly]);

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'list', 'bullet',
    'align',
    'link', 'image'
  ];

  // 处理图片上传/粘贴
  const imageHandler = () => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = () => {
      const file = input.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const quill = window.quillEditor;
          if (quill) {
            const range = quill.getSelection();
            quill.insertEmbed(range.index, 'image', e.target.result);
          }
        };
        reader.readAsDataURL(file);
      }
    };
  };

  // 自定义模块配置，包含图片处理
  const customModules = useMemo(() => ({
    ...modules,
    toolbar: {
      ...modules.toolbar,
      handlers: {
        image: imageHandler
      }
    }
  }), [modules]);

  return (
    <EditorContainer>
      <ReactQuill
        ref={(el) => {
          if (el) {
            window.quillEditor = el.getEditor();
          }
        }}
        theme="snow"
        value={value || ''}
        onChange={onChange}
        modules={customModules}
        formats={formats}
        placeholder={placeholder}
        readOnly={readOnly}
      />
    </EditorContainer>
  );
};

export default RichTextEditor;
