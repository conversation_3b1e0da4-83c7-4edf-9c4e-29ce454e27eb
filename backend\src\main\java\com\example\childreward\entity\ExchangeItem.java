package com.example.childreward.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 兑换商品实体类
 */
@Entity
@Table(name = "exchange_items")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "required_points", nullable = false)
    private Integer requiredPoints;
    
    @Column(name = "stock")
    private Integer stock; // -1表示无限库存
    
    @Column(name = "category", length = 50)
    private String category;
    
    @Column(name = "image_url", length = 500)
    private String imageUrl;
    
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;
    
    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;
    
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedTime = LocalDateTime.now();
    }
    
    /**
     * 检查是否有库存
     */
    public boolean hasStock() {
        return stock == null || stock == -1 || stock > 0;
    }
    
    /**
     * 减少库存
     */
    public void decreaseStock() {
        if (stock != null && stock > 0) {
            stock--;
        }
    }
}
