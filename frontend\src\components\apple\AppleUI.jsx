/**
 * 苹果标准UI组件库
 * 完全基于 iOS Human Interface Guidelines
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import { AppleDesignSystem, mediaQueries } from '../../design/AppleDesignSystem';

// ==================== 基础按钮组件 ====================
export const AppleButton = styled(motion.button)`
  /* 基础样式 */
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${AppleDesignSystem.spacing.sm};
  text-decoration: none;
  user-select: none;
  outline: none;
  
  /* 尺寸变体 */
  ${props => {
    const size = props.size || 'medium';
    const styles = {
      small: css`
        height: ${AppleDesignSystem.components.button.height.small};
        padding: ${AppleDesignSystem.components.button.padding.small};
        font-size: ${AppleDesignSystem.typography.textStyles.footnote.fontSize};
        border-radius: ${AppleDesignSystem.borderRadius.ios.small};
      `,
      medium: css`
        height: ${AppleDesignSystem.components.button.height.medium};
        padding: ${AppleDesignSystem.components.button.padding.medium};
        font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
        border-radius: ${AppleDesignSystem.borderRadius.ios.button};
      `,
      large: css`
        height: ${AppleDesignSystem.components.button.height.large};
        padding: ${AppleDesignSystem.components.button.padding.large};
        font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
        border-radius: ${AppleDesignSystem.borderRadius.ios.large};
      `
    };
    return styles[size];
  }}
  
  /* 样式变体 */
  ${props => {
    const variant = props.variant || 'filled';
    const color = props.color || 'blue';
    const systemColor = AppleDesignSystem.colors.system[color];
    
    const styles = {
      filled: css`
        background: ${systemColor};
        color: white;
        box-shadow: ${AppleDesignSystem.shadows.button};
        
        &:hover:not(:disabled) {
          background: ${systemColor}E6;
          transform: translateY(-1px);
          box-shadow: ${AppleDesignSystem.shadows.md};
        }
        
        &:active:not(:disabled) {
          background: ${systemColor}CC;
          transform: translateY(0);
          box-shadow: ${AppleDesignSystem.shadows.sm};
        }
      `,
      
      tinted: css`
        background: ${systemColor}1A;
        color: ${systemColor};
        
        &:hover:not(:disabled) {
          background: ${systemColor}26;
        }
        
        &:active:not(:disabled) {
          background: ${systemColor}33;
        }
      `,
      
      plain: css`
        background: transparent;
        color: ${systemColor};
        
        &:hover:not(:disabled) {
          background: ${AppleDesignSystem.colors.semantic.systemFill};
        }
        
        &:active:not(:disabled) {
          background: ${AppleDesignSystem.colors.semantic.secondarySystemFill};
        }
      `
    };
    return styles[variant];
  }}
  
  /* 禁用状态 */
  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none !important;
  }
  
  /* 动画 */
  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};
  
  /* 触摸设备优化 */
  ${mediaQueries.touch} {
    &:hover {
      transform: none;
    }
    
    &:active:not(:disabled) {
      transform: scale(0.96);
    }
  }
`;

// ==================== 卡片组件 ====================
export const AppleCard = styled(motion.div)`
  background: ${AppleDesignSystem.colors.semantic.systemBackground};
  border-radius: ${AppleDesignSystem.borderRadius.ios.card};
  box-shadow: ${AppleDesignSystem.shadows.card};
  overflow: hidden;
  
  ${props => props.padding && css`
    padding: ${typeof props.padding === 'string' ? props.padding : AppleDesignSystem.spacing.md};
  `}
  
  ${props => props.interactive && css`
    cursor: pointer;
    transition: all ${AppleDesignSystem.animation.duration.normal} ${AppleDesignSystem.animation.easing.standard};
    
    &:hover {
      box-shadow: ${AppleDesignSystem.shadows.lg};
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(-1px);
      box-shadow: ${AppleDesignSystem.shadows.md};
    }
    
    ${mediaQueries.touch} {
      &:hover {
        transform: none;
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  `}
`;

// ==================== 输入框组件 ====================
export const AppleInput = styled.input`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  width: 100%;
  border: 1px solid ${AppleDesignSystem.colors.semantic.separator};
  border-radius: ${AppleDesignSystem.borderRadius.ios.input};
  background: ${AppleDesignSystem.colors.semantic.systemBackground};
  color: ${AppleDesignSystem.colors.semantic.label};
  outline: none;
  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};
  
  ${props => {
    const size = props.size || 'medium';
    const heights = {
      small: AppleDesignSystem.components.input.height.small,
      medium: AppleDesignSystem.components.input.height.medium,
      large: AppleDesignSystem.components.input.height.large
    };
    return css`
      height: ${heights[size]};
      padding: 0 ${AppleDesignSystem.spacing.md};
    `;
  }}
  
  &:focus {
    border-color: ${AppleDesignSystem.colors.system.blue};
    box-shadow: 0 0 0 3px ${AppleDesignSystem.colors.system.blue}1A;
  }
  
  &::placeholder {
    color: ${AppleDesignSystem.colors.semantic.tertiaryLabel};
  }
  
  &:disabled {
    background: ${AppleDesignSystem.colors.semantic.secondarySystemBackground};
    color: ${AppleDesignSystem.colors.semantic.tertiaryLabel};
    cursor: not-allowed;
  }
`;

// ==================== 文本组件 ====================
export const AppleText = styled.div`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  color: ${AppleDesignSystem.colors.semantic.label};
  margin: 0;
  
  ${props => {
    const style = props.style || 'body';
    const textStyle = AppleDesignSystem.typography.textStyles[style];
    return css`
      font-size: ${textStyle.fontSize};
      line-height: ${textStyle.lineHeight};
      font-weight: ${textStyle.fontWeight};
      letter-spacing: ${textStyle.letterSpacing};
    `;
  }}
  
  ${props => props.color && css`
    color: ${AppleDesignSystem.colors.semantic[props.color] || AppleDesignSystem.colors.system[props.color] || props.color};
  `}
  
  ${props => props.weight && css`
    font-weight: ${AppleDesignSystem.typography.fontWeight[props.weight] || props.weight};
  `}
  
  ${props => props.align && css`
    text-align: ${props.align};
  `}
`;

// ==================== 容器组件 ====================
export const AppleContainer = styled.div`
  width: 100%;
  margin: 0 auto;
  padding: 0 ${AppleDesignSystem.spacing.md};
  
  ${props => {
    const maxWidth = props.maxWidth || 'none';
    const widths = {
      small: '600px',
      medium: '800px', 
      large: '1200px',
      xlarge: '1400px',
      none: 'none'
    };
    return css`
      max-width: ${widths[maxWidth] || maxWidth};
    `;
  }}
  
  ${mediaQueries.ipad} {
    padding: 0 ${AppleDesignSystem.spacing.lg};
  }
  
  ${mediaQueries.mac} {
    padding: 0 ${AppleDesignSystem.spacing.xl};
  }
`;

// ==================== 网格组件 ====================
export const AppleGrid = styled.div`
  display: grid;
  gap: ${props => props.gap || AppleDesignSystem.spacing.md};
  
  ${props => {
    if (props.columns) {
      return css`
        grid-template-columns: repeat(${props.columns}, 1fr);
      `;
    }
    
    // 响应式网格
    return css`
      grid-template-columns: 1fr;
      
      ${mediaQueries.ipad} {
        grid-template-columns: repeat(2, 1fr);
      }
      
      ${mediaQueries.mac} {
        grid-template-columns: repeat(3, 1fr);
      }
    `;
  }}
  
  ${mediaQueries.iphone} {
    gap: ${AppleDesignSystem.spacing.sm};
  }
`;

// ==================== 堆栈组件 ====================
export const AppleStack = styled.div`
  display: flex;
  
  ${props => {
    const direction = props.direction || 'column';
    const spacing = props.spacing || AppleDesignSystem.spacing.md;
    const align = props.align || 'stretch';
    const justify = props.justify || 'flex-start';
    
    return css`
      flex-direction: ${direction};
      gap: ${spacing};
      align-items: ${align};
      justify-content: ${justify};
    `;
  }}
`;

// ==================== 分隔线组件 ====================
export const AppleDivider = styled.hr`
  border: none;
  height: 1px;
  background: ${AppleDesignSystem.colors.semantic.separator};
  margin: ${props => props.margin || `${AppleDesignSystem.spacing.lg} 0`};
  width: 100%;
`;

// ==================== 徽章组件 ====================
export const AppleBadge = styled.span`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.caption1.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  padding: ${AppleDesignSystem.spacing.xs} ${AppleDesignSystem.spacing.sm};
  border-radius: ${AppleDesignSystem.borderRadius.full};
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  
  ${props => {
    const color = props.color || 'blue';
    const systemColor = AppleDesignSystem.colors.system[color];
    return css`
      background: ${systemColor};
      color: white;
    `;
  }}
`;

// ==================== 加载指示器 ====================
export const AppleSpinner = styled(motion.div)`
  width: ${props => props.size || '24px'};
  height: ${props => props.size || '24px'};
  border: 2px solid ${AppleDesignSystem.colors.semantic.quaternarySystemFill};
  border-top: 2px solid ${props => AppleDesignSystem.colors.system[props.color] || AppleDesignSystem.colors.system.blue};
  border-radius: 50%;
  display: inline-block;
`;

// 动画预设
export const spinAnimation = {
  animate: { rotate: 360 },
  transition: { duration: 1, repeat: Infinity, ease: 'linear' }
};

// ==================== 导航栏组件 ====================
export const AppleNavigationBar = styled.nav`
  background: ${AppleDesignSystem.colors.semantic.systemBackground};
  border-bottom: 1px solid ${AppleDesignSystem.colors.semantic.separator};
  height: ${AppleDesignSystem.components.navigation.height.regular};
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 ${AppleDesignSystem.spacing.md};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  ${mediaQueries.mac} {
    padding: 0 ${AppleDesignSystem.spacing.lg};
  }
`;

export const AppleNavigationTitle = styled(AppleText)`
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
`;

// ==================== 标签栏组件 ====================
export const AppleTabBar = styled.nav`
  background: ${AppleDesignSystem.colors.semantic.systemBackground};
  border-top: 1px solid ${AppleDesignSystem.colors.semantic.separator};
  height: ${AppleDesignSystem.components.tabBar.height};
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: ${AppleDesignSystem.spacing.sm} 0;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  ${mediaQueries.mac} {
    display: none; /* 桌面端隐藏标签栏 */
  }
`;

export const AppleTabItem = styled(motion.button)`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.xs};
  padding: ${AppleDesignSystem.spacing.xs};
  border-radius: ${AppleDesignSystem.borderRadius.ios.small};
  min-width: 44px;

  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.caption2.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.medium};

  color: ${props => props.active
    ? AppleDesignSystem.colors.system.blue
    : AppleDesignSystem.colors.semantic.secondaryLabel};

  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};

  &:active {
    transform: scale(0.95);
  }

  .icon {
    font-size: ${AppleDesignSystem.components.tabBar.iconSize};
  }
`;

export default {
  AppleButton,
  AppleCard,
  AppleInput,
  AppleText,
  AppleContainer,
  AppleGrid,
  AppleStack,
  AppleDivider,
  AppleBadge,
  AppleSpinner,
  AppleNavigationBar,
  AppleNavigationTitle,
  AppleTabBar,
  AppleTabItem,
  spinAnimation
};
