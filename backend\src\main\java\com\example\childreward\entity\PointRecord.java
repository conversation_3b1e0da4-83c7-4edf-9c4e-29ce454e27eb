package com.example.childreward.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "point_record")
public class PointRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "point_change", nullable = false)
    private Integer pointChange;

    @Enumerated(EnumType.STRING)
    @Column(name = "change_type", nullable = false)
    private ChangeType changeType;

    @Column(name = "record_time", nullable = false)
    private LocalDateTime recordTime;

    @Column(length = 255)
    private String description;
    
    @Column(name = "related_task_id")
    private Long relatedTaskId;
    
    @Column(name = "related_reward_id")
    private Long relatedRewardId;

    @PrePersist
    protected void onCreate() {
        this.recordTime = LocalDateTime.now();
    }

    public enum ChangeType {
        TASK_COMPLETION, // 完成任务奖励
        TASK_PENALTY,    // 未完成任务惩罚
        REWARD_EXCHANGE, // 兑换奖励
        MANUAL_ADJUST    // 手动调整
    }
} 