import SwiftUI

// MARK: - 兑换页面视图
struct ExchangeView: View {
    @StateObject private var rewardViewModel = RewardViewModel()

    @State private var selectedTab: ExchangeTab = .available
    @State private var selectedCategory: String? = nil
    @State private var showingExchangeDetail = false
    @State private var showingExchangeAlert = false
    @State private var selectedExchange: ExchangeRecord? = nil
    @State private var selectedItem: ExchangeItem? = nil

    var body: some View {
        VStack(spacing: 0) {
            // 积分显示
            pointsHeader

            // 标签切换器
            tabSelector

            // 内容区域
            TabView(selection: $selectedTab) {
                // 可兑换商品
                availableItemsView
                    .tag(ExchangeTab.available)

                // 兑换历史
                exchangeHistoryView
                    .tag(ExchangeTab.history)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        }
        .background(Color(.systemGroupedBackground))
        .alert("兑换确认", isPresented: $showingExchangeAlert) {
            exchangeAlert
        }
        .alert("操作结果", isPresented: .constant(rewardViewModel.successMessage != nil)) {
            Button("确定") {
                rewardViewModel.clearMessages()
            }
        } message: {
            if let message = rewardViewModel.successMessage {
                Text(message)
            }
        }
        .alert("错误", isPresented: .constant(rewardViewModel.errorMessage != nil)) {
            Button("确定") {
                rewardViewModel.clearMessages()
            }
        } message: {
            if let message = rewardViewModel.errorMessage {
                Text(message)
            }
        }
        .sheet(isPresented: $showingExchangeDetail) {
            if let exchange = selectedExchange {
                ExchangeDetailView(exchange: exchange)
            }
        }
    }

    // MARK: - 子视图

    private var pointsHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("我的积分")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("\(rewardViewModel.totalPoints)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }

            Spacer()

            Button("刷新") {
                rewardViewModel.loadData()
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 16)
        .background(.ultraThinMaterial)
    }

    private var tabSelector: some View {
        HStack(spacing: 0) {
            TabButton(title: "🛒 可兑换", isSelected: selectedTab == .available) {
                selectedTab = .available
            }

            TabButton(title: "📋 历史", isSelected: selectedTab == .history) {
                selectedTab = .history
            }
        }
        .padding(.horizontal, 32)
        .padding(.top, 16)
    }

    private var availableItemsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 分类筛选器
                if !rewardViewModel.availableCategories.isEmpty {
                    categoryFilter
                }

                if rewardViewModel.isLoading {
                    ProgressView("加载中...")
                        .frame(maxWidth: .infinity, minHeight: 200)
                } else if rewardViewModel.exchangeItems.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "cart.fill")
                            .font(.system(size: 48))
                            .foregroundColor(.gray)
                        Text("暂无可兑换商品")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                } else {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(filteredExchangeItems, id: \.id) { item in
                            ExchangeItemCard(
                                item: item,
                                userPoints: rewardViewModel.totalPoints,
                                onExchange: {
                                    selectedItem = item
                                    showingExchangeAlert = true
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 32)
                }

                Spacer(minLength: 32)
            }
            .padding(.top, 20)
        }
    }

    private var categoryFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                CategoryButton(
                    title: "全部",
                    isSelected: selectedCategory == nil
                ) {
                    selectedCategory = nil
                }

                ForEach(rewardViewModel.availableCategories, id: \.self) { category in
                    CategoryButton(
                        title: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, 32)
        }
    }

    // 筛选后的兑换商品
    private var filteredExchangeItems: [ExchangeItem] {
        return rewardViewModel.getExchangeItems(for: selectedCategory)
    }

    private var exchangeHistoryView: some View {
        ScrollView {
            VStack(spacing: 16) {
                if rewardViewModel.isLoading {
                    ProgressView("加载中...")
                        .frame(maxWidth: .infinity, minHeight: 200)
                } else if rewardViewModel.exchangeHistory.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "clock.fill")
                            .font(.system(size: 48))
                            .foregroundColor(.gray)
                        Text("暂无兑换记录")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                } else {
                    ForEach(rewardViewModel.exchangeHistory, id: \.id) { record in
                        ExchangeHistoryCard(
                            record: record,
                            onTap: {
                                selectedExchange = record
                                showingExchangeDetail = true
                            }
                        )
                    }
                    .padding(.horizontal, 32)
                }

                Spacer(minLength: 32)
            }
            .padding(.top, 20)
        }
    }

    // 兑换确认弹窗
    private var exchangeAlert: some View {
        Group {
            if let item = selectedItem {
                Button("确认兑换") {
                    rewardViewModel.exchangeItem(item)
                    selectedItem = nil
                }

                Button("取消", role: .cancel) {
                    selectedItem = nil
                }
            }
        }
    }
}

// MARK: - 兑换标签枚举
enum ExchangeTab: CaseIterable {
    case available
    case history

    var displayName: String {
        switch self {
        case .available: return "可兑换"
        case .history: return "历史"
        }
    }
}

// MARK: - 兑换历史卡片
struct ExchangeHistoryCard: View {
    let record: ExchangeRecord
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 状态指示器
                Circle()
                    .fill(record.status.color)
                    .frame(width: 12, height: 12)

                // 兑换信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(record.itemName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(record.formattedExchangeTime)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 积分和状态
                VStack(alignment: .trailing, spacing: 4) {
                    Text("-\(record.pointsUsed)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)

                    Text(record.status.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(record.status.color)
                }
            }
            .padding(16)
            .background(.ultraThinMaterial)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 兑换详情视图
struct ExchangeDetailView: View {
    let exchange: ExchangeRecord
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 兑换信息卡片
                exchangeInfoCard

                // 状态信息
                statusInfoCard

                if let notes = exchange.notes, !notes.isEmpty {
                    notesCard
                }

                Spacer()
            }
            .padding(32)
            .navigationTitle("兑换详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var exchangeInfoCard: some View {
        VStack(spacing: 16) {
            Text(exchange.itemName)
                .font(.title2)
                .fontWeight(.bold)

            HStack {
                VStack {
                    Text("消费积分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(exchange.pointsUsed)")
                        .font(.title3)
                        .fontWeight(.semibold)
                }

                Spacer()

                VStack {
                    Text("兑换日期")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(exchange.formattedExchangeTime)
                        .font(.title3)
                        .fontWeight(.semibold)
                }
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }

    private var statusInfoCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("状态")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text(exchange.status.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(exchange.status.color)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(exchange.status.color.opacity(0.1))
                    .cornerRadius(12)
            }

            if let expireTime = exchange.formattedExpireTime {
                HStack {
                    Text("过期时间")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text(expireTime)
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }

    private var notesCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("备注")
                .font(.headline)
                .fontWeight(.semibold)

            Text(exchange.notes ?? "")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
}
