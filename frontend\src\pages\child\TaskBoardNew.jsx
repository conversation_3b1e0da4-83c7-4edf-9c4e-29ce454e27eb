import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import { childApi } from '../../api/apiService';
import {
  ChildContainer,
  ChildHeader,
  ChildTitle,
  ChildSubtitle,
  ChildGrid,
  ChildCard,
  ChildCardTitle,
  ChildCardContent,
  ChildButton,
  ChildTabBar,
  ChildTabItem,
  ChildBadge
} from '../../components/child/ChildAppleUI';
import { ApplePageTransition } from '../../components/apple/AppleAnimations';
import RichTextRenderer from '../../components/common/RichTextRenderer';

// 任务状态排序优先级
const getStatusPriority = (status) => {
  if (!status) return 999;

  switch(status.toUpperCase()) {
    case 'NOT_STARTED': return 1;  // 未开始
    case 'IN_PROGRESS': return 2;  // 进行中
    case 'PENDING': return 3;      // 待审核
    case 'REJECTED': return 4;     // 已作废
    case 'COMPLETED': return 5;    // 已完成
    case 'OVERDUE': return 6;      // 已过期
    default: return 999;
  }
};

// 任务排序函数
const sortTasks = (tasks) => {
  return tasks.sort((a, b) => {
    const priorityA = getStatusPriority(a.status);
    const priorityB = getStatusPriority(b.status);

    // 首先按状态优先级排序
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    // 状态相同时，按任务ID排序（保持稳定排序）
    return a.id - b.id;
  });
};

const TaskBoardNew = () => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const response = await childApi.getTodayTasks();
      const sortedTasks = sortTasks(response.data || []);
      setTasks(sortedTasks);
    } catch (error) {
      console.error('获取任务失败:', error);
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 显示任务详情
  const showTaskDetails = (task) => {
    navigate(`/child/task/${task.id}`);
  };

  // 执行任务操作
  const handleTaskAction = async (task, action) => {
    try {
      const status = task.status ? task.status.toString().toUpperCase() : '';

      if (['COMPLETED', 'APPROVED', 'COMPLETE'].includes(status)) {
        alert(`✅ 任务已完成\n\n${task.title || task.taskName}\n获得积分：+${task.basePoints || task.points || task.score || 0}\n\n太棒了！`);
        return;
      }

      if (action === 'start') {
        await childApi.startTask(task.id);
        alert('🚀 任务已开始！\n\n加油完成任务吧！');
        fetchTasks();
      } else if (action === 'complete') {
        await childApi.completeTask(task.id);
        alert('🎉 任务完成！\n\n恭喜你完成了任务！\n积分 +' + (task.basePoints || task.points || task.score || 0) + '\n\n继续加油！');
        fetchTasks();
      }

      closeTaskDetail();
    } catch (error) {
      console.error('任务操作失败:', error);
      alert('❌ 操作失败\n\n请检查网络连接后重试');
    }
  };

  const getTaskStatusColor = (status) => {
    if (!status) return AppleDesignSystem.colors.semantic.systemOrange;
    const upperStatus = status.toString().toUpperCase();
    if (['COMPLETED', 'APPROVED', 'COMPLETE'].includes(upperStatus)) {
      return AppleDesignSystem.colors.semantic.systemGreen;
    }
    if (['PENDING', 'WAITING', 'IN_PROGRESS'].includes(upperStatus)) {
      return AppleDesignSystem.colors.semantic.systemOrange;
    }
    return AppleDesignSystem.colors.semantic.systemGray;
  };

  const getTaskStatusText = (status) => {
    if (!status) return '未开始';
    const upperStatus = status.toString().toUpperCase();
    if (['COMPLETED', 'APPROVED', 'COMPLETE'].includes(upperStatus)) {
      return '已完成';
    }
    if (['PENDING', 'WAITING', 'IN_PROGRESS'].includes(upperStatus)) {
      return '进行中';
    }
    return '未开始';
  };

  const getTaskIcon = (status) => {
    if (!status) return '📝';
    const upperStatus = status.toString().toUpperCase();
    if (['COMPLETED', 'APPROVED', 'COMPLETE'].includes(upperStatus)) {
      return '✅';
    }
    if (['PENDING', 'WAITING', 'IN_PROGRESS'].includes(upperStatus)) {
      return '⏳';
    }
    return '📝';
  };

  const getTaskGradient = (status) => {
    if (!status) return '#667eea 0%, #764ba2 100%';
    const upperStatus = status.toString().toUpperCase();
    if (['COMPLETED', 'APPROVED', 'COMPLETE'].includes(upperStatus)) {
      return '#4facfe 0%, #00f2fe 100%'; // 蓝色渐变
    }
    if (['PENDING', 'WAITING', 'IN_PROGRESS'].includes(upperStatus)) {
      return '#fa709a 0%, #fee140 100%'; // 粉橙渐变
    }
    return '#667eea 0%, #764ba2 100%'; // 紫色渐变
  };

  if (loading) {
    return (
      <ChildContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          flexDirection: 'column',
          gap: AppleDesignSystem.spacing.md
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: `4px solid ${AppleDesignSystem.colors.semantic.systemFill}`,
            borderTop: `4px solid ${AppleDesignSystem.colors.child.primary}`,
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div style={{
            fontSize: AppleDesignSystem.typography.textStyles.headline.fontSize,
            color: AppleDesignSystem.colors.semantic.secondaryLabel
          }}>
            正在加载任务...
          </div>
        </div>
        
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </ChildContainer>
    );
  }

  return (
    <ApplePageTransition>
      <ChildContainer>
        <ChildHeader>
          <div>
            <ChildTitle>📝 我的任务</ChildTitle>
            <ChildSubtitle>完成任务赚取积分吧！</ChildSubtitle>
          </div>
          <div style={{
            fontSize: '48px',
            background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
            borderRadius: '50%',
            width: '64px',
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            📋
          </div>
        </ChildHeader>

        {tasks.length === 0 ? (
          <ChildCard fullWidth style={{ textAlign: 'center', padding: '40px' }}>
            <div style={{ fontSize: '120px', marginBottom: '20px' }}>
              🎉
            </div>
            <ChildCardTitle>太棒了！</ChildCardTitle>
            <ChildCardContent>
              今天没有任务，你可以自由玩耍啦！
            </ChildCardContent>
          </ChildCard>
        ) : (
          <div style={{ display: 'grid', gap: AppleDesignSystem.spacing.lg }}>
            {tasks.map((task, index) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ChildCard
                  interactive
                  onClick={() => showTaskDetails(task)}
                  style={{
                    cursor: 'pointer',
                    position: 'relative',
                    background: `linear-gradient(135deg, ${getTaskGradient(task.status)})`,
                    border: 'none',
                    color: 'white'
                  }}
                >
                  {/* 状态徽章 */}
                  <div style={{
                    position: 'absolute',
                    top: AppleDesignSystem.spacing.md,
                    right: AppleDesignSystem.spacing.md,
                    background: 'rgba(255,255,255,0.2)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '20px',
                    padding: '6px 12px',
                    fontSize: '12px',
                    fontWeight: '600'
                  }}>
                    {getTaskStatusText(task.status)}
                  </div>

                  {/* 积分徽章 */}
                  <div style={{
                    position: 'absolute',
                    top: AppleDesignSystem.spacing.md,
                    left: AppleDesignSystem.spacing.md,
                    background: 'rgba(255,255,255,0.2)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '20px',
                    padding: '6px 12px',
                    fontSize: '12px',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    💰 +{task.basePoints || task.points || task.score || 0}
                  </div>

                  <div style={{
                    padding: `${AppleDesignSystem.spacing.xl} ${AppleDesignSystem.spacing.md} ${AppleDesignSystem.spacing.md}`,
                    display: 'flex',
                    alignItems: 'center',
                    gap: AppleDesignSystem.spacing.md
                  }}>
                    <div style={{
                      fontSize: '48px',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
                    }}>
                      {getTaskIcon(task.status)}
                    </div>

                    <div style={{ flex: 1 }}>
                      <ChildCardTitle style={{
                        margin: 0,
                        color: 'white',
                        fontSize: '18px',
                        fontWeight: '600',
                        textShadow: '0 1px 2px rgba(0,0,0,0.2)'
                      }}>
                        {task.title || task.taskName || '未知任务'}
                      </ChildCardTitle>

                      {task.description && (
                        <div style={{
                          marginTop: AppleDesignSystem.spacing.sm,
                          fontSize: '14px',
                          color: 'rgba(255,255,255,0.8)',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textShadow: '0 1px 2px rgba(0,0,0,0.2)'
                        }}>
                          {task.description.length > 50
                            ? task.description.substring(0, 50) + '...'
                            : task.description}
                        </div>
                      )}

                      {task.dueTime && (
                        <div style={{
                          marginTop: AppleDesignSystem.spacing.sm,
                          fontSize: '12px',
                          color: 'rgba(255,255,255,0.7)',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}>
                          ⏰ {task.dueTime}
                        </div>
                      )}
                    </div>

                    <div style={{
                      fontSize: '24px',
                      opacity: 0.7
                    }}>
                      →
                    </div>
                  </div>
                </ChildCard>
              </motion.div>
            ))}
          </div>
        )}



        <ChildTabBar>
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🏠</div>
            <div style={{ fontSize: '12px' }}>首页</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={true}
            onClick={() => navigate('/child/tasks')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📝</div>
            <div style={{ fontSize: '12px' }}>任务</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child/spin')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>🎯</div>
            <div style={{ fontSize: '12px' }}>转盘</div>
          </ChildTabItem>
          
          <ChildTabItem
            active={false}
            onClick={() => navigate('/child/points')}
          >
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
            <div style={{ fontSize: '12px' }}>记录</div>
          </ChildTabItem>
        </ChildTabBar>
      </ChildContainer>
    </ApplePageTransition>
  );
};

export default TaskBoardNew;