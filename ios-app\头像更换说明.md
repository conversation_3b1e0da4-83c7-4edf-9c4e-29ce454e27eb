# 头像更换说明

## 方法一：使用脚本自动处理（推荐）

1. **保存头像图片**
   - 将您提供的小男孩头像图片保存到任意位置，比如桌面
   - 记住文件的完整路径

2. **运行脚本**
   ```bash
   cd /Users/<USER>/IdeaProjects/crs/ios-app
   ./add_avatar.sh /path/to/your/avatar/image.jpg
   ```
   - 脚本会自动裁剪为正方形并调整大小
   - 自动转换为PNG格式并保存到正确位置

## 方法二：手动添加

1. **保存头像图片**
   - 将您提供的小男孩头像图片保存到桌面，命名为 `avatar.png`
   - 建议尺寸：至少 160x160 像素，保持正方形比例

2. **添加到Xcode项目**
   - 打开 Xcode 项目
   - 在左侧导航栏中找到 `Assets.xcassets`
   - 展开后找到 `avatar.imageset` 文件夹
   - 将保存的 `avatar.png` 图片拖拽到 `avatar.imageset` 文件夹中
   - 确保图片被正确添加到 `1x` 位置

3. **图片处理建议**
   - 图片会自动应用圆角裁剪效果
   - 会等比缩放到 80x80 像素显示
   - 有白色边框和渐变背景效果

## 已完成的代码修改

✅ 创建了 `AvatarView.swift` 组件，统一管理头像显示
✅ 修改了 `ContentView.swift` 中的头像显示代码，使用新的AvatarView组件
✅ 更新了 `ImagePicker.swift` 中的AvatarPicker，支持默认头像显示
✅ 添加了圆角裁剪和边框效果
✅ 添加了备用显示方案（如果图片不存在会显示emoji）
✅ 创建了 `Assets.xcassets/avatar.imageset/Contents.json` 配置文件
✅ 创建了 `add_avatar.sh` 脚本，可自动处理和添加头像图片

## 效果预览

头像将显示为：
- 84x84 像素的渐变背景圆形
- 80x80 像素的圆形头像图片
- 2像素的白色边框
- 如果图片加载失败，会显示默认的👶emoji

## 快速测试（不添加图片）

如果您想先测试代码修改是否正确：
1. 直接重新编译项目
2. 应用会显示默认的👶emoji头像
3. 头像仍然有圆形背景和渐变效果

## 完整测试（添加真实头像后）

完成图片添加后：
1. 重新编译项目
2. 在模拟器中查看左侧导航栏的头像是否正确显示
3. 头像应该显示为圆形，带有白色边框效果
4. 头像会自动等比缩放并裁剪为圆形

## 故障排除

如果头像不显示：
1. 确保图片文件名为 `avatar.png`
2. 确保图片在 `Assets.xcassets/avatar.imageset/` 目录下
3. 在Xcode中清理项目缓存 (Product → Clean Build Folder)
4. 重新编译项目
