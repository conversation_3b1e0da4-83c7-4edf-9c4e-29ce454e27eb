import SwiftUI

// 任务进度条组件
struct TaskProgressBar: View {
    let progress: Double // 0.0 到 1.0
    let height: CGFloat
    let backgroundColor: Color
    let foregroundColor: Color
    let showPercentage: Bool
    let animated: Bool
    
    init(
        progress: Double,
        height: CGFloat = 8,
        backgroundColor: Color = Color(.systemGray5),
        foregroundColor: Color = .blue,
        showPercentage: Bool = false,
        animated: Bool = true
    ) {
        self.progress = max(0, min(1, progress)) // 确保在0-1范围内
        self.height = height
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.showPercentage = showPercentage
        self.animated = animated
    }
    
    var body: some View {
        HStack(spacing: 8) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    Rectangle()
                        .fill(backgroundColor)
                        .frame(height: height)
                        .cornerRadius(height / 2)
                    
                    // 前景进度
                    Rectangle()
                        .fill(foregroundColor)
                        .frame(width: geometry.size.width * progress, height: height)
                        .cornerRadius(height / 2)
                        .animation(animated ? .easeInOut(duration: 0.5) : .none, value: progress)
                }
            }
            .frame(height: height)
            
            // 百分比文字（可选）
            if showPercentage {
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(width: 35, alignment: .trailing)
            }
        }
    }
}

// 紧凑型进度条（用于任务卡片）
struct CompactProgressBar: View {
    let progress: Double
    let color: Color
    
    init(progress: Double, color: Color = .blue) {
        self.progress = max(0, min(1, progress))
        self.color = color
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景
                Rectangle()
                    .fill(Color(.systemGray5))
                    .frame(height: 4)
                    .cornerRadius(2)
                
                // 前景进度
                Rectangle()
                    .fill(color)
                    .frame(width: geometry.size.width * progress, height: 4)
                    .cornerRadius(2)
                    .animation(.easeInOut(duration: 0.3), value: progress)
            }
        }
        .frame(height: 4)
    }
}

// 预览
struct TaskProgressBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 基础进度条
            TaskProgressBar(progress: 0.3)
                .frame(height: 8)
            
            // 带百分比的进度条
            TaskProgressBar(progress: 0.65, showPercentage: true)
                .frame(height: 10)
            
            // 自定义颜色的进度条
            TaskProgressBar(
                progress: 0.8,
                foregroundColor: .green,
                showPercentage: true
            )
            .frame(height: 12)
            
            // 紧凑型进度条
            CompactProgressBar(progress: 0.45, color: .orange)
                .frame(height: 4)
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
