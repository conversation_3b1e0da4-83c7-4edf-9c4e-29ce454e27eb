import SwiftUI

// MARK: - 任务详情卡片组件
struct TaskDetailCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片标题
            cardHeader
            
            // 卡片内容
            content()
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
        .padding(.horizontal, 32)
    }
    
    private var cardHeader: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)

            Text(title)
                .font(.headline)
                .fontWeight(.semibold)

            Spacer()
        }
    }
}

// MARK: - 任务详情视图组件
struct TaskDetailView: View {
    let task: TaskModel
    let onBack: () -> Void
    let onAction: (TaskAction) -> Void

    @StateObject private var screenDimmingManager = ScreenDimmingManager.shared
    @State private var showingActionSheet = false
    @State private var selectedAction: TaskAction?
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 返回按钮和标题
                headerSection
                
                // 任务基本信息
                basicInfoCard
                
                // 任务详情
                detailsCard
                
                // 时间信息
                timeInfoCard
                
                // 进度和状态
                progressCard
                
                // 操作按钮
                actionButtonsCard
                
                Spacer(minLength: 32)
            }
        }
        .background(Color(.systemGroupedBackground))
        .actionSheet(isPresented: $showingActionSheet) {
            taskActionSheet
        }
    }
    
    // MARK: - 子视图
    
    private var headerSection: some View {
        HStack {
            Button(action: onBack) {
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.title3)
                    Text("返回")
                        .font(.body)
                }
                .foregroundColor(.blue)
            }
            
            Spacer()
            
            Text("任务详情")
                .font(.title2)
                .fontWeight(.bold)
            
            Spacer()
            
            // 占位符保持居中
            Color.clear
                .frame(width: 60)
        }
        .padding(.horizontal, 32)
        .padding(.top, 20)
    }
    
    private var basicInfoCard: some View {
        TaskDetailCard(
            title: "基本信息",
            icon: "info.circle",
            color: .blue
        ) {
            VStack(alignment: .leading, spacing: 12) {
                // 任务标题
                VStack(alignment: .leading, spacing: 4) {
                    Text("任务名称")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(task.title)
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Divider()
                
                // 积分和时间
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("奖励积分")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        HStack(spacing: 4) {
                            Image(systemName: "star.fill")
                                .foregroundColor(.orange)
                                .font(.caption)
                            Text("\(task.basePoints)")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("预计用时")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(task.formattedExpectedTime)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                }
                
                // 状态标签
                statusBadge
            }
        }
    }
    
    private var detailsCard: some View {
        TaskDetailCard(
            title: "任务详情",
            icon: "doc.text",
            color: .green
        ) {
            VStack(alignment: .leading, spacing: 12) {
                if let taskType = task.taskType {
                    detailRow(label: "任务类型", value: taskType)
                }
                
                if let sourceTemplateId = task.sourceTemplateId {
                    detailRow(label: "模板ID", value: "\(sourceTemplateId)")
                }
                
                if let createdTime = task.createdTime {
                    detailRow(label: "创建时间", value: formatDateTime(createdTime))
                }
                
                if let scheduledDate = task.scheduledDate {
                    detailRow(label: "计划日期", value: formatDate(scheduledDate))
                }
            }
        }
    }
    
    private var timeInfoCard: some View {
        TaskDetailCard(
            title: "时间信息",
            icon: "clock",
            color: .orange
        ) {
            VStack(alignment: .leading, spacing: 12) {
                if let dueDate = task.dueDate {
                    detailRow(label: "截止日期", value: formatDate(dueDate))
                }
                
                if let dueTime = task.dueTime {
                    detailRow(label: "截止时间", value: dueTime)
                }
                
                if let startTime = task.startTime {
                    detailRow(label: "开始时间", value: formatDateTime(startTime))
                }
                
                if let endTime = task.endTime {
                    detailRow(label: "结束时间", value: formatDateTime(endTime))
                }
                
                if let actualMinutes = task.actualMinutes {
                    detailRow(label: "实际用时", value: formatMinutes(actualMinutes))
                }
            }
        }
    }
    
    private var progressCard: some View {
        TaskDetailCard(
            title: "进度状态",
            icon: "chart.line.uptrend.xyaxis",
            color: .purple
        ) {
            VStack(alignment: .leading, spacing: 16) {
                // 状态进度条
                statusProgressBar
                
                // 时间进度（如果任务进行中）
                if task.status == .inProgress, let startTime = task.startTime {
                    timeProgressSection(startTime: startTime)
                }
                
                // 积分信息
                pointsSection
            }
        }
    }
    
    private var actionButtonsCard: some View {
        TaskDetailCard(
            title: "操作",
            icon: "gearshape",
            color: .blue
        ) {
            VStack(spacing: 12) {
                ForEach(availableActions, id: \.self) { action in
                    actionButton(for: action)
                }
            }
        }
    }
    
    // MARK: - 辅助视图
    
    private var statusBadge: some View {
        HStack {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            
            Text(task.status.displayName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(statusColor)
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(statusColor.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var statusProgressBar: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("任务进度")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(progressText)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            ProgressView(value: progressValue)
                .progressViewStyle(LinearProgressViewStyle(tint: statusColor))
        }
    }
    
    private func timeProgressSection(startTime: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("时间进度")
                .font(.caption)
                .foregroundColor(.secondary)
            
            // 这里可以添加基于开始时间的进度计算
            Text("已进行: \(calculateElapsedTime(from: startTime))")
                .font(.subheadline)
        }
    }
    
    private var pointsSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("基础积分")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text("\(task.basePoints)")
                    .font(.subheadline)
                    .fontWeight(.semibold)
            }
            
            Spacer()
            
            if let actualPoints = task.actualPoints {
                VStack(alignment: .trailing, spacing: 4) {
                    Text("实际积分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(actualPoints)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(actualPoints >= task.basePoints ? .green : .orange)
                }
            }
        }
    }
    
    private func detailRow(label: String, value: String) -> some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.subheadline)
            
            Spacer()
        }
    }
    
    private func actionButton(for action: TaskAction) -> some View {
        Button(action: {
            selectedAction = action

            // 如果是开始任务，启动息屏倒计时
            if action == .start {
                screenDimmingManager.startDimmingForTask()
            }

            onAction(action)
        }) {
            HStack {
                Image(systemName: action.iconName)
                    .font(.body)
                    .foregroundColor(action.color)
                    .frame(width: 24)
                
                Text(action.title)
                    .font(.body)
                    .fontWeight(.medium)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(action.color.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 计算属性和方法
    
    private var statusColor: Color {
        switch task.status {
        case .notStarted, .pending: return .orange
        case .inProgress: return .blue
        case .completed, .approved: return .green
        case .overdue: return .red
        case .cancelled: return .gray
        case .rejected: return .red
        case .penaltyApproval: return .purple
        }
    }
    
    private var progressValue: Double {
        switch task.status {
        case .notStarted: return 0.0
        case .pending: return 0.1
        case .inProgress: return 0.5
        case .completed, .approved: return 1.0
        case .overdue: return 0.3
        case .cancelled: return 0.0
        case .rejected: return 0.0
        case .penaltyApproval: return 1.0
        }
    }
    
    private var progressText: String {
        switch task.status {
        case .notStarted: return "未开始"
        case .pending: return "等待开始"
        case .inProgress: return "进行中"
        case .completed, .approved: return "已完成"
        case .overdue: return "已过期"
        case .cancelled: return "已取消"
        case .rejected: return "已作废"
        case .penaltyApproval: return "惩罚审批"
        }
    }
    
    private var availableActions: [TaskAction] {
        switch task.status {
        case .notStarted, .pending:
            return [.start, .cancel]
        case .inProgress:
            return [.complete, .pause, .cancel]
        case .completed:
            return [.restart]
        case .approved:
            return []
        case .overdue:
            return [.restart, .cancel]
        case .cancelled:
            return [.restart]
        case .rejected:
            return [.restart]
        case .penaltyApproval:
            return [] // 惩罚审批状态下不允许任何操作
        }
    }
    
    private var taskActionSheet: ActionSheet {
        ActionSheet(
            title: Text("选择操作"),
            message: Text("请选择要执行的操作"),
            buttons: availableActions.map { action in
                .default(Text(action.title)) {
                    // 如果是开始任务，启动息屏倒计时
                    if action == .start {
                        screenDimmingManager.startDimmingForTask()
                    }

                    onAction(action)
                }
            } + [.cancel()]
        )
    }
    
    // MARK: - 格式化方法
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return dateString
    }
    
    private func formatDateTime(_ dateTimeString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        if let date = formatter.date(from: dateTimeString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日 HH:mm"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return dateTimeString
    }
    
    private func formatMinutes(_ minutes: Int) -> String {
        if minutes < 60 {
            return "\(minutes)分钟"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(remainingMinutes)分钟"
            }
        }
    }
    
    private func calculateElapsedTime(from startTime: String) -> String {
        // 这里应该实现实际的时间计算逻辑
        return "30分钟" // 示例值
    }
}

// MARK: - 任务操作枚举
enum TaskAction: CaseIterable {
    case start
    case complete
    case pause
    case cancel
    case restart
    
    var title: String {
        switch self {
        case .start: return "开始任务"
        case .complete: return "完成任务"
        case .pause: return "暂停任务"
        case .cancel: return "取消任务"
        case .restart: return "重新开始"
        }
    }
    
    var iconName: String {
        switch self {
        case .start: return "play.fill"
        case .complete: return "checkmark.circle.fill"
        case .pause: return "pause.fill"
        case .cancel: return "xmark.circle.fill"
        case .restart: return "arrow.clockwise"
        }
    }
    
    var color: Color {
        switch self {
        case .start: return .blue
        case .complete: return .green
        case .pause: return .orange
        case .cancel: return .red
        case .restart: return .purple
        }
    }
}

// MARK: - 预览
struct TaskDetailCard_Previews: PreviewProvider {
    static var previews: some View {
        TaskDetailCard(
            title: "基本信息",
            icon: "info.circle",
            color: .blue
        ) {
            VStack(alignment: .leading, spacing: 8) {
                Text("这是卡片内容")
                Text("可以包含任意视图")
            }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}
