import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { childTheme } from '../../utils/themes';
import { childApi } from '../../api/apiService';
import { rewardApi } from '../../api/apiService';

// 模拟奖品池数据
const mockRewardPools = [
  {
    id: 1,
    name: '小奖品池',
    costPoints: 10,
    items: [
      { id: 1, name: '贴纸', probability: 0.5, stock: 20, image: '🏷️' },
      { id: 2, name: '橡皮擦', probability: 0.3, stock: 15, image: '✏️' },
      { id: 3, name: '铅笔', probability: 0.2, stock: 10, image: '📝' },
    ]
  },
  {
    id: 2,
    name: '中奖品池',
    costPoints: 30,
    items: [
      { id: 4, name: '画册', probability: 0.4, stock: 8, image: '📚' },
      { id: 5, name: '彩笔', probability: 0.3, stock: 10, image: '🖌️' },
      { id: 6, name: '小玩具', probability: 0.3, stock: 5, image: '🧸' },
    ]
  },
  {
    id: 3,
    name: '大奖品池',
    costPoints: 50,
    items: [
      { id: 7, name: '积木', probability: 0.5, stock: 3, image: '🧱' },
      { id: 8, name: '故事书', probability: 0.3, stock: 5, image: '📕' },
      { id: 9, name: '电子游戏时间', probability: 0.2, stock: 2, image: '��' },
    ]
  }
];

// 模拟惩罚池数据
const mockPenaltyPools = [
  {
    id: 101,
    name: '轻微惩罚池',
    costPoints: -10,
    items: [
      { id: 201, name: '额外作业', probability: 0.5, image: '📄' },
      { id: 202, name: '减少娱乐时间', probability: 0.3, image: '⏱️' },
      { id: 203, name: '做家务', probability: 0.2, image: '🧹' },
    ]
  },
  {
    id: 102,
    name: '严重惩罚池',
    costPoints: -30,
    items: [
      { id: 204, name: '收走手机', probability: 0.4, image: '📱' },
      { id: 205, name: '早睡一小时', probability: 0.3, image: '🛌' },
      { id: 206, name: '周末补课', probability: 0.3, image: '📝' },
    ]
  }
];

const RewardCenter = ({ currentPoints, onPointsChange, dailyPointsChange }) => {
  const [rewardPools, setRewardPools] = useState([]);
  const [penaltyPools, setPenaltyPools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('rewards'); // 'rewards' 或 'penalties'
  const navigate = useNavigate();

  useEffect(() => {
    // 从后端API获取奖品池和惩罚池
    const fetchPools = async () => {
      setLoading(true);
      try {
        // 获取可用奖品池
        const rewardResponse = await childApi.getAvailableRewardPools();
        
        // 格式化奖品池数据
        const formattedRewards = await Promise.all(
          rewardResponse.data.map(async pool => {
            try {
              // 获取奖品池详情，包含奖品项
              const poolDetailResponse = await rewardApi.getRewardPool(pool.id);
              const poolDetail = poolDetailResponse.data;
              
              return {
                id: pool.id,
                name: pool.name,
                costPoints: pool.costPoints,
                items: poolDetail && poolDetail.rewardItems ? poolDetail.rewardItems.map(item => ({
                  id: item.id,
                  name: item.name,
                  probability: item.probability,
                  stock: item.stock,
                  image: item.imageUrl || '🎁' // 默认为礼物表情符号
                })) : []
              };
            } catch (error) {
              console.error(`获取奖品池 ${pool.id} 详情失败:`, error);
              return {
                id: pool.id,
                name: pool.name,
                costPoints: pool.costPoints,
                items: []
              };
            }
          })
        );
        
        setRewardPools(formattedRewards);
        
        // 目前没有惩罚池的后端API，所以保留空数组
        // 如果未来支持惩罚池，可以添加相应的代码
        setPenaltyPools([]);
      } catch (error) {
        console.error('获取奖品池失败:', error);
        setRewardPools([]);
        setPenaltyPools([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPools();
  }, []);

  const handleSelectPool = (poolId, isPenalty = false) => {
    const pools = isPenalty ? penaltyPools : rewardPools;
    const selectedPool = pools.find(pool => pool.id === poolId);
    
    if (!selectedPool) return;
    
    // 检查积分是否足够（惩罚池不需要检查）
    if (!isPenalty && currentPoints < selectedPool.costPoints) {
      alert('积分不足，无法抽奖！');
      return;
    }
    
    // 导航到抽奖页面，传递惩罚池标志
    navigate(`/child/spin/${poolId}${isPenalty ? '?penalty=true' : ''}`);
  };

  if (loading) {
    return <LoadingContainer>加载中...</LoadingContainer>;
  }

  return (
    <RewardCenterContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <TabsContainer>
        <Tab 
          active={activeTab === 'rewards'}
          onClick={() => setActiveTab('rewards')}
        >
          <TabIcon>🎁</TabIcon>
          奖励抽奖
        </Tab>
        <Tab 
          active={activeTab === 'penalties'}
          onClick={() => setActiveTab('penalties')}
          isPenalty
        >
          <TabIcon>⚠️</TabIcon>
          惩罚抽奖
        </Tab>
      </TabsContainer>
      
      {activeTab === 'rewards' && (
        <PoolsContainer>
          <AnimatePresence>
            {rewardPools.map(pool => (
              <PoolCard
                key={pool.id}
                layoutId={`pool-${pool.id}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                disabled={currentPoints < pool.costPoints}
              >
                <PoolHeader>
                  <PoolTitle>{pool.name}</PoolTitle>
                  <PoolCost>
                    <StarIcon>⭐</StarIcon>
                    {pool.costPoints}
                  </PoolCost>
                </PoolHeader>
                
                <PoolItems>
                  {pool.items.map(item => (
                    <ItemBubble key={item.id}>
                      <ItemImage>{item.image}</ItemImage>
                      <ItemName>{item.name}</ItemName>
                    </ItemBubble>
                  ))}
                </PoolItems>
                
                <SpinButton
                  onClick={() => handleSelectPool(pool.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={currentPoints < pool.costPoints}
                >
                  {currentPoints >= pool.costPoints ? '开始抽奖' : '积分不足'}
                </SpinButton>
                
                {currentPoints < pool.costPoints && (
                  <LockOverlay>
                    <LockIcon>🔒</LockIcon>
                    <NeededPoints>还需 {pool.costPoints - currentPoints} 积分</NeededPoints>
                  </LockOverlay>
                )}
              </PoolCard>
            ))}
          </AnimatePresence>
        </PoolsContainer>
      )}
      
      {activeTab === 'penalties' && (
        <PoolsContainer>
          <AnimatePresence>
            {penaltyPools.map(pool => (
              <PenaltyPoolCard
                key={pool.id}
                layoutId={`pool-${pool.id}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ type: 'spring', stiffness: 300, damping: 25 }}
              >
                <PoolHeader>
                  <PoolTitle>{pool.name}</PoolTitle>
                  <PenaltyCost>
                    <WarningIcon>⚠️</WarningIcon>
                    {Math.abs(pool.costPoints)}
                  </PenaltyCost>
                </PoolHeader>
                
                <PoolItems>
                  {pool.items.map(item => (
                    <PenaltyItemBubble key={item.id}>
                      <ItemImage>{item.image}</ItemImage>
                      <ItemName>{item.name}</ItemName>
                    </PenaltyItemBubble>
                  ))}
                </PoolItems>
                
                <PenaltySpinButton
                  onClick={() => handleSelectPool(pool.id, true)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  接受惩罚
                </PenaltySpinButton>
              </PenaltyPoolCard>
            ))}
          </AnimatePresence>
        </PoolsContainer>
      )}
    </RewardCenterContainer>
  );
};

const RewardCenterContainer = styled(motion.div)`
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const CenterTitle = styled.h1`
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: ${childTheme.textColor};
  text-align: center;
  font-weight: 700;
`;

// 标签切换
const TabsContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  gap: 1rem;
`;

const Tab = styled.div`
  padding: 0.6rem 1.5rem;
  background: ${props => props.active 
    ? props.isPenalty 
      ? 'linear-gradient(135deg, #ff5252, #d32f2f)'
      : 'linear-gradient(135deg, #42a5f5, #1976d2)'
    : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border-radius: 25px;
  font-weight: 600;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const TabIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
`;

// 奖品池样式
const PoolsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  overflow-y: auto;
  padding: 0.5rem;
  flex: 1;
`;

const PoolCard = styled(motion.div)`
  background: white;
  border-radius: ${childTheme.borderRadius};
  padding: 0.75rem;
  box-shadow: 0 4px 15px ${childTheme.shadowColor};
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  opacity: ${props => props.disabled ? 0.7 : 1};
  filter: ${props => props.disabled ? 'grayscale(40%)' : 'none'};
  transition: all 0.3s ease;
  height: auto;
  min-height: 160px;
`;

const PenaltyPoolCard = styled(motion.div)`
  background: white;
  border-radius: ${childTheme.borderRadius};
  padding: 0.75rem;
  box-shadow: 0 4px 15px rgba(255, 82, 82, 0.2);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  height: auto;
  min-height: 160px;
  border: 1px solid rgba(255, 82, 82, 0.3);
  
  &:hover {
    box-shadow: 0 6px 20px rgba(255, 82, 82, 0.3);
  }
`;

const PoolHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
`;

const PoolTitle = styled.h3`
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  color: ${childTheme.textColor};
`;

const PoolCost = styled.div`
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #ffa000;
`;

const PenaltyCost = styled.div`
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #d32f2f;
`;

const StarIcon = styled.span`
  color: #ffd700;
  margin-right: 0.5rem;
  font-size: 1.2em;
`;

const WarningIcon = styled.span`
  margin-right: 0.5rem;
  font-size: 1.2em;
`;

const PoolItems = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  gap: 0.5rem;
`;

const ItemBubble = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 12px;
  width: 60px;
  height: 68px;
  padding: 0.3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  
  &:hover {
    transform: translateY(-3px);
  }
`;

const PenaltyItemBubble = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff5f5;
  border-radius: 12px;
  width: 60px;
  height: 68px;
  padding: 0.3rem;
  box-shadow: 0 2px 8px rgba(255, 82, 82, 0.1);
  transition: transform 0.2s;
  
  &:hover {
    transform: translateY(-3px);
  }
`;

const ItemImage = styled.div`
  font-size: 1.7rem;
  margin-bottom: 0.3rem;
`;

const ItemName = styled.div`
  font-size: 0.75rem;
  text-align: center;
  color: #333;
  line-height: 1.1;
  max-width: 100%;
`;

const SpinButton = styled(motion.button)`
  padding: 0.5rem 0;
  border-radius: ${childTheme.borderRadius};
  border: none;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  background: ${props => props.disabled ? '#ccc' : '#FF9648'};
  color: white;
  box-shadow: 0 4px 10px ${childTheme.shadowColor};
  transition: all 0.3s ease;
  margin-top: auto;
  
  &:hover {
    transform: ${props => props.disabled ? 'none' : 'translateY(-2px)'};
    box-shadow: ${props => props.disabled ? '0 4px 10px rgba(0,0,0,0.1)' : '0 6px 15px rgba(0,0,0,0.15)'};
    background: ${props => props.disabled ? '#ccc' : '#FF8330'};
  }
  
  &:focus {
    outline: none;
  }
`;

const PenaltySpinButton = styled(motion.button)`
  padding: 0.5rem 0;
  border-radius: ${childTheme.borderRadius};
  border: none;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  color: white;
  box-shadow: 0 4px 10px rgba(255, 82, 82, 0.3);
  transition: all 0.3s ease;
  margin-top: auto;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 82, 82, 0.4);
  }
  
  &:focus {
    outline: none;
  }
`;

const LockOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(2px);
`;

const LockIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
`;

const NeededPoints = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: ${childTheme.textColor};
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2rem;
  color: ${childTheme.textColor};
`;

export default RewardCenter; 