-- 性能优化索引脚本
-- 为儿童奖励系统添加必要的数据库索引
-- 基于实际数据库表结构修正

-- 1. 积分记录表索引 (point_record)
-- 按记录时间查询的索引（用于日期范围查询）
CREATE INDEX IF NOT EXISTS idx_point_record_time ON point_record(record_time);

-- 按关联任务ID查询的索引
CREATE INDEX IF NOT EXISTS idx_point_record_task_id ON point_record(related_task_id);

-- 按关联奖励ID查询的索引
CREATE INDEX IF NOT EXISTS idx_point_record_reward_id ON point_record(related_reward_id);

-- 按变更类型查询的索引
CREATE INDEX IF NOT EXISTS idx_point_record_change_type ON point_record(change_type);

-- 2. 任务表索引 (tasks)
-- 按计划日期查询的索引
CREATE INDEX IF NOT EXISTS idx_tasks_scheduled_date ON tasks(scheduled_date);

-- 按到期日期查询的索引
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);

-- 按状态查询的索引
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);

-- 按状态和计划日期的复合索引（用于状态+日期范围查询）
CREATE INDEX IF NOT EXISTS idx_tasks_status_scheduled_date ON tasks(status, scheduled_date);

-- 按状态和到期日期的复合索引（用于逾期任务查询）
CREATE INDEX IF NOT EXISTS idx_tasks_status_due_date ON tasks(status, due_date);

-- 按模板ID和计划日期的复合索引（用于检查是否已生成任务）
CREATE INDEX IF NOT EXISTS idx_tasks_template_scheduled ON tasks(source_template_id, scheduled_date);

-- 按任务类型查询的索引
CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type);

-- 3. 兑换商品表索引 (exchange_items)
-- 按激活状态查询的索引
CREATE INDEX IF NOT EXISTS idx_exchange_items_active ON exchange_items(is_active);

-- 按分类和激活状态的复合索引
CREATE INDEX IF NOT EXISTS idx_exchange_items_category_active ON exchange_items(category, is_active);

-- 按排序顺序的索引
CREATE INDEX IF NOT EXISTS idx_exchange_items_sort_order ON exchange_items(sort_order);

-- 按创建时间查询的索引
CREATE INDEX IF NOT EXISTS idx_exchange_items_created_time ON exchange_items(created_time);

-- 4. 兑换记录表索引 (exchange_records)
-- 按兑换时间查询的索引
CREATE INDEX IF NOT EXISTS idx_exchange_records_time ON exchange_records(exchange_time);

-- 按状态查询的索引
CREATE INDEX IF NOT EXISTS idx_exchange_records_status ON exchange_records(status);

-- 按商品ID查询的索引
CREATE INDEX IF NOT EXISTS idx_exchange_records_item_id ON exchange_records(exchange_item_id);

-- 按过期时间查询的索引（用于查找过期记录）
CREATE INDEX IF NOT EXISTS idx_exchange_records_expire_time ON exchange_records(expire_time);

-- 5. 奖励物品表索引 (reward_items)
-- 按奖励池ID查询的索引
CREATE INDEX IF NOT EXISTS idx_reward_items_pool_id ON reward_items(pool_id);

-- 按库存查询的索引（用于查找有库存的物品）
CREATE INDEX IF NOT EXISTS idx_reward_items_stock ON reward_items(stock);

-- 按奖励池ID和库存的复合索引
CREATE INDEX IF NOT EXISTS idx_reward_items_pool_stock ON reward_items(pool_id, stock);

-- 6. 计划任务表索引 (scheduled_task)
-- 按激活状态查询的索引
CREATE INDEX IF NOT EXISTS idx_scheduled_task_active ON scheduled_task(active);

-- 按任务类型查询的索引
CREATE INDEX IF NOT EXISTS idx_scheduled_task_type ON scheduled_task(task_type);

-- 显示索引创建完成信息
SELECT 'Performance indexes created successfully!' as message;
