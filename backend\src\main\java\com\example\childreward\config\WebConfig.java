package com.example.childreward.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 对所有接口生效
                .allowedOriginPatterns("*") // 允许所有来源，包括IP地址和localhost
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许所有常用方法
                .allowedHeaders("*") // 允许所有请求头
                .allowCredentials(false) // 不允许携带凭证（这样可以使用通配符）
                .maxAge(3600); // 预检请求的缓存时间
    }
} 