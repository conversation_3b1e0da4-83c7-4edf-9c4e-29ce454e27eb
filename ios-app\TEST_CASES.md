# 今日任务总用时功能测试用例

## 测试场景

### 场景1: 无已完成任务
- **条件**: 今日没有已完成的任务
- **预期结果**: 显示 "0分钟"

### 场景2: 单个已完成任务（小于60分钟）
- **条件**: 1个已完成任务，用时30分钟
- **预期结果**: 显示 "30分钟"

### 场景3: 单个已完成任务（整小时）
- **条件**: 1个已完成任务，用时60分钟
- **预期结果**: 显示 "1小时"

### 场景4: 单个已完成任务（小时+分钟）
- **条件**: 1个已完成任务，用时90分钟
- **预期结果**: 显示 "1小时30分钟"

### 场景5: 多个已完成任务
- **条件**: 
  - 任务1: 30分钟（已完成）
  - 任务2: 45分钟（已完成）
  - 任务3: 20分钟（进行中，不计入）
- **预期结果**: 显示 "1小时15分钟"

### 场景6: 使用实际用时vs预计用时
- **条件**:
  - 任务1: 预计30分钟，实际25分钟（已完成）
  - 任务2: 预计60分钟，无实际用时（已完成）
- **预期结果**: 显示 "1小时25分钟"（25 + 60 = 85分钟）

### 场景7: 不同任务状态
- **条件**:
  - 任务1: 30分钟（已完成 - COMPLETED）
  - 任务2: 45分钟（已批准 - APPROVED）
  - 任务3: 60分钟（进行中 - IN_PROGRESS）
  - 任务4: 20分钟（未开始 - NOT_STARTED）
- **预期结果**: 显示 "1小时15分钟"（只计算COMPLETED和APPROVED）

## 手动测试步骤

### 步骤1: 准备测试数据
1. 在后端创建几个测试任务
2. 设置不同的预计用时和实际用时
3. 将任务设置为不同的状态

### 步骤2: 验证显示
1. 打开iOS应用
2. 进入"我的任务"页面
3. 查看"今日任务"标题右边的"当前已用时"显示
4. 验证时间格式和数值是否正确

### 步骤3: 动态测试
1. 完成一个任务
2. 观察总用时是否实时更新
3. 刷新页面，验证数据持久性

## 自动化测试代码示例

```swift
// 测试用例：计算总用时
func testTodayTotalUsedMinutes() {
    let tasks = [
        TaskSummary(id: 1, status: .completed, expectedMinutes: 30, actualMinutes: 25),
        TaskSummary(id: 2, status: .approved, expectedMinutes: 60, actualMinutes: nil),
        TaskSummary(id: 3, status: .inProgress, expectedMinutes: 45, actualMinutes: nil)
    ]
    
    let totalMinutes = calculateTotalUsedMinutes(tasks: tasks)
    XCTAssertEqual(totalMinutes, 85) // 25 + 60 = 85
}

// 测试用例：格式化时间显示
func testFormattedTime() {
    XCTAssertEqual(formatTime(0), "0分钟")
    XCTAssertEqual(formatTime(30), "30分钟")
    XCTAssertEqual(formatTime(60), "1小时")
    XCTAssertEqual(formatTime(90), "1小时30分钟")
    XCTAssertEqual(formatTime(125), "2小时5分钟")
}
```

## 边界测试

### 边界值测试
- **0分钟**: 验证显示 "0分钟"
- **1分钟**: 验证显示 "1分钟"
- **59分钟**: 验证显示 "59分钟"
- **60分钟**: 验证显示 "1小时"
- **61分钟**: 验证显示 "1小时1分钟"

### 异常情况测试
- **空任务列表**: 验证不会崩溃，显示 "0分钟"
- **负数时间**: 验证处理异常数据
- **超大数值**: 验证大时间值的显示（如24小时以上）

## 性能测试
- **大量任务**: 测试100+任务时的计算性能
- **频繁更新**: 测试任务状态频繁变化时的响应速度
