package com.example.childreward.controller;

import com.example.childreward.entity.ScheduledTask;
import com.example.childreward.entity.TaskType;
import com.example.childreward.repository.ScheduledTaskRepository;
import com.example.childreward.service.TaskGenerationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*") // 允许所有来源跨域请求
@RequestMapping("/api/test")
public class TestController {

    private final ScheduledTaskRepository scheduledTaskRepository;
    private final TaskGenerationService taskGenerationService;
    private static final Logger log = LoggerFactory.getLogger(TestController.class);

    public TestController(ScheduledTaskRepository scheduledTaskRepository, TaskGenerationService taskGenerationService) {
        this.scheduledTaskRepository = scheduledTaskRepository;
        this.taskGenerationService = taskGenerationService;
    }

    @GetMapping("/create-scheduled-task")
    public ResponseEntity<Map<String, Object>> createTestScheduledTask() {
        try {
            log.info("尝试创建测试计划任务");
            
            // 创建一个测试任务
            ScheduledTask task = new ScheduledTask();
            task.setTitle("测试任务");
            task.setDescription("这是一个测试任务");
            task.setPoints(10);
            task.setType(TaskType.REWARD);
            task.setCronExpression("0 0 12 * * ?");
            task.setExpectedMinutes(30);
            task.setDueTime(LocalTime.of(18, 0));
            task.setActive(true);
            
            // 保存到数据库
            ScheduledTask savedTask = scheduledTaskRepository.save(task);
            log.info("成功创建测试计划任务: {}", savedTask);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("task", savedTask);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建测试计划任务失败", e);
            
            // 返回错误信息
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("stackTrace", e.getStackTrace());
            return ResponseEntity.status(500).body(result);
        }
    }
    
    @GetMapping("/get-all-scheduled-tasks")
    public ResponseEntity<Map<String, Object>> getAllScheduledTasks() {
        try {
            log.info("尝试获取所有计划任务");
            
            // 从数据库获取所有任务
            var tasks = scheduledTaskRepository.findAll();
            log.info("成功获取所有计划任务，共 {} 条", tasks.size());
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("tasks", tasks);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取所有计划任务失败", e);
            
            // 返回错误信息
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("stackTrace", e.getStackTrace());
            return ResponseEntity.status(500).body(result);
        }
    }

    @GetMapping("/api/test")
    public Map<String, Object> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "API测试成功");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    @GetMapping("/generate-tasks")
    public ResponseEntity<Map<String, Object>> manualGenerateTasks() {
        try {
            log.info("手动触发任务生成");
            taskGenerationService.manualGenerateTasksFromTemplates();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "任务生成完成");
            result.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动生成任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "任务生成失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
}