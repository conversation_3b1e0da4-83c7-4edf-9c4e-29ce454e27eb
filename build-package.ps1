# CRS Children Reward System - Package Build Script
# Build separate JAR and ZIP packages

param(
    [switch]$SkipTests = $true
)

$ProjectRoot = Get-Location
$BackendDir = Join-Path $ProjectRoot "backend"
$FrontendDir = Join-Path $ProjectRoot "frontend"
$OutputDir = Join-Path $ProjectRoot "packages"

Write-Host "=== CRS Package Build Script ===" -ForegroundColor Cyan
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Cyan
Write-Host "Output Directory: $OutputDir" -ForegroundColor Cyan
Write-Host ""

# Create output directory
if (Test-Path $OutputDir) {
    Remove-Item $OutputDir -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null

# Build Backend JAR
Write-Host "=== Building Backend JAR ===" -ForegroundColor Cyan
Set-Location $BackendDir

if ($SkipTests) {
    Write-Host "Running: mvn clean package -DskipTests" -ForegroundColor Yellow
    & mvn clean package -DskipTests
} else {
    Write-Host "Running: mvn clean package" -ForegroundColor Yellow
    & mvn clean package
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "Backend build failed" -ForegroundColor Red
    Set-Location $ProjectRoot
    exit 1
}

# Copy JAR file
$JarFile = Get-ChildItem -Path "target" -Name "*.jar" | Where-Object { $_ -notlike "*.original" } | Select-Object -First 1
if ($JarFile) {
    $SourceJar = Join-Path "target" $JarFile
    $DestJar = Join-Path $OutputDir "crs-backend.jar"
    Copy-Item $SourceJar $DestJar
    Write-Host "Backend JAR created: $DestJar" -ForegroundColor Green
} else {
    Write-Host "Backend JAR not found" -ForegroundColor Red
    Set-Location $ProjectRoot
    exit 1
}

# Build Frontend
Write-Host "=== Building Frontend ===" -ForegroundColor Cyan
Set-Location $FrontendDir

Write-Host "Running: npm run build" -ForegroundColor Yellow
& npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "Frontend build failed" -ForegroundColor Red
    Set-Location $ProjectRoot
    exit 1
}

# Create frontend ZIP package
$FrontendZip = Join-Path $OutputDir "crs-frontend.zip"
Write-Host "Creating frontend package: $FrontendZip" -ForegroundColor Yellow
Compress-Archive -Path "dist\*" -DestinationPath $FrontendZip -Force
Write-Host "Frontend ZIP created: $FrontendZip" -ForegroundColor Green

# Summary
Write-Host ""
Write-Host "=== Build Complete ===" -ForegroundColor Green
Write-Host "Backend JAR: $(Join-Path $OutputDir "crs-backend.jar")" -ForegroundColor Cyan
Write-Host "Frontend ZIP: $(Join-Path $OutputDir "crs-frontend.zip")" -ForegroundColor Cyan
Write-Host ""
Write-Host "Generated Files:" -ForegroundColor Cyan
Write-Host "- crs-backend.jar - Spring Boot executable JAR" -ForegroundColor Yellow
Write-Host "- crs-frontend.zip - Frontend static files" -ForegroundColor Yellow

Set-Location $ProjectRoot
