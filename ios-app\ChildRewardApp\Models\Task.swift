import Foundation
import SwiftUI

// API响应模型
struct TaskListResponse: Codable {
    let data: [TaskSummary]?
    let message: String?
    let success: Bool?
}

// 任务状态枚举
enum TaskStatus: String, CaseIterable, Codable {
    case notStarted = "NOT_STARTED"
    case pending = "PENDING"
    case inProgress = "IN_PROGRESS"
    case completed = "COMPLETED"
    case approved = "APPROVED"
    case overdue = "OVERDUE"
    case cancelled = "CANCELLED"
    case rejected = "REJECTED"
    case penaltyApproval = "PENALTY_APPROVAL"

    var displayName: String {
        switch self {
        case .notStarted: return "未开始"
        case .pending: return "待审批"  // PENDING状态改为"待审批"
        case .inProgress: return "进行中"
        case .completed: return "已完成"
        case .approved: return "已批准"
        case .overdue: return "已过期"
        case .cancelled: return "已取消"
        case .rejected: return "已作废"
        case .penaltyApproval: return "惩罚审批"
        }
    }

    var iconName: String {
        switch self {
        case .notStarted: return "circle"
        case .pending: return "clock"
        case .inProgress: return "play.circle.fill"
        case .completed: return "checkmark.circle.fill"
        case .approved: return "checkmark.circle.fill"
        case .overdue: return "exclamationmark.circle.fill"
        case .cancelled: return "xmark.circle.fill"
        case .rejected: return "xmark.circle.fill"
        case .penaltyApproval: return "exclamationmark.triangle.fill"
        }
    }

    var color: Color {
        switch self {
        case .notStarted: return .gray
        case .pending: return .orange
        case .inProgress: return .blue
        case .completed: return .green
        case .approved: return .green
        case .overdue: return .red
        case .cancelled: return .gray
        case .rejected: return .red
        case .penaltyApproval: return .purple
        }
    }
}

// 任务模型
struct TaskModel: Codable, Identifiable {
    let id: Int
    let sourceTemplateId: Int?
    let title: String
    let description: String?  // 添加描述字段
    let expectedMinutes: Int
    let basePoints: Int
    let dueTime: String?
    let dueDate: String?
    let status: TaskStatus
    let taskType: String?
    let startTime: String?
    let endTime: String?
    let actualPoints: Int?
    let createdTime: String?
    let scheduledDate: String?
    let actualMinutes: Int?
    
    // 计算属性
    var isCompleted: Bool {
        return status == .completed || status == .approved
    }
    
    var isOverdue: Bool {
        return status == .overdue
    }
    
    var canStart: Bool {
        return (status == .notStarted || status == .pending) && status != .rejected && status != .penaltyApproval
    }

    var canComplete: Bool {
        return status == .inProgress && status != .rejected && status != .penaltyApproval
    }
    
    // 格式化时间
    var formattedDueDate: String? {
        guard let dueDate = dueDate else { return nil }
        return DateFormatter.taskDate.string(from: ISO8601DateFormatter().date(from: dueDate) ?? Date())
    }
    
    var formattedExpectedTime: String {
        if expectedMinutes < 60 {
            return "\(expectedMinutes)分钟"
        } else {
            let hours = expectedMinutes / 60
            let minutes = expectedMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }
}

// 类型别名，避免与Swift内置Task冲突
typealias Task = TaskModel

// 任务摘要模型（用于列表显示）
struct TaskSummary: Codable, Identifiable {
    let id: Int
    let sourceTemplateId: Int?
    let title: String
    let description: String?  // 可选字段，后端可能不返回
    let expectedMinutes: Int
    let basePoints: Int
    let dueTime: String?
    let dueDate: String?
    let status: TaskStatus
    let taskType: String?
    let startTime: String?
    let endTime: String?
    let actualPoints: Int?
    let createdTime: String?
    let scheduledDate: String?
    let actualMinutes: Int?
    
    // 计算属性
    var isCompleted: Bool {
        return status == .completed || status == .approved
    }

    var isOverdue: Bool {
        return status == .overdue
    }

    var canStart: Bool {
        return (status == .notStarted || status == .pending) && status != .rejected && status != .penaltyApproval
    }

    var canComplete: Bool {
        return status == .inProgress && status != .rejected && status != .penaltyApproval
    }

    // 显示状态（根据实际情况动态判断）
    var displayStatus: TaskStatus {
        // 如果状态是PENDING但有实际耗时，说明任务已完成，等待审批
        if status == .pending && actualMinutes != nil {
            return .pending  // 保持PENDING，但显示为"待审批"
        }
        // 如果状态是PENDING但没有实际耗时，说明任务还未开始
        else if status == .pending && actualMinutes == nil {
            return .notStarted  // 显示为"未开始"
        }
        // 其他情况保持原状态
        else {
            return status
        }
    }

    var formattedExpectedTime: String {
        if expectedMinutes < 60 {
            return "\(expectedMinutes)分钟"
        } else {
            let hours = expectedMinutes / 60
            let minutes = expectedMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }

    // 格式化实际耗时
    var formattedActualTime: String? {
        guard let actualMinutes = actualMinutes else { return nil }
        if actualMinutes < 60 {
            return "\(actualMinutes)分钟"
        } else {
            let hours = actualMinutes / 60
            let minutes = actualMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }

    // 格式化时间信息（预计 + 实际）
    var formattedTimeInfo: String {
        if let actualTime = formattedActualTime {
            return "\(formattedExpectedTime) / 实际\(actualTime)"
        } else {
            return formattedExpectedTime
        }
    }

    // 计算任务进度（0.0 到 1.0）
    var progress: Double {
        switch status {
        case .notStarted:
            return 0.0
        case .pending:
            // 如果有实际耗时，说明任务已完成，等待审批
            if actualMinutes != nil {
                return 1.0
            } else {
                return 0.0
            }
        case .inProgress:
            // 如果有实际耗时，根据实际耗时与预期耗时的比例计算进度
            if let actual = actualMinutes {
                let progress = Double(actual) / Double(expectedMinutes)
                return min(progress, 1.0) // 最大不超过100%
            } else {
                return 0.1 // 已开始但没有实际耗时记录，显示10%
            }
        case .completed, .approved:
            return 1.0
        case .overdue, .cancelled, .rejected:
            return 0.0
        case .penaltyApproval:
            return 1.0 // 惩罚审批状态视为已完成
        }
    }

    // 进度条颜色
    var progressColor: Color {
        switch status {
        case .notStarted:
            return .gray
        case .pending:
            return actualMinutes != nil ? .orange : .gray
        case .inProgress:
            return .blue
        case .completed, .approved:
            return .green
        case .overdue:
            return .red
        case .cancelled:
            return .gray
        case .rejected:
            return .red
        case .penaltyApproval:
            return .purple
        }
    }
}

// 日期格式化扩展
extension DateFormatter {
    static let taskDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
    
    static let taskDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}

// 任务操作枚举
enum TaskAction {
    case start
    case complete
    case viewDetail
}

// 任务统计模型
struct TaskStatistics: Codable {
    let todayCompleted: Int
    let todayInProgress: Int
    let todayNotStarted: Int
    let todayPending: Int          // 新增：今日待审核任务数
    let todayTotal: Int
    let todayAvailablePoints: Int  // 今日可获得积分
    let todayEarnedPoints: Int     // 今日已获得积分

    // 计算属性
    var todayCompletionRate: Double {
        guard todayTotal > 0 else { return 0.0 }
        return Double(todayCompleted) / Double(todayTotal)
    }

    var todayRemainingPoints: Int {
        return todayAvailablePoints - todayEarnedPoints
    }
}



