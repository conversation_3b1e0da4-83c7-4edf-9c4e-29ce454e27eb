import SwiftUI

// MARK: - 奖励卡片组件
struct RewardCard: View {
    let reward: RewardItem
    let userPoints: Int
    let onExchange: () -> Void
    let onTap: () -> Void
    
    // 可选参数
    var showExchangeButton: Bool = true
    var isCompact: Bool = false
    var isFavorite: Bool = false
    var onFavoriteToggle: (() -> Void)? = nil

    var body: some View {
        Button(action: onTap) {
            if isCompact {
                compactLayout
            } else {
                standardLayout
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 布局变体
    
    private var standardLayout: some View {
        VStack(spacing: 16) {
            // 奖励图标和收藏按钮
            rewardIconSection
            
            // 奖励信息
            rewardInfoSection
            
            // 兑换按钮
            if showExchangeButton {
                exchangeButtonSection
            }
        }
        .padding(20)
        .background(cardBackground)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    private var compactLayout: some View {
        HStack(spacing: 16) {
            // 紧凑图标
            compactIcon
            
            // 信息区域
            VStack(alignment: .leading, spacing: 4) {
                Text(reward.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text("\(reward.pointsCost) 积分")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 紧凑兑换按钮
            if showExchangeButton {
                compactExchangeButton
            }
        }
        .padding(16)
        .background(cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - 子视图组件
    
    private var rewardIconSection: some View {
        ZStack {
            // 主图标
            ZStack {
                Circle()
                    .fill(reward.color.opacity(0.2))
                    .frame(width: 80, height: 80)

                Image(systemName: reward.iconName)
                    .font(.system(size: 32))
                    .foregroundColor(reward.color)
            }
            
            // 收藏按钮
            if let onFavoriteToggle = onFavoriteToggle {
                VStack {
                    HStack {
                        Spacer()
                        favoriteButton(action: onFavoriteToggle)
                    }
                    Spacer()
                }
            }
            
            // 不可用遮罩
            if !reward.isAvailable {
                Circle()
                    .fill(Color.black.opacity(0.3))
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    )
            }
        }
    }
    
    private var rewardInfoSection: some View {
        VStack(spacing: 8) {
            // 奖励名称
            Text(reward.name)
                .font(.headline)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .foregroundColor(.primary)

            // 积分消耗
            pointsCostBadge
            
            // 额外信息
            if let description = reward.description {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            
            // 分类标签
            if let category = reward.category {
                categoryBadge(category)
            }
        }
    }
    
    private var exchangeButtonSection: some View {
        exchangeButton
    }
    
    private var compactIcon: some View {
        ZStack {
            Circle()
                .fill(reward.color.opacity(0.2))
                .frame(width: 50, height: 50)

            Image(systemName: reward.iconName)
                .font(.system(size: 20))
                .foregroundColor(reward.color)
            
            if !reward.isAvailable {
                Circle()
                    .fill(Color.black.opacity(0.3))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.caption)
                            .foregroundColor(.white)
                    )
            }
        }
    }
    
    private var compactExchangeButton: some View {
        Button(action: onExchange) {
            Text(canAfford ? "兑换" : "积分不足")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(buttonColor)
                .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!canAfford || !reward.isAvailable)
    }
    
    private var pointsCostBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: "star.fill")
                .font(.caption)
                .foregroundColor(.orange)
            
            Text("\(reward.pointsCost)")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.orange)
            
            Text("积分")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var exchangeButton: some View {
        Button(action: onExchange) {
            HStack(spacing: 8) {
                if !canAfford {
                    Image(systemName: "exclamationmark.circle.fill")
                        .font(.caption)
                } else if !reward.isAvailable {
                    Image(systemName: "clock.fill")
                        .font(.caption)
                }
                
                Text(buttonText)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(buttonColor)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!canAfford || !reward.isAvailable)
    }
    
    private func favoriteButton(action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Image(systemName: isFavorite ? "heart.fill" : "heart")
                .font(.title3)
                .foregroundColor(isFavorite ? .red : .gray)
                .padding(8)
                .background(Color.white.opacity(0.9))
                .cornerRadius(8)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func categoryBadge(_ category: RewardCategory) -> some View {
        Text(category.displayName)
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.blue)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
    }
    
    // MARK: - 计算属性
    
    private var canAfford: Bool {
        return userPoints >= reward.pointsCost
    }
    
    private var buttonText: String {
        if !reward.isAvailable {
            return "暂不可用"
        } else if !canAfford {
            return "积分不足"
        } else {
            return "兑换"
        }
    }
    
    private var buttonColor: Color {
        if !reward.isAvailable {
            return .gray
        } else if !canAfford {
            return .red.opacity(0.7)
        } else {
            return reward.color
        }
    }
    
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: isCompact ? 12 : 16)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: isCompact ? 12 : 16)
                    .stroke(
                        canAfford ? reward.color.opacity(0.3) : Color.clear,
                        lineWidth: canAfford ? 1 : 0
                    )
            )
    }
}

// MARK: - 预览
struct RewardCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // 标准卡片
            RewardCard(
                reward: RewardItem.mockRewards[0],
                userPoints: 100,
                onExchange: {},
                onTap: {},
                isFavorite: false,
                onFavoriteToggle: {}
            )
            
            // 紧凑卡片
            RewardCard(
                reward: RewardItem.mockRewards[1],
                userPoints: 50,
                onExchange: {},
                onTap: {},
                isCompact: true
            )
            
            // 积分不足的卡片
            RewardCard(
                reward: RewardItem.mockRewards[2],
                userPoints: 30,
                onExchange: {},
                onTap: {}
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}
