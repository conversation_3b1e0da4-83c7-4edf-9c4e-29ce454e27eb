import axios from 'axios';

// 儿童端API配置
const childApiConfig = {
  development: 'http://localhost:18080/api',
  production: 'http://*************:18080/api'
};

// 家长端API配置
const parentApiConfig = {
  development: 'http://localhost:18080/api',
  production: 'http://***************:7002/api'
};

// 获取儿童端API地址
const getChildApiBaseUrl = () => {
  const env = import.meta.env.PROD ? 'production' : 'development';
  return childApiConfig[env];
};

// 获取家长端API地址
const getParentApiBaseUrl = () => {
  const env = import.meta.env.PROD ? 'production' : 'development';
  return parentApiConfig[env];
};

// 通用API地址获取函数（向后兼容）
const getApiBaseUrl = () => {
  const currentPath = window.location.pathname;
  if (currentPath.startsWith('/child')) {
    return getChildApiBaseUrl();
  } else if (currentPath.startsWith('/parent')) {
    return getParentApiBaseUrl();
  }
  // 默认使用家长端配置
  return getParentApiBaseUrl();
};

// 创建通用API客户端配置
const createClientConfig = (baseURL) => ({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  // 优化超时时间，提升用户体验
  timeout: 10000, // 10秒超时，避免用户等待过久
  // 允许不完整的JSON响应
  transformResponse: [
    function(data) {
      if (typeof data === 'string') {
        try {
          return JSON.parse(data);
        } catch (e) {
          // 如果解析失败，返回原始数据
          console.warn('JSON解析失败，返回原始数据', e);
          return { originalData: data, parseError: true };
        }
      }
      return data;
    }
  ],
  validateStatus: function (status) {
    // 将201也视为成功状态码
    return status >= 200 && status < 300;
  }
});

// 配置拦截器
const setupInterceptors = (client, clientType = 'API') => {
  // 请求拦截器
  client.interceptors.request.use(
    config => {
      console.log(`${clientType} 发送请求: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);
      return config;
    },
    error => {
      console.error(`${clientType} 请求错误:`, error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  client.interceptors.response.use(
    response => {
      console.log(`${clientType} 收到响应: ${response.status} ${response.config.url}`);
      return response;
    },
    error => {
      // 特殊处理201 Created状态，将其视为成功
      if (error.response && error.response.status === 201) {
        console.log(`${clientType} 收到201 Created响应，处理为成功`);
        return {
          status: 201,
          data: error.response.data || { success: true }
        };
      }

      // 处理网络错误
      if (error.code === 'ERR_NETWORK' || error.code === 'ERR_INCOMPLETE_CHUNKED_ENCODING') {
        console.warn(`${clientType} 网络错误(${error.code})，URL: ${error.config?.url}`);

        // 如果是奖品池相关请求，返回一个空数组或默认值，允许应用继续运行
        if (error.config?.url?.includes('/rewards/pools')) {
          if (error.config.url === '/rewards/pools') {
            console.log(`${clientType} 奖品池列表请求失败，返回空数组`);
            return { data: [] };
          } else if (error.config.url.match(/\/rewards\/pools\/\d+$/)) {
            console.log(`${clientType} 奖品池详情请求失败，返回默认奖品池`);
            return {
              data: {
                id: parseInt(error.config.url.split('/').pop()),
                name: '未命名奖品池',
                costPoints: 10,
                isEnabled: true,
                rewardItems: []
              }
            };
          } else if (error.config.url.match(/\/rewards\/pools\/\d+\/items$/)) {
            console.log(`${clientType} 奖品项列表请求失败，返回空数组`);
            const poolId = parseInt(error.config.url.split('/')[3]);
            return { data: [] };
          }
        }
      }

      console.error(`${clientType} 响应错误:`, error);
      return Promise.reject(error);
    }
  );
};

// 创建儿童端API客户端
export const createChildApiClient = () => {
  const baseURL = getChildApiBaseUrl();
  console.log(`🧒 创建儿童端API客户端，地址: ${baseURL}`);

  const client = axios.create(createClientConfig(baseURL));
  setupInterceptors(client, '儿童端API');
  return client;
};

// 创建家长端API客户端
export const createParentApiClient = () => {
  const baseURL = getParentApiBaseUrl();
  console.log(`👨‍👩‍👧‍👦 创建家长端API客户端，地址: ${baseURL}`);

  const client = axios.create(createClientConfig(baseURL));
  setupInterceptors(client, '家长端API');
  return client;
};

// 通用API客户端（向后兼容，根据路径自动选择）
export const getApiClient = () => {
  const currentPath = window.location.pathname;
  if (currentPath.startsWith('/child')) {
    return createChildApiClient();
  } else if (currentPath.startsWith('/parent')) {
    return createParentApiClient();
  }
  // 默认使用家长端
  return createParentApiClient();
};

// 导出配置函数供其他模块使用
export {
  getApiBaseUrl,
  getChildApiBaseUrl,
  getParentApiBaseUrl,
  childApiConfig,
  parentApiConfig
};
