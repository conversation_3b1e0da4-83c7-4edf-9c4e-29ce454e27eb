#!/bin/bash

echo "=== NAS性能诊断脚本 ==="
echo "检查时间: $(date)"
echo ""

# 1. 基础系统信息
echo "=== 1. 系统基础信息 ==="
echo "CPU核心数: $(nproc)"
echo "总内存: $(free -h | grep Mem | awk '{print $2}')"
echo "可用内存: $(free -h | grep Mem | awk '{print $7}')"
echo "系统负载: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

# 2. Java进程检查
echo "=== 2. Java进程状态 ==="
if pgrep -f "spring-boot" > /dev/null; then
    echo "✓ Spring Boot进程运行中"
    JAVA_PID=$(pgrep -f "spring-boot")
    echo "进程ID: $JAVA_PID"
    echo "内存使用: $(ps -p $JAVA_PID -o pid,ppid,pcpu,pmem,vsz,rss,comm --no-headers)"
    
    # 检查JVM参数
    echo "JVM参数:"
    ps -p $JAVA_PID -o args --no-headers | tr ' ' '\n' | grep -E "^-X|^-D" | head -10
else
    echo "✗ 未找到Spring Boot进程"
fi
echo ""

# 3. 端口和网络检查
echo "=== 3. 网络状态 ==="
echo "端口18080监听状态:"
if netstat -tlnp 2>/dev/null | grep :18080 > /dev/null; then
    echo "✓ 端口18080正在监听"
    netstat -tlnp | grep :18080
else
    echo "✗ 端口18080未监听"
fi

echo ""
echo "网络连接数:"
netstat -an | grep :18080 | wc -l
echo ""

# 4. 数据库连接检查
echo "=== 4. 数据库状态 ==="
if command -v mysql > /dev/null; then
    echo "MySQL连接测试:"
    mysql -u root -p123456 -e "SELECT 'Database connection OK' as status;" 2>/dev/null || echo "✗ 数据库连接失败"
    
    echo "MySQL进程状态:"
    if pgrep mysqld > /dev/null; then
        echo "✓ MySQL进程运行中"
        MYSQL_PID=$(pgrep mysqld)
        echo "MySQL内存使用: $(ps -p $MYSQL_PID -o pid,pcpu,pmem,vsz,rss --no-headers)"
    else
        echo "✗ MySQL进程未运行"
    fi
else
    echo "MySQL客户端未安装，跳过数据库检查"
fi
echo ""

# 5. 磁盘I/O简单测试
echo "=== 5. 磁盘性能测试 ==="
echo "当前目录磁盘使用:"
df -h . | tail -1

echo ""
echo "简单I/O测试 (100MB):"
time (dd if=/dev/zero of=/tmp/test_io bs=1M count=100 2>/dev/null && sync) 2>&1 | grep real
rm -f /tmp/test_io
echo ""

# 6. 应用响应测试
echo "=== 6. 应用响应测试 ==="
if command -v curl > /dev/null; then
    echo "健康检查接口测试:"
    RESPONSE_TIME=$(curl -w "%{time_total}" -o /dev/null -s "http://localhost:18080/actuator/health" 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "✓ 健康检查响应时间: ${RESPONSE_TIME}秒"
    else
        echo "✗ 健康检查接口无响应"
    fi
    
    echo ""
    echo "API接口测试:"
    RESPONSE_TIME=$(curl -w "%{time_total}" -o /dev/null -s "http://localhost:18080/api/points/total" 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "✓ API响应时间: ${RESPONSE_TIME}秒"
    else
        echo "✗ API接口无响应"
    fi
else
    echo "curl未安装，跳过接口测试"
fi
echo ""

# 7. 系统资源使用情况
echo "=== 7. 系统资源使用 ==="
echo "CPU使用率前5的进程:"
ps aux --sort=-%cpu | head -6

echo ""
echo "内存使用率前5的进程:"
ps aux --sort=-%mem | head -6
echo ""

# 8. 建议
echo "=== 8. 性能建议 ==="
TOTAL_MEM=$(free -m | grep Mem | awk '{print $2}')
AVAILABLE_MEM=$(free -m | grep Mem | awk '{print $7}')
CPU_COUNT=$(nproc)

if [ $TOTAL_MEM -lt 2048 ]; then
    echo "⚠️  建议: 总内存少于2GB，建议增加到至少2GB"
fi

if [ $AVAILABLE_MEM -lt 512 ]; then
    echo "⚠️  建议: 可用内存不足512MB，可能影响性能"
fi

if [ $CPU_COUNT -lt 2 ]; then
    echo "⚠️  建议: CPU核心数少于2个，建议增加到至少2核"
fi

echo ""
echo "=== 诊断完成 ==="
echo "如果发现性能问题，请检查:"
echo "1. 虚拟机分配的CPU和内存资源"
echo "2. 数据库配置和索引"
echo "3. 网络延迟"
echo "4. 磁盘I/O性能"
