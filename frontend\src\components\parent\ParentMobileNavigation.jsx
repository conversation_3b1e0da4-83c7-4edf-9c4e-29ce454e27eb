import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { taskApi } from '../../api/apiService';

const ParentMobileNavigation = () => {
  const location = useLocation();
  const [pendingCount, setPendingCount] = useState(0);

  // 获取待审批数量
  useEffect(() => {
    const fetchPendingCount = async () => {
      try {
        const response = await taskApi.getPendingTasks();
        setPendingCount(response.data?.length || 0);
      } catch (error) {
        console.error('获取待审批数量失败:', error);
      }
    };

    fetchPendingCount();
    // 每30秒刷新一次
    const interval = setInterval(fetchPendingCount, 30000);
    return () => clearInterval(interval);
  }, []);
  
  // 移动端主要导航项目（精简版，突出重要功能）
  const navItems = [
    {
      path: '/parent',
      icon: '📊',
      label: '概览',
      exact: true
    },
    {
      path: '/parent/tasks',
      icon: '📋',
      label: '任务'
    },
    {
      path: '/parent/approvals',
      icon: '✅',
      label: '审批',
      highlight: true // 突出显示审批功能
    },
    {
      path: '/parent/exchange',
      icon: '🛒',
      label: '兑换'
    },
    {
      path: '/parent/settings',
      icon: '⚙️',
      label: '更多'
    }
  ];

  return (
    <MobileNavContainer>
      <NavBackground />
      {navItems.map((item) => {
        const isActive = item.exact 
          ? location.pathname === item.path
          : location.pathname.startsWith(item.path);
          
        return (
          <MobileNavItem
            key={item.path}
            to={item.path}
            $isActive={isActive}
            onClick={() => {
              // 简单的触觉反馈（如果支持）
              if (navigator.vibrate) {
                navigator.vibrate(10);
              }
            }}
          >
            <IconWrapper $isActive={isActive}>
              <NavIcon $isActive={isActive}>{item.icon}</NavIcon>
              {/* 审批功能显示待处理数量 */}
              {item.path === '/parent/approvals' && pendingCount > 0 && (
                <BadgeCount>
                  {pendingCount > 99 ? '99+' : pendingCount}
                </BadgeCount>
              )}
              {/* 活跃状态的背景动画 */}
              {isActive && (
                <ActiveBackground
                  layoutId="parentMobileActiveBg"
                  initial={false}
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                />
              )}
            </IconWrapper>
            <NavLabel $isActive={isActive}>{item.label}</NavLabel>
            {isActive && (
              <ActiveIndicator
                layoutId="parentMobileActiveIndicator"
                initial={false}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              />
            )}
          </MobileNavItem>
        );
      })}
    </MobileNavContainer>
  );
};

const MobileNavContainer = styled.nav`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0.75rem 0.5rem calc(0.75rem + env(safe-area-inset-bottom, 0px));
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.15);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 1000;
  min-height: 70px;

  /* 只在手机端显示 */
  @media (min-width: 769px) {
    display: none;
  }
`;

const NavBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
`;

const MobileNavItem = styled(NavLink)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: ${props => props.$isActive ? parentTheme.primaryColor : '#8E8E93'};
  position: relative;
  padding: 0.5rem 0.75rem;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 64px;
  flex: 1;
  max-width: 80px;
  z-index: 1;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  &:active {
    transform: scale(0.92);
  }

  /* 增强触摸目标 */
  @media (pointer: coarse) {
    min-height: 48px;
  }
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background-color: ${props => props.$isActive ? `${parentTheme.primaryColor}20` : 'transparent'};
  margin-bottom: 0.375rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
`;

const NavIcon = styled.span`
  font-size: 1.4rem;
  line-height: 1;
  filter: ${props => props.$isActive ? 'none' : 'grayscale(0.3)'};
  transition: filter 0.3s ease;
`;

const NavLabel = styled.span`
  font-size: 0.75rem;
  font-weight: ${props => props.$isActive ? '600' : '500'};
  text-align: center;
  line-height: 1.2;
  letter-spacing: -0.01em;
  margin-top: 0.125rem;
`;

const ActiveIndicator = styled(motion.div)`
  position: absolute;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: ${parentTheme.primaryColor};
  border-radius: 50%;
  box-shadow: 0 0 0 2px white;
`;

const ActiveBackground = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, ${parentTheme.primaryColor}15 0%, ${parentTheme.primaryColor}25 100%);
  border-radius: 12px;
  z-index: -1;
`;

const BadgeCount = styled.div`
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: white;
  font-size: 0.65rem;
  font-weight: 700;
  padding: 0.2rem 0.35rem;
  border-radius: 12px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
  border: 2px solid white;
  z-index: 2;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
`;

export default ParentMobileNavigation;
