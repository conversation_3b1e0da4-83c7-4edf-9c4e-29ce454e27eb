@echo off
echo 🧪 测试部署包...
echo.

REM 检查部署包是否存在
if not exist "crs-frontend-deploy.zip" (
    echo ❌ 部署包不存在，请先运行构建
    pause
    exit /b 1
)

echo ✅ 部署包存在: crs-frontend-deploy.zip
echo.

REM 创建临时测试目录
if exist "test-deploy" rmdir /s /q "test-deploy"
mkdir "test-deploy"
mkdir "test-deploy\crs"

echo 📦 解压部署包到测试目录...
powershell "Expand-Archive -Path 'crs-frontend-deploy.zip' -DestinationPath 'test-deploy\crs' -Force"

echo.
echo 📋 部署包内容:
dir "test-deploy\crs" /b

echo.
echo 🔍 检查关键文件:
if exist "test-deploy\crs\index.html" (
    echo ✅ index.html 存在
) else (
    echo ❌ index.html 缺失
)

if exist "test-deploy\crs\assets" (
    echo ✅ assets 目录存在
) else (
    echo ❌ assets 目录缺失
)

if exist "test-deploy\crs\star.svg" (
    echo ✅ star.svg 存在
) else (
    echo ❌ star.svg 缺失
)

echo.
echo 🌐 模拟Apache部署结构:
echo test-deploy\
echo └── crs\
echo     ├── index.html
echo     ├── assets\
echo     └── star.svg
echo.
echo 📝 部署说明:
echo 1. 将 crs-frontend-deploy.zip 上传到服务器
echo 2. 在Apache根目录创建 crs 文件夹
echo 3. 解压内容到 crs 文件夹
echo 4. 访问: http://your-server/crs/
echo 5. 确保后端运行在: http://*************:18080
echo.

REM 清理测试目录
rmdir /s /q "test-deploy"

echo ✅ 测试完成！部署包准备就绪。
echo.
pause