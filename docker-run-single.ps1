# 儿童奖励系统 - 一体化容器本地运行脚本
param (
    [string]$Action = "start"  # start, stop, restart, logs, status, build
)

$CONTAINER_NAME = "crs-system"
$IMAGE_NAME = "zlean/crs-system:1.0.0"

function Build-And-Start {
    Write-Host "构建并启动儿童奖励系统..." -ForegroundColor Green
    
    # 1. 构建镜像
    Write-Host "`n1. 构建Docker镜像..." -ForegroundColor Yellow
    .\docker-build-single.ps1 -Version "1.0.0"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: 镜像构建失败" -ForegroundColor Red
        return
    }
    
    # 2. 停止现有容器
    Stop-Container
    
    # 3. 启动新容器
    Start-Container
}

function Start-Container {
    Write-Host "启动容器..." -ForegroundColor Green
    
    # 检查容器是否已存在
    $existing = docker ps -a --filter "name=$CONTAINER_NAME" --format "{{.Names}}"
    if ($existing -eq $CONTAINER_NAME) {
        Write-Host "启动现有容器..." -ForegroundColor Yellow
        docker start $CONTAINER_NAME
    } else {
        Write-Host "创建并启动新容器..." -ForegroundColor Yellow
        docker run -d `
            --name $CONTAINER_NAME `
            -p 8080:80 `
            -p 18080:18080 `
            -e SPRING_PROFILES_ACTIVE=prod `
            -e JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC" `
            -e MYSQL_HOST=localhost `
            -e MYSQL_PORT=3306 `
            -e MYSQL_DATABASE=crs `
            -e MYSQL_USERNAME=root `
            -e MYSQL_PASSWORD=123456 `
            -v "${PWD}/logs:/app/logs" `
            $IMAGE_NAME
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 容器启动成功!" -ForegroundColor Green
        Write-Host "等待服务完全启动..." -ForegroundColor Yellow
        Start-Sleep -Seconds 15
        Show-Status
    } else {
        Write-Host "❌ 容器启动失败" -ForegroundColor Red
    }
}

function Stop-Container {
    Write-Host "停止容器..." -ForegroundColor Yellow
    
    $running = docker ps --filter "name=$CONTAINER_NAME" --format "{{.Names}}"
    if ($running -eq $CONTAINER_NAME) {
        docker stop $CONTAINER_NAME
        Write-Host "✅ 容器已停止" -ForegroundColor Green
    } else {
        Write-Host "容器未运行" -ForegroundColor Yellow
    }
}

function Remove-Container {
    Stop-Container
    
    $existing = docker ps -a --filter "name=$CONTAINER_NAME" --format "{{.Names}}"
    if ($existing -eq $CONTAINER_NAME) {
        Write-Host "删除容器..." -ForegroundColor Yellow
        docker rm $CONTAINER_NAME
        Write-Host "✅ 容器已删除" -ForegroundColor Green
    }
}

function Restart-Container {
    Write-Host "重启容器..." -ForegroundColor Yellow
    Stop-Container
    Start-Sleep -Seconds 2
    Start-Container
}

function Show-Logs {
    Write-Host "显示容器日志..." -ForegroundColor Yellow
    Write-Host "按 Ctrl+C 退出日志查看" -ForegroundColor Cyan
    docker logs -f $CONTAINER_NAME
}

function Show-Status {
    Write-Host "`n======= 容器状态 =======" -ForegroundColor Cyan
    
    # 容器状态
    $status = docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    if ($status) {
        Write-Host $status -ForegroundColor White
    } else {
        Write-Host "容器未运行" -ForegroundColor Red
        return
    }
    
    # 资源使用
    Write-Host "`n======= 资源使用 =======" -ForegroundColor Cyan
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $CONTAINER_NAME
    
    # 健康检查
    Write-Host "`n======= 服务检查 =======" -ForegroundColor Cyan
    Test-Services
    
    # 访问地址
    Write-Host "`n======= 访问地址 =======" -ForegroundColor Cyan
    Write-Host "家长端: http://localhost:8080" -ForegroundColor White
    Write-Host "儿童端: http://localhost:8080/child" -ForegroundColor White
    Write-Host "后端API: http://localhost:18080" -ForegroundColor White
    Write-Host "健康检查: http://localhost:8080/actuator/health" -ForegroundColor White
}

function Test-Services {
    # 检查前端服务
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 前端服务: 正常" -ForegroundColor Green
        } else {
            Write-Host "❌ 前端服务: 异常 (状态码: $($response.StatusCode))" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 前端服务: 无法连接" -ForegroundColor Red
    }
    
    # 检查后端健康状态
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8080/actuator/health" -TimeoutSec 5
        if ($response.status -eq "UP") {
            Write-Host "✅ 后端服务: 健康" -ForegroundColor Green
        } else {
            Write-Host "❌ 后端服务: 异常" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 后端服务: 无法连接" -ForegroundColor Red
    }
}

function Show-Help {
    Write-Host "儿童奖励系统 - 一体化容器管理" -ForegroundColor Green
    Write-Host "`n用法: .\docker-run-single.ps1 -Action [操作]" -ForegroundColor Yellow
    Write-Host "`n可用操作:" -ForegroundColor Cyan
    Write-Host "  build   - 构建并启动 (首次使用)" -ForegroundColor White
    Write-Host "  start   - 启动容器" -ForegroundColor White
    Write-Host "  stop    - 停止容器" -ForegroundColor White
    Write-Host "  restart - 重启容器" -ForegroundColor White
    Write-Host "  remove  - 删除容器" -ForegroundColor White
    Write-Host "  logs    - 查看日志" -ForegroundColor White
    Write-Host "  status  - 查看状态" -ForegroundColor White
    Write-Host "  test    - 服务测试" -ForegroundColor White
    Write-Host "`n示例:" -ForegroundColor Cyan
    Write-Host "  .\docker-run-single.ps1 -Action build    # 首次构建并启动" -ForegroundColor White
    Write-Host "  .\docker-run-single.ps1 -Action start    # 启动服务" -ForegroundColor White
    Write-Host "  .\docker-run-single.ps1 -Action status   # 查看状态" -ForegroundColor White
}

# 主执行逻辑
switch ($Action.ToLower()) {
    "build" {
        Build-And-Start
    }
    "start" {
        Start-Container
    }
    "stop" {
        Stop-Container
    }
    "restart" {
        Restart-Container
    }
    "remove" {
        Remove-Container
    }
    "logs" {
        Show-Logs
    }
    "status" {
        Show-Status
    }
    "test" {
        Test-Services
    }
    default {
        Show-Help
    }
}