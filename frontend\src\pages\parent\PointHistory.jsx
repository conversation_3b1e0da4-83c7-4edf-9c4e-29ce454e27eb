import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { pointApi } from '../../api/apiService';
import { parentTheme } from '../../utils/themes';
import { format } from 'date-fns';
import { getParentApiBaseUrl } from '../../api/apiConfig';

const PointHistory = () => {
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30天前
    endDate: new Date().toISOString().split('T')[0] // 今天
  });
  const [filter, setFilter] = useState('ALL'); // ALL, EARNED, SPENT
  const [stats, setStats] = useState({
    totalEarned: 0,
    totalSpent: 0,
    net: 0
  });

  // 从API获取积分记录
  const fetchPointRecords = async () => {
    setLoading(true);
    try {
      const startTime = new Date(`${dateRange.startDate}T00:00:00`).toISOString();
      const endTime = new Date(`${dateRange.endDate}T23:59:59`).toISOString();
      
      // 计算两个日期之间的天数
      const start = new Date(dateRange.startDate);
      const end = new Date(dateRange.endDate);
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 包含起止日期
      
      // 添加详细日志
      console.log('------------------------------------------------------');
      console.log('开始请求父母端积分记录');
      console.log(`日期范围: ${dateRange.startDate} 到 ${dateRange.endDate} (${diffDays}天)`);
      
      // 如果天数在90天内，优先使用简化API
      if (diffDays <= 90) {
        console.log('使用简化API获取记录，天数:', diffDays);
        const response = await pointApi.getRecentPointRecords(diffDays);
        
        if (response && response.data) {
          console.log(`收到 ${response.data.length} 条记录`);
          if (response.data.length > 0) {
            console.log('记录示例:', response.data.slice(0, 3));
          }
          setRecords(response.data);
          calculateStats(response.data);
        } else {
          console.warn('返回的记录为空');
          setRecords([]);
          calculateStats([]);
        }
      } 
      // 否则使用原始API查询指定日期范围的记录
      else {
        console.log('使用原始API获取记录，日期范围过大');
        console.log(`请求参数: startTime=${startTime}, endTime=${endTime}`);
        
        const response = await pointApi.getPointRecords(startTime, endTime);
        console.log('积分记录API返回结果:', response);
        console.log('状态码:', response.status);
        console.log('响应类型:', typeof response.data);
        console.log('是否为数组:', Array.isArray(response.data));
        console.log('记录数量:', response.data ? response.data.length : 0);
        
        if (response.data && response.data.length > 0) {
          console.log('记录示例:', response.data.slice(0, 3));
          setRecords(response.data);
          calculateStats(response.data);
        } else {
          console.warn('返回的记录为空');
          setRecords([]);
          calculateStats([]);
        }
      }
      
      console.log('------------------------------------------------------');
    } catch (error) {
      console.error('获取积分记录失败:', error);
      setRecords([]);
      calculateStats([]);
    } finally {
      setLoading(false);
    }
  };

  // 计算统计信息
  const calculateStats = (data) => {
    const totalEarned = data
      .filter(record => record.pointChange > 0)
      .reduce((sum, record) => sum + record.pointChange, 0);
      
    const totalSpent = data
      .filter(record => record.pointChange < 0)
      .reduce((sum, record) => sum + Math.abs(record.pointChange), 0);
      
    setStats({
      totalEarned,
      totalSpent,
      net: totalEarned - totalSpent
    });
  };

  // 使用简化API获取最近N天的积分记录
  const fetchRecentPointRecords = async (days) => {
    setLoading(true);
    try {
      console.log(`正在获取最近 ${days} 天的积分记录...`);
      
      // 使用简化版API
      const response = await pointApi.getRecentPointRecords(days);
      console.log('最近积分记录API返回结果:', response);
      
      if (response && response.data) {
        console.log(`收到 ${response.data.length} 条记录`);
        setRecords(response.data);
        calculateStats(response.data);
        
        // 更新日期范围显示
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        setDateRange({
          startDate,
          endDate
        });
      } else {
        console.warn('返回的记录为空');
        setRecords([]);
        calculateStats([]);
      }
    } catch (error) {
      console.error('获取最近积分记录失败:', error);
      setRecords([]);
      calculateStats([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (e) => {
    setDateRange({
      ...dateRange,
      [e.target.name]: e.target.value
    });
  };

  // 快速设置时间范围
  const setQuickTimeRange = (days) => {
    fetchRecentPointRecords(days);
  };

  // 处理筛选变化
  const handleFilterChange = (e) => {
    setFilter(e.target.value);
  };

  // 过滤记录
  const filteredRecords = records.filter(record => {
    if (filter === 'EARNED') {
      return record.pointChange > 0;
    } else if (filter === 'SPENT') {
      return record.pointChange < 0;
    }
    return true;
  });

  // 输出调试信息
  useEffect(() => {
    console.log('------- 当前记录状态信息 -------');
    console.log('总记录数：', records.length);
    console.log('过滤后记录数：', filteredRecords.length);
    console.log('当前过滤条件：', filter);
    console.log('示例记录：', records.length > 0 ? records[0] : '无记录');
    console.log('--------------------------------');
  }, [records, filteredRecords, filter]);

  // 获取变动类型的中文描述
  const getChangeTypeLabel = (changeType) => {
    switch (changeType) {
      case 'TASK_COMPLETION': return '完成任务';
      case 'TASK_PENALTY': return '任务惩罚';
      case 'REWARD_EXCHANGE': return '兑换奖励';
      case 'MANUAL_ADJUST': return '手动调整';
      default: return changeType;
    }
  };

  // 获取所有积分记录（用于调试）
  const fetchAllRecords = async () => {
    setLoading(true);
    try {
      console.log('获取所有积分记录...');
      const response = await pointApi.getAllPointRecords();
      console.log('所有积分记录返回结果:', response);
      
      if (response && response.data) {
        // 判断返回的数据是对象还是数组
        let recordsData;
        if (Array.isArray(response.data)) {
          // 直接返回的数组
          recordsData = response.data;
          console.log(`获取到 ${recordsData.length} 条记录(数组格式)`);
        } else if (response.data.records && Array.isArray(response.data.records)) {
          // 返回的是包含records字段的对象
          recordsData = response.data.records;
          console.log(`获取到 ${recordsData.length} 条记录(对象格式)`);
        } else {
          // 不支持的格式
          recordsData = [];
          console.warn('未能识别的数据格式:', response.data);
        }
        
        console.log('获取的记录示例:', recordsData.slice(0, 3));
        
        setRecords(recordsData);
        calculateStats(recordsData);
      } else {
        console.warn('未获取到任何积分记录');
        setRecords([]);
        calculateStats([]);
      }
    } catch (error) {
      console.error('获取所有积分记录失败:', error);
      setRecords([]);
      calculateStats([]);
    } finally {
      setLoading(false);
    }
  };

  // 生成测试数据（用于调试）
  const createTestData = async () => {
    try {
      console.log('正在生成测试数据...');
      const response = await pointApi.createTestData();
      console.log('测试数据生成结果:', response);
      // 生成后立即刷新数据
      fetchPointRecords();
      return response.data.message || '成功生成测试数据';
    } catch (error) {
      console.error('生成测试数据失败:', error);
      return '生成测试数据失败';
    }
  };

  // 生成历史数据（用于调试）
  const createHistoricalData = async () => {
    try {
      console.log('正在生成历史积分数据...');
      const response = await pointApi.createHistoricalData();
      console.log('历史数据生成结果:', response);
      // 生成后立即刷新数据
      fetchRecentPointRecords(30);
      return response.data.message || '成功生成历史积分数据';
    } catch (error) {
      console.error('生成历史数据失败:', error);
      return '生成历史数据失败';
    }
  };

  // 修复积分记录日期（用于调试）
  const fixPointDates = async () => {
    try {
      console.log('正在修复积分记录日期...');
      const response = await pointApi.fixPointRecordDates();
      console.log('修复积分记录日期结果:', response);
      // 修复后立即刷新数据
      fetchRecentPointRecords(30);
      return response.data.message || '成功修复积分记录日期';
    } catch (error) {
      console.error('修复积分记录日期失败:', error);
      return '修复积分记录日期失败';
    }
  };

  // 诊断服务器连接（用于调试）
  const diagnoseServerConnection = async () => {
    try {
      console.log('===== 开始系统诊断 =====');
      console.log('1. 检查API服务器可用性...');
      
      // 尝试通过fetch直接请求API
      const apiUrl = `${getParentApiBaseUrl()}/points/total`;
      console.log(`请求URL: ${apiUrl}`);
      
      try {
        const response = await fetch(apiUrl);
        console.log('API服务器响应状态:', response.status);
        if (response.ok) {
          const data = await response.json();
          console.log('API服务器响应数据:', data);
          alert('API服务器连接正常, 状态码: ' + response.status);
        } else {
          console.error('API服务器响应错误状态码:', response.status);
          alert('API服务器连接异常, 状态码: ' + response.status);
        }
      } catch (fetchError) {
        console.error('API服务器连接失败:', fetchError);
        alert('API服务器连接失败: ' + fetchError.message);
        return '诊断失败，API服务器无法连接';
      }
      
      console.log('2. 尝试使用系统诊断API进行深度检查...');
      try {
        const diagResponse = await pointApi.diagnoseSystem();
        console.log('系统诊断返回结果:', diagResponse.data);
        
        if (diagResponse.data.databaseConnected) {
          console.log('数据库连接状态: 正常');
          console.log('记录数量:', diagResponse.data.recordCount);
          
          if (diagResponse.data.sampleRecord) {
            console.log('样本记录:', diagResponse.data.sampleRecord);
            alert(`系统诊断完成：\n- 数据库连接正常\n- 总记录数: ${diagResponse.data.recordCount}\n- 有样本记录可用`);
          } else {
            alert(`系统诊断完成：\n- 数据库连接正常\n- 总记录数: ${diagResponse.data.recordCount}\n- 无样本记录`);
          }
        } else {
          console.error('数据库连接异常:', diagResponse.data.databaseError);
          alert(`系统诊断发现问题：\n数据库连接失败\n${diagResponse.data.databaseError}`);
        }
      } catch (diagError) {
        console.error('系统诊断API调用失败:', diagError);
        alert('系统诊断失败: ' + diagError.message);
      }
      
      console.log('===== 诊断完成 =====');
      return '诊断完成，请查看控制台日志';
    } catch (error) {
      console.error('诊断过程中发生错误:', error);
      return '诊断失败: ' + error.message;
    }
  };

  // 组件加载时自动获取数据
  useEffect(() => {
    console.log('父母端积分历史组件加载，自动获取最近7天记录...');
    
    // 首先尝试修复日期问题
    fixPointDates()
      .then(() => {
        // 修复后使用新接口获取最近7天数据
        fetchRecentPointRecords(7);
      })
      .catch(() => {
        // 出错时也尝试获取记录
        fetchRecentPointRecords(7);
      });
  }, []);
  
  // 监听日期范围变化重新获取数据
  useEffect(() => {
    console.log(`日期范围变更为 ${dateRange.startDate} 到 ${dateRange.endDate}, 重新获取数据...`);
    fetchPointRecords();
  }, [dateRange.startDate, dateRange.endDate]);

  return (
    <PageContainer>
      <Header>
        <Title>积分历史记录</Title>
        <Controls>
          <RefreshButton 
            onClick={() => fetchRecentPointRecords(30)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <RefreshIcon>🔄</RefreshIcon>
            刷新
          </RefreshButton>
        </Controls>
      </Header>

      <StatCardsContainer>
        <StatCard positive>
          <StatValue>+{stats.totalEarned}</StatValue>
          <StatLabel>获得积分</StatLabel>
        </StatCard>
        <StatCard negative>
          <StatValue>-{stats.totalSpent}</StatValue>
          <StatLabel>消费积分</StatLabel>
        </StatCard>
        <StatCard net={stats.net >= 0}>
          <StatValue>{stats.net >= 0 ? '+' : ''}{stats.net}</StatValue>
          <StatLabel>净变化</StatLabel>
        </StatCard>
      </StatCardsContainer>
      
      <QuickFilterContainer>
        <QuickFilterTitle>快速筛选:</QuickFilterTitle>
        <QuickFilterButtons>
          <QuickFilterButton onClick={() => setQuickTimeRange(7)}>最近7天</QuickFilterButton>
          <QuickFilterButton onClick={() => setQuickTimeRange(30)}>最近30天</QuickFilterButton>
          <QuickFilterButton onClick={() => setQuickTimeRange(90)}>最近90天</QuickFilterButton>
        </QuickFilterButtons>
      </QuickFilterContainer>

      <FilterContainer>
        <DateRangeSelector>
          <DateInput
            type="date"
            name="startDate"
            value={dateRange.startDate}
            onChange={handleDateRangeChange}
          />
          <DateRangeSeparator>至</DateRangeSeparator>
          <DateInput
            type="date"
            name="endDate"
            value={dateRange.endDate}
            onChange={handleDateRangeChange}
          />
        </DateRangeSelector>
        
        <FilterSelector>
          <FilterLabel>筛选:</FilterLabel>
          <Select value={filter} onChange={handleFilterChange}>
            <option value="ALL">全部记录</option>
            <option value="EARNED">获得积分</option>
            <option value="SPENT">消费积分</option>
          </Select>
        </FilterSelector>
      </FilterContainer>

      {loading ? (
        <LoadingMessage>加载中...</LoadingMessage>
      ) : (
        <RecordsContainer>
          {filteredRecords.length > 0 ? (
            <RecordsList>
              {filteredRecords.map(record => (
                <RecordItem
                  key={record.id}
                  positive={record.pointChange > 0}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <RecordMainInfo>
                    <RecordType>
                      <TypeLabel>{getChangeTypeLabel(record.changeType)}</TypeLabel>
                    </RecordType>
                    <RecordDescription>{record.description}</RecordDescription>
                  </RecordMainInfo>
                  
                  <RecordDetails>
                    <RecordTime>
                      {(() => {
                        try {
                          return format(new Date(record.recordTime), 'yyyy-MM-dd HH:mm:ss');
                        } catch (error) {
                          console.error('时间格式化错误:', error, record.recordTime);
                          return '时间格式错误';
                        }
                      })()}
                    </RecordTime>
                    <RecordPoints positive={record.pointChange > 0}>
                      {record.pointChange > 0 ? '+' : ''}{record.pointChange}
                    </RecordPoints>
                  </RecordDetails>
                </RecordItem>
              ))}
            </RecordsList>
          ) : (
            <NoRecordsMessage>
              <NoRecordsIcon>📊</NoRecordsIcon>
              <NoRecordsText>所选时间范围内没有积分记录</NoRecordsText>
              
              {/* 添加调试按钮 */}
              <DebugButtonsContainer>
                <DebugButton onClick={fetchAllRecords}>
                  显示所有记录
                </DebugButton>
                <DebugButton onClick={createTestData}>
                  生成测试数据
                </DebugButton>
                <DebugButton onClick={createHistoricalData}>
                  生成历史数据
                </DebugButton>
                <DebugButton onClick={fixPointDates}>
                  修复积分记录日期
                </DebugButton>
                <DebugButton onClick={diagnoseServerConnection}>
                  诊断服务器连接
                </DebugButton>
              </DebugButtonsContainer>
            </NoRecordsMessage>
          )}
        </RecordsContainer>
      )}
    </PageContainer>
  );
};

// 样式组件
const PageContainer = styled.div`
  padding: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: ${parentTheme.textColor};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: 1rem;
`;

const RefreshButton = styled(motion.button)`
  display: flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-weight: 600;
  cursor: pointer;
`;

const RefreshIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
  line-height: 1;
`;

const StatCardsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const StatCard = styled.div.withConfig({
  shouldForwardProp: (prop) => !['positive', 'negative', 'net'].includes(prop),
})`
  background-color: white;
  padding: 1.2rem;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    width: 100%;
    background-color: ${props =>
      props.positive ? parentTheme.statusColors.COMPLETED :
      props.negative ? parentTheme.statusColors.REJECTED :
      props.net ? parentTheme.statusColors.COMPLETED : parentTheme.statusColors.REJECTED};
  }
`;

const StatValue = styled.div`
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const QuickFilterContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const QuickFilterTitle = styled.div`
  font-size: 0.9rem;
  color: #666;
`;

const QuickFilterButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const QuickFilterButton = styled.button`
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 0.5rem;
  background: #f5f5f5;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  
  &:hover {
    background: #eeeeee;
  }
`;

const FilterContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const DateRangeSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const DateInput = styled.input`
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
`;

const DateRangeSeparator = styled.span`
  font-size: 0.9rem;
  color: #666;
`;

const FilterSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FilterLabel = styled.label`
  font-size: 0.9rem;
  color: #666;
`;

const Select = styled.select`
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
  background-color: white;
`;

const RecordsContainer = styled.div`
  background-color: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const RecordsList = styled.div`
  max-height: 600px;
  overflow-y: auto;
`;

const RecordItem = styled(motion.div).withConfig({
  shouldForwardProp: (prop) => prop !== 'positive',
})`
  display: flex;
  flex-direction: column;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  position: relative;

  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background-color: ${props => props.positive ?
      parentTheme.statusColors.COMPLETED :
      parentTheme.statusColors.REJECTED};
    opacity: 0.7;
  }
`;

const RecordMainInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
`;

const RecordType = styled.div`
  min-width: 80px;
`;

const TypeLabel = styled.span`
  display: inline-block;
  padding: 0.3rem 0.6rem;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: #f5f5f5;
  border-radius: 0.3rem;
  color: #666;
`;

const RecordDescription = styled.div`
  font-size: 1rem;
  font-weight: 500;
  color: ${parentTheme.textColor};
`;

const RecordDetails = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.3rem;
`;

const RecordTime = styled.div`
  font-size: 0.8rem;
  color: #999;
`;

const RecordPoints = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'positive',
})`
  font-size: 1.2rem;
  font-weight: 700;
  color: ${props => props.positive ?
    parentTheme.statusColors.COMPLETED :
    parentTheme.statusColors.REJECTED};
`;

const LoadingMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #666;
`;

const NoRecordsMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
`;

const NoRecordsIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const NoRecordsText = styled.div`
  font-size: 1.2rem;
  color: #666;
`;

const DebugButtonsContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
`;

const DebugButton = styled.button`
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 0.5rem;
  background: #f5f5f5;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  
  &:hover {
    background: #eeeeee;
  }
`;

export default PointHistory; 