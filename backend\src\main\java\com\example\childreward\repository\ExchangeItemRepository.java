package com.example.childreward.repository;

import com.example.childreward.entity.ExchangeItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 兑换商品Repository
 */
@Repository
public interface ExchangeItemRepository extends JpaRepository<ExchangeItem, Long> {
    
    /**
     * 查找所有启用的兑换商品，按排序顺序和创建时间排序
     */
    List<ExchangeItem> findByIsActiveTrueOrderBySortOrderAscCreatedTimeDesc();
    
    /**
     * 按分类查找启用的兑换商品
     */
    List<ExchangeItem> findByIsActiveTrueAndCategoryOrderBySortOrderAscCreatedTimeDesc(String category);
    
    /**
     * 查找所有兑换商品，按排序顺序和创建时间排序
     */
    List<ExchangeItem> findAllByOrderBySortOrderAscCreatedTimeDesc();
    
    /**
     * 查找有库存的启用商品
     */
    @Query("SELECT e FROM ExchangeItem e WHERE e.isActive = true AND (e.stock IS NULL OR e.stock = -1 OR e.stock > 0) ORDER BY e.sortOrder ASC, e.createdTime DESC")
    List<ExchangeItem> findAvailableItems();
    
    /**
     * 获取所有商品分类
     */
    @Query("SELECT DISTINCT e.category FROM ExchangeItem e WHERE e.category IS NOT NULL AND e.isActive = true ORDER BY e.category")
    List<String> findDistinctCategories();
}
