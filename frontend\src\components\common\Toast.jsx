import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';

const ToastContainer = styled(motion.div)`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: ${props => {
    switch (props.type) {
      case 'success': return AppleDesignSystem.colors.semantic.systemGreen;
      case 'error': return AppleDesignSystem.colors.semantic.systemRed;
      case 'warning': return AppleDesignSystem.colors.semantic.systemOrange;
      default: return AppleDesignSystem.colors.semantic.systemBlue;
    }
  }};
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  font-size: 16px;
  font-weight: 500;
  max-width: 90vw;
  word-break: break-word;
  
  @media (max-width: 768px) {
    top: 10px;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 10px;
  }
`;

const ToastIcon = styled.span`
  font-size: 20px;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    font-size: 18px;
  }
`;

const ToastMessage = styled.span`
  flex: 1;
  line-height: 1.4;
`;

// Toast管理器
class ToastManager {
  constructor() {
    this.toasts = [];
    this.listeners = [];
  }

  show(message, type = 'info', duration = 3000) {
    const id = Date.now() + Math.random();
    const toast = { id, message, type, duration };
    
    this.toasts.push(toast);
    this.notifyListeners();
    
    // 自动移除
    setTimeout(() => {
      this.remove(id);
    }, duration);
    
    return id;
  }

  remove(id) {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
    this.notifyListeners();
  }

  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  notifyListeners() {
    this.listeners.forEach(listener => listener(this.toasts));
  }
}

export const toastManager = new ToastManager();

// Toast组件
const Toast = ({ toast, onRemove }) => {
  const getIcon = (type) => {
    switch (type) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <ToastContainer
      type={toast.type}
      initial={{ opacity: 0, y: -50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.9 }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      onClick={() => onRemove(toast.id)}
    >
      <ToastIcon>{getIcon(toast.type)}</ToastIcon>
      <ToastMessage>{toast.message}</ToastMessage>
    </ToastContainer>
  );
};

// Toast容器组件
export const ToastProvider = () => {
  const [toasts, setToasts] = useState([]);

  useEffect(() => {
    const unsubscribe = toastManager.subscribe(setToasts);
    return unsubscribe;
  }, []);

  return (
    <AnimatePresence mode="sync">
      {toasts.map((toast, index) => (
        <motion.div
          key={toast.id}
          style={{
            position: 'fixed',
            top: 20 + index * 70,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 10000 - index,
          }}
        >
          <Toast
            toast={toast}
            onRemove={toastManager.remove.bind(toastManager)}
          />
        </motion.div>
      ))}
    </AnimatePresence>
  );
};

// 便捷方法
export const toast = {
  success: (message, duration) => toastManager.show(message, 'success', duration),
  error: (message, duration) => toastManager.show(message, 'error', duration),
  warning: (message, duration) => toastManager.show(message, 'warning', duration),
  info: (message, duration) => toastManager.show(message, 'info', duration),
};

export default Toast;
