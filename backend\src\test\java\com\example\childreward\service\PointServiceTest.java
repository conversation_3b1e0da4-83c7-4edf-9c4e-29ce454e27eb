package com.example.childreward.service;

import com.example.childreward.entity.PointBalance;
import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.PointRecord.ChangeType;
import com.example.childreward.repository.PointBalanceRepository;
import com.example.childreward.repository.PointRecordRepository;
import com.example.childreward.service.impl.PointServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PointServiceTest {

    @Mock
    private PointRecordRepository pointRecordRepository;

    @Mock
    private PointBalanceRepository pointBalanceRepository;

    @InjectMocks
    private PointServiceImpl pointService;

    @Captor
    private ArgumentCaptor<PointRecord> pointRecordCaptor;

    @Captor
    private ArgumentCaptor<PointBalance> pointBalanceCaptor;

    private PointBalance testBalance;
    private PointRecord testRecord;

    @BeforeEach
    void setUp() {
        testBalance = PointBalance.builder()
                .id(1L)
                .totalPoints(100)
                .updateTime(LocalDateTime.now())
                .build();

        testRecord = PointRecord.builder()
                .id(1L)
                .pointChange(10)
                .changeType(ChangeType.TASK_COMPLETION)
                .description("完成任务")
                .recordTime(LocalDateTime.now())
                .relatedTaskId(1L)
                .build();
    }

    @Test
    void getTotalPoints_shouldReturnCurrentBalance() {
        // Given
        when(pointBalanceRepository.getCurrentBalance()).thenReturn(testBalance);

        // When
        Integer result = pointService.getTotalPoints();

        // Then
        assertEquals(100, result);
    }

    @Test
    void addPoints_shouldCreatePositiveRecordAndUpdateBalance() {
        // Given
        when(pointBalanceRepository.getCurrentBalance()).thenReturn(testBalance);
        when(pointRecordRepository.save(any(PointRecord.class))).thenAnswer(i -> i.getArguments()[0]);
        when(pointBalanceRepository.save(any(PointBalance.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        PointRecord result = pointService.addPoints(20, ChangeType.TASK_COMPLETION, "完成任务奖励", 1L);

        // Then
        assertEquals(20, result.getPointChange());
        assertEquals(ChangeType.TASK_COMPLETION, result.getChangeType());
        assertEquals("完成任务奖励", result.getDescription());
        assertEquals(1L, result.getRelatedTaskId());

        verify(pointRecordRepository).save(pointRecordCaptor.capture());
        verify(pointBalanceRepository).save(pointBalanceCaptor.capture());

        assertEquals(20, pointRecordCaptor.getValue().getPointChange());
        assertEquals(120, pointBalanceCaptor.getValue().getTotalPoints());
    }

    @Test
    void addPoints_shouldThrowExceptionWhenPointsIsNegative() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> pointService.addPoints(-10, ChangeType.TASK_COMPLETION, "测试", null));
    }

    @Test
    void deductPoints_shouldCreateNegativeRecordAndUpdateBalance() {
        // Given
        when(pointBalanceRepository.getCurrentBalance()).thenReturn(testBalance);
        when(pointRecordRepository.save(any(PointRecord.class))).thenAnswer(i -> i.getArguments()[0]);
        when(pointBalanceRepository.save(any(PointBalance.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        PointRecord result = pointService.deductPoints(30, ChangeType.REWARD_EXCHANGE, "兑换奖励", 2L);

        // Then
        assertEquals(-30, result.getPointChange());
        assertEquals(ChangeType.REWARD_EXCHANGE, result.getChangeType());
        assertEquals("兑换奖励", result.getDescription());
        assertEquals(2L, result.getRelatedRewardId());

        verify(pointRecordRepository).save(pointRecordCaptor.capture());
        verify(pointBalanceRepository).save(pointBalanceCaptor.capture());

        assertEquals(-30, pointRecordCaptor.getValue().getPointChange());
        assertEquals(70, pointBalanceCaptor.getValue().getTotalPoints());
    }

    @Test
    void deductPoints_shouldThrowExceptionWhenPointsIsNegative() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> pointService.deductPoints(-10, ChangeType.REWARD_EXCHANGE, "测试", null));
    }

    @Test
    void getPointRecords_shouldReturnRecordsInTimeRange() {
        // Given
        LocalDateTime start = LocalDateTime.now().minusDays(1);
        LocalDateTime end = LocalDateTime.now();
        List<PointRecord> expectedRecords = Arrays.asList(testRecord);

        when(pointRecordRepository.findByRecordTimeBetweenOrderByRecordTimeDesc(start, end)).thenReturn(expectedRecords);

        // When
        List<PointRecord> result = pointService.getPointRecords(start, end);

        // Then
        assertEquals(expectedRecords, result);
    }

    @Test
    void getTodayPointsChange_shouldCalculateSumOfTodayRecords() {
        // Given
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        PointRecord record1 = PointRecord.builder().pointChange(10).build();
        PointRecord record2 = PointRecord.builder().pointChange(-5).build();
        List<PointRecord> todayRecords = Arrays.asList(record1, record2);

        when(pointRecordRepository.findByRecordTimeBetweenOrderByRecordTimeDesc(startOfDay, endOfDay)).thenReturn(todayRecords);

        // When
        Integer result = pointService.getTodayPointsChange();

        // Then
        assertEquals(5, result);
    }

    @Test
    void getPointsChangeByDate_shouldCalculateSumOfDateRecords() {
        // Given
        LocalDate date = LocalDate.now().minusDays(1);
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

        PointRecord record1 = PointRecord.builder().pointChange(20).build();
        PointRecord record2 = PointRecord.builder().pointChange(-8).build();
        List<PointRecord> dateRecords = Arrays.asList(record1, record2);

        when(pointRecordRepository.findByRecordTimeBetweenOrderByRecordTimeDesc(startOfDay, endOfDay)).thenReturn(dateRecords);

        // When
        Integer result = pointService.getPointsChangeByDate(date);

        // Then
        assertEquals(12, result);
    }

    @Test
    void getAllRecords_shouldReturnAllRecords() {
        // Given
        List<PointRecord> allRecords = Arrays.asList(testRecord);
        when(pointRecordRepository.findAll()).thenReturn(allRecords);

        // When
        List<PointRecord> result = pointService.getAllRecords();

        // Then
        assertEquals(allRecords, result);
    }

    @Test
    void saveRecord_shouldSaveAndReturnRecord() {
        // Given
        when(pointRecordRepository.save(any(PointRecord.class))).thenReturn(testRecord);

        // When
        PointRecord result = pointService.saveRecord(testRecord);

        // Then
        assertEquals(testRecord, result);
        verify(pointRecordRepository).save(testRecord);
    }
} 