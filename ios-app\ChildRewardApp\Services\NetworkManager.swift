import Foundation
import Combine

// 日期格式化器扩展
extension DateFormatter {
    static let logFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
}

// 网络管理器
class NetworkManager: ObservableObject {
    static let shared = NetworkManager()
    
    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = APIConfig.timeoutInterval
        config.timeoutIntervalForResource = APIConfig.timeoutInterval * 2
        // 优化网络配置
        config.waitsForConnectivity = true // 等待网络连接，提高成功率
        config.allowsCellularAccess = true
        config.requestCachePolicy = .returnCacheDataElseLoad // 优先使用缓存，网络失败时使用缓存数据
        config.urlCache = URLCache(memoryCapacity: 10 * 1024 * 1024, diskCapacity: 50 * 1024 * 1024) // 10MB内存缓存，50MB磁盘缓存
        self.session = URLSession(configuration: config)
    }
    
    // 通用请求方法（带重试机制）
    func request<T: Codable>(
        endpoint: APIEndpoint,
        responseType: T.Type,
        body: Data? = nil,
        retryCount: Int = 2
    ) -> AnyPublisher<T, APIError> {
        
        guard let url = buildURL(for: endpoint) else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.method.rawValue
        request.allHTTPHeaderFields = APIConfig.defaultHeaders

        // 🔥 关键修复：强制禁用缓存，确保每次都获取最新数据
        request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData

        if let body = body {
            request.httpBody = body
        }
        
        // 添加详细调试日志
        let requestId = UUID().uuidString.prefix(8)
        let startTime = Date()
        print("🚀 [请求\(requestId)] NetworkManager: API请求开始")
        print("🌐 [请求\(requestId)] 方法: \(endpoint.method.rawValue)")
        print("🌐 [请求\(requestId)] URL: \(url)")
        print("🌐 [请求\(requestId)] 端点: \(endpoint)")
        print("⏰ [请求\(requestId)] 开始时间: \(DateFormatter.logFormatter.string(from: startTime))")
        print("⚙️ [请求\(requestId)] 超时设置: \(APIConfig.timeoutInterval)秒")
        if let body = body, let bodyString = String(data: body, encoding: .utf8) {
            print("📤 [请求\(requestId)] 请求体: \(bodyString)")
        }
        
        return session.dataTaskPublisher(for: request)
            .tryMap { data, response -> Data in
                let endTime = Date()
                let duration = endTime.timeIntervalSince(startTime)

                // 添加详细响应日志
                print("📥 [请求\(requestId)] NetworkManager: 收到响应")
                print("⏰ [请求\(requestId)] 响应时间: \(DateFormatter.logFormatter.string(from: endTime))")
                print("⏱️ [请求\(requestId)] 请求耗时: \(String(format: "%.3f", duration))秒")

                if let httpResponse = response as? HTTPURLResponse {
                    print("📊 [请求\(requestId)] 状态码: \(httpResponse.statusCode)")
                    print("📊 [请求\(requestId)] 数据大小: \(data.count) bytes")
                    print("📊 [请求\(requestId)] 响应头: \(httpResponse.allHeaderFields)")

                    if let responseString = String(data: data, encoding: .utf8) {
                        let preview = responseString.count > 500 ? String(responseString.prefix(500)) + "..." : responseString
                        print("📥 [请求\(requestId)] 响应内容: \(preview)")
                    }
                } else {
                    print("❌ [请求\(requestId)] 响应不是HTTP响应")
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw APIError.unknown
                }
                
                switch httpResponse.statusCode {
                case 200...299:
                    print("✅ [请求\(requestId)] 请求成功")
                    return data
                case 401:
                    print("❌ [请求\(requestId)] 未授权访问 (401)")
                    throw APIError.unauthorized
                case 403:
                    print("❌ [请求\(requestId)] 访问被禁止 (403)")
                    throw APIError.forbidden
                case 404:
                    print("❌ [请求\(requestId)] 资源未找到 (404)")
                    throw APIError.notFound
                case 408:
                    print("❌ [请求\(requestId)] 请求超时 (408)")
                    throw APIError.timeout
                case 400...499:
                    print("❌ [请求\(requestId)] 客户端错误 (\(httpResponse.statusCode))")
                    throw APIError.serverError(httpResponse.statusCode)
                case 500...599:
                    print("❌ [请求\(requestId)] 服务器错误 (\(httpResponse.statusCode))")
                    throw APIError.serverError(httpResponse.statusCode)
                default:
                    print("❌ [请求\(requestId)] 未知状态码 (\(httpResponse.statusCode))")
                    throw APIError.unknown
                }
            }
            .decode(type: T.self, decoder: JSONDecoder())
            .mapError { error in
                let endTime = Date()
                let duration = endTime.timeIntervalSince(startTime)

                if let apiError = error as? APIError {
                    print("❌ [请求\(requestId)] API错误: \(apiError.localizedDescription)")
                    return apiError
                } else if error is DecodingError {
                    print("❌ [请求\(requestId)] 解码错误: \(error)")
                    print("⏱️ [请求\(requestId)] 失败耗时: \(String(format: "%.3f", duration))秒")
                    return APIError.decodingError(error)
                } else if let urlError = error as? URLError {
                    print("❌ [请求\(requestId)] URL错误: \(urlError.localizedDescription)")
                    print("❌ [请求\(requestId)] 错误代码: \(urlError.code.rawValue)")
                    print("⏱️ [请求\(requestId)] 失败耗时: \(String(format: "%.3f", duration))秒")

                    // 详细分析URL错误类型
                    switch urlError.code {
                    case .timedOut:
                        print("⏰ [请求\(requestId)] 具体错误: 请求超时")
                    case .notConnectedToInternet:
                        print("🌐 [请求\(requestId)] 具体错误: 无网络连接")
                    case .networkConnectionLost:
                        print("📡 [请求\(requestId)] 具体错误: 网络连接丢失")
                    case .cannotConnectToHost:
                        print("🔌 [请求\(requestId)] 具体错误: 无法连接到主机")
                    case .cannotFindHost:
                        print("🔍 [请求\(requestId)] 具体错误: 找不到主机")
                    default:
                        print("❓ [请求\(requestId)] 具体错误: \(urlError.localizedDescription)")
                    }

                    return APIError.networkError(error)
                } else {
                    print("❌ [请求\(requestId)] 未知错误: \(error)")
                    print("⏱️ [请求\(requestId)] 失败耗时: \(String(format: "%.3f", duration))秒")
                    return APIError.networkError(error)
                }
            }
            .retry(retryCount) // 添加重试机制
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    // 构建URL
    private func buildURL(for endpoint: APIEndpoint) -> URL? {
        let fullPath = APIConfig.baseURL + endpoint.path
        print("🌐 NetworkManager: 构建URL: \(fullPath)")
        return URL(string: fullPath)
    }

    // 网络连接测试
    func testConnection() -> AnyPublisher<Bool, Never> {
        let testId = UUID().uuidString.prefix(8)
        print("🔍 [测试\(testId)] 开始网络连接测试")
        print("🌐 [测试\(testId)] 测试URL: \(APIConfig.baseURL)")

        guard let url = URL(string: APIConfig.baseURL + "/tasks/today") else {
            print("❌ [测试\(testId)] 无效的测试URL")
            return Just(false).eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.allHTTPHeaderFields = APIConfig.defaultHeaders
        request.timeoutInterval = 5.0 // 短超时用于快速测试

        return session.dataTaskPublisher(for: request)
            .map { data, response in
                if let httpResponse = response as? HTTPURLResponse {
                    let success = (200...299).contains(httpResponse.statusCode)
                    print("🔍 [测试\(testId)] 连接测试结果: \(success ? "成功" : "失败") (状态码: \(httpResponse.statusCode))")
                    return success
                } else {
                    print("❌ [测试\(testId)] 无效的HTTP响应")
                    return false
                }
            }
            .catch { error in
                print("❌ [测试\(testId)] 连接测试失败: \(error)")
                return Just(false)
            }
            .eraseToAnyPublisher()
    }

    // 解析错误消息
    private func parseErrorMessage(from data: Data) -> String? {
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let message = json["message"] as? String {
                return message
            }
        } catch {
            // 忽略解析错误，返回nil
        }
        return nil
    }
    
    // 编码请求体
    func encodeBody<T: Codable>(_ object: T) -> Data? {
        do {
            return try JSONEncoder().encode(object)
        } catch {
            print("❌ 编码错误: \(error)")
            return nil
        }
    }
}

// 网络状态监控
class NetworkMonitor: ObservableObject {
    @Published var isConnected = true
    
    // 这里可以添加网络状态监控逻辑
    // 例如使用 Network framework 来监控网络连接状态
}
