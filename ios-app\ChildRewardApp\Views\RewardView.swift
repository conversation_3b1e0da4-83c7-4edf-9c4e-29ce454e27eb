import SwiftUI

// MARK: - 奖励系统视图
struct RewardView: View {
    @StateObject private var rewardViewModel = RewardViewModel()
    @State private var selectedReward: RewardItem?
    @State private var showRedeemAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 积分卡片
                    pointsCard
                    
                    // 奖励商店
                    rewardStore
                    
                    // 兑换历史
                    redeemHistory
                }
                .padding()
            }
            .navigationTitle("奖励商店")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await rewardViewModel.loadRewards()
            }
        }
        .alert("兑换奖励", isPresented: $showRedeemAlert) {
            Button("取消", role: .cancel) { }
            But<PERSON>("确认兑换") {
                if let reward = selectedReward {
                    Task {
                        await rewardViewModel.redeemReward(reward)
                    }
                }
            }
        } message: {
            if let reward = selectedReward {
                Text("确定要用 \(reward.pointsCost) 积分兑换「\(reward.name)」吗？")
            }
        }
        .onAppear {
            Task {
                await rewardViewModel.loadRewards()
            }
        }
    }
    
    // MARK: - 积分卡片
    private var pointsCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("我的积分")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("\(rewardViewModel.totalPoints)")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    Text("今日获得")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("+\(rewardViewModel.todayEarned)")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
            
            // 积分趋势图（简化版）
            HStack(spacing: 4) {
                ForEach(0..<7, id: \.self) { index in
                    Rectangle()
                        .fill(Color.blue.opacity(0.3 + Double(index) * 0.1))
                        .frame(width: 8, height: CGFloat.random(in: 20...40))
                        .cornerRadius(4)
                }
            }
            .frame(height: 50)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }
    
    // MARK: - 奖励商店
    private var rewardStore: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("奖励商店")
                .font(.title2)
                .fontWeight(.bold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(rewardViewModel.availableRewards) { reward in
                    RewardItemCard(
                        reward: reward,
                        canAfford: rewardViewModel.totalPoints >= reward.pointsCost
                    ) {
                        selectedReward = reward
                        showRedeemAlert = true
                    }
                }
            }
        }
    }
    
    // MARK: - 兑换历史
    private var redeemHistory: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("兑换历史")
                .font(.title2)
                .fontWeight(.bold)
            
            if rewardViewModel.redeemHistory.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "gift.circle")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("还没有兑换记录")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("完成任务获得积分，就可以兑换奖励啦！")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(40)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(rewardViewModel.redeemHistory) { record in
                        RedeemHistoryRow(record: record)
                    }
                }
            }
        }
    }
}

// MARK: - 奖励项目卡片
struct RewardItemCard: View {
    let reward: RewardItem
    let canAfford: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 奖励图标
                ZStack {
                    Circle()
                        .fill(reward.color.opacity(0.2))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: reward.iconName)
                        .font(.system(size: 24))
                        .foregroundColor(reward.color)
                }
                
                // 奖励信息
                VStack(spacing: 4) {
                    Text(reward.name)
                        .font(.headline)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text("\(reward.pointsCost) 积分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 兑换按钮
                Text(canAfford ? "兑换" : "积分不足")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(canAfford ? .white : .secondary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(canAfford ? reward.color : Color.gray.opacity(0.3))
                    )
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
        }
        .disabled(!canAfford)
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 兑换历史行
struct RedeemHistoryRow: View {
    let record: RedeemRecord
    
    var body: some View {
        HStack(spacing: 12) {
            // 奖励图标
            ZStack {
                Circle()
                    .fill(record.reward.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: record.reward.iconName)
                    .font(.system(size: 16))
                    .foregroundColor(record.reward.color)
            }
            
            // 兑换信息
            VStack(alignment: .leading, spacing: 2) {
                Text(record.reward.name)
                    .font(.headline)
                
                Text(record.formattedDate)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 积分消耗
            Text("-\(record.reward.pointsCost)")
                .font(.headline)
                .foregroundColor(.red)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

// MARK: - 预览
struct RewardView_Previews: PreviewProvider {
    static var previews: some View {
        RewardView()
            .preferredColorScheme(.light)
    }
}
