package com.example.childreward.service.impl;

import com.example.childreward.entity.ExchangeItem;
import com.example.childreward.entity.ExchangeRecord;
import com.example.childreward.entity.PointRecord;
import com.example.childreward.repository.ExchangeItemRepository;
import com.example.childreward.repository.ExchangeRecordRepository;
import com.example.childreward.service.ExchangeService;
import com.example.childreward.service.PointService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 兑换服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExchangeServiceImpl implements ExchangeService {
    
    private final ExchangeItemRepository exchangeItemRepository;
    private final ExchangeRecordRepository exchangeRecordRepository;
    private final PointService pointService;
    
    // ========== 兑换商品管理 ==========
    
    @Override
    public List<ExchangeItem> getAllExchangeItems() {
        return exchangeItemRepository.findAllByOrderBySortOrderAscCreatedTimeDesc();
    }
    
    @Override
    public List<ExchangeItem> getAvailableExchangeItems() {
        return exchangeItemRepository.findAvailableItems();
    }
    
    @Override
    public List<ExchangeItem> getAvailableExchangeItemsByCategory(String category) {
        return exchangeItemRepository.findByIsActiveTrueAndCategoryOrderBySortOrderAscCreatedTimeDesc(category);
    }
    
    @Override
    public List<String> getAllCategories() {
        return exchangeItemRepository.findDistinctCategories();
    }
    
    @Override
    public ExchangeItem getExchangeItemById(Long id) {
        return exchangeItemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("兑换商品不存在"));
    }
    
    @Override
    @Transactional
    public ExchangeItem createExchangeItem(ExchangeItem exchangeItem) {
        return exchangeItemRepository.save(exchangeItem);
    }
    
    @Override
    @Transactional
    public ExchangeItem updateExchangeItem(Long id, ExchangeItem exchangeItem) {
        ExchangeItem existingItem = getExchangeItemById(id);
        
        existingItem.setName(exchangeItem.getName());
        existingItem.setDescription(exchangeItem.getDescription());
        existingItem.setRequiredPoints(exchangeItem.getRequiredPoints());
        existingItem.setStock(exchangeItem.getStock());
        existingItem.setCategory(exchangeItem.getCategory());
        existingItem.setImageUrl(exchangeItem.getImageUrl());
        existingItem.setIsActive(exchangeItem.getIsActive());
        existingItem.setSortOrder(exchangeItem.getSortOrder());
        
        return exchangeItemRepository.save(existingItem);
    }
    
    @Override
    @Transactional
    public void deleteExchangeItem(Long id) {
        ExchangeItem item = getExchangeItemById(id);
        exchangeItemRepository.delete(item);
    }
    
    @Override
    @Transactional
    public ExchangeItem toggleExchangeItemStatus(Long id) {
        ExchangeItem item = getExchangeItemById(id);
        item.setIsActive(!item.getIsActive());
        return exchangeItemRepository.save(item);
    }
    
    // ========== 兑换操作 ==========
    
    @Override
    @Transactional
    public ExchangeRecord exchangeItem(Long exchangeItemId) {
        ExchangeItem item = getExchangeItemById(exchangeItemId);
        
        // 检查商品是否可用
        if (!item.getIsActive()) {
            throw new RuntimeException("该商品已下架");
        }
        
        if (!item.hasStock()) {
            throw new RuntimeException("该商品库存不足");
        }
        
        // 检查积分余额
        Integer currentPoints = pointService.getTotalPoints();
        if (currentPoints < item.getRequiredPoints()) {
            throw new RuntimeException("积分不足，当前积分：" + currentPoints + "，需要积分：" + item.getRequiredPoints());
        }

        // 扣除积分
        pointService.deductPoints(
            item.getRequiredPoints(),
            PointRecord.ChangeType.REWARD_EXCHANGE,
            "兑换商品：" + item.getName(),
            item.getId()
        );
        
        // 减少库存
        item.decreaseStock();
        exchangeItemRepository.save(item);
        
        // 创建兑换记录
        ExchangeRecord record = ExchangeRecord.builder()
                .exchangeItemId(item.getId())
                .itemName(item.getName())
                .pointsUsed(item.getRequiredPoints())
                .status(ExchangeRecord.ExchangeStatus.UNUSED)
                .build();
        
        // 设置过期时间（30天后过期）
        record.setExpireTime(LocalDateTime.now().plusDays(30));
        
        ExchangeRecord savedRecord = exchangeRecordRepository.save(record);
        
        log.info("用户兑换商品成功：{}, 消耗积分：{}", item.getName(), item.getRequiredPoints());
        
        return savedRecord;
    }
    
    @Override
    @Transactional
    public ExchangeRecord useExchangeRecord(Long recordId, String notes) {
        ExchangeRecord record = getExchangeRecordById(recordId);
        
        if (record.getStatus() != ExchangeRecord.ExchangeStatus.UNUSED) {
            throw new RuntimeException("该兑换记录已使用或已过期");
        }
        
        if (record.isExpired()) {
            record.setStatus(ExchangeRecord.ExchangeStatus.EXPIRED);
            exchangeRecordRepository.save(record);
            throw new RuntimeException("该兑换记录已过期");
        }
        
        record.markAsUsed();
        record.setNotes(notes);
        
        return exchangeRecordRepository.save(record);
    }
    
    // ========== 兑换记录查询 ==========
    
    @Override
    public List<ExchangeRecord> getAllExchangeRecords() {
        return exchangeRecordRepository.findAllByOrderByExchangeTimeDesc();
    }
    
    @Override
    public List<ExchangeRecord> getUnusedExchangeRecords() {
        return exchangeRecordRepository.findByStatusOrderByExchangeTimeAsc(ExchangeRecord.ExchangeStatus.UNUSED);
    }
    
    @Override
    public ExchangeRecord getExchangeRecordById(Long id) {
        return exchangeRecordRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("兑换记录不存在"));
    }
    
    @Override
    @Transactional
    public void processExpiredRecords() {
        List<ExchangeRecord> expiredRecords = exchangeRecordRepository.findExpiredRecords(LocalDateTime.now());
        
        for (ExchangeRecord record : expiredRecords) {
            record.setStatus(ExchangeRecord.ExchangeStatus.EXPIRED);
        }
        
        if (!expiredRecords.isEmpty()) {
            exchangeRecordRepository.saveAll(expiredRecords);
            log.info("处理过期兑换记录：{} 条", expiredRecords.size());
        }
    }
}
