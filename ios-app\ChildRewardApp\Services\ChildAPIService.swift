import Foundation
import Combine
import SwiftUI

// 儿童端API服务
class ChildAPIService: ObservableObject {
    static let shared = ChildAPIService()

    private let networkManager = NetworkManager.shared
    private let environmentManager = EnvironmentManager.shared
    private var cancellables = Set<AnyCancellable>()

    private init() {}

    // MARK: - 任务相关API

    // 获取今日任务
    func getTodayTasks() -> AnyPublisher<[TaskSummary], APIError> {
        print("📋 使用真实API获取任务数据")
        print("🌐 当前环境: \(environmentManager.currentEnvironment)")
        print("🌐 当前baseURL: \(environmentManager.currentBaseURL)")
        print("🌐 完整API URL: \(APIConfig.baseURL)")
        // 后端直接返回任务数组，不是包装的响应
        return networkManager.request(
            endpoint: .todayTasks,
            responseType: [TaskSummary].self
        )
        .map { tasks in
            print("📋 API响应: 获取到 \(tasks.count) 个任务")
            return tasks
        }
        .eraseToAnyPublisher()
    }

    // 获取昨日任务
    func getYesterdayTasks() -> AnyPublisher<[TaskSummary], APIError> {
        return networkManager.request(
            endpoint: .yesterdayTasks,
            responseType: [TaskSummary].self
        )
    }

    // 获取过期任务
    func getOverdueTasks() -> AnyPublisher<[TaskSummary], APIError> {
        return networkManager.request(
            endpoint: .overdueTasks,
            responseType: [TaskSummary].self
        )
    }

    // 获取待审核任务
    func getPendingTasks() -> AnyPublisher<[TaskSummary], APIError> {
        print("📋 使用真实API获取待审核任务数据")
        return networkManager.request(
            endpoint: .pendingTasks,
            responseType: [TaskSummary].self
        )
        .map { tasks in
            print("📋 API响应: 获取到 \(tasks.count) 个待审核任务")
            return tasks
        }
        .eraseToAnyPublisher()
    }

    // 获取任务详情
    func getTaskDetail(taskId: Int) -> AnyPublisher<TaskModel, APIError> {
        return networkManager.request(
            endpoint: .taskDetail(taskId),
            responseType: TaskModel.self
        )
    }

    // 开始任务
    func startTask(taskId: Int) -> AnyPublisher<TaskModel, APIError> {
        return networkManager.request(
            endpoint: .startTask(taskId),
            responseType: TaskModel.self
        )
    }

    // 完成任务
    func completeTask(taskId: Int) -> AnyPublisher<TaskModel, APIError> {
        return networkManager.request(
            endpoint: .completeTask(taskId),
            responseType: TaskModel.self
        )
    }

    // 根据日期获取任务
    func getTasksByDate(_ date: String) -> AnyPublisher<[TaskSummary], APIError> {
        return networkManager.request(
            endpoint: .tasksByDate(date),
            responseType: [TaskSummary].self
        )
    }

    // MARK: - 积分相关API

    // 获取积分余额
    func getPointBalance() -> AnyPublisher<PointBalance, APIError> {
        print("💰 使用真实API获取积分余额数据")
        return networkManager.request(
            endpoint: .pointBalance,
            responseType: PointBalance.self
        )
    }

    // 获取今日积分变化
    func getTodayPointChange() -> AnyPublisher<TodayPointChange, APIError> {
        print("📊 使用真实API获取今日积分变化数据")
        return networkManager.request(
            endpoint: .todayPointChange,
            responseType: TodayPointChange.self
        )
    }

    // 获取积分记录
    func getPointRecords() -> AnyPublisher<[PointRecord], APIError> {
        print("📊 使用真实API获取积分记录数据")
        return networkManager.request(
            endpoint: .pointRecords,
            responseType: [PointRecord].self
        )
    }

    // MARK: - 统计相关API

    // 获取任务统计（基于今日任务和待审核任务计算）
    func getTaskStatistics() -> AnyPublisher<TaskStatistics, APIError> {
        return Publishers.CombineLatest3(getTodayTasks(), getPendingTasks(), getTodayPointChange())
            .map { tasks, pendingTasks, pointChange in
                let completed = tasks.filter { $0.status == .completed }.count
                let inProgress = tasks.filter { $0.status == .inProgress }.count
                let pending = pendingTasks.count  // 使用专门的待审核任务接口
                let notStarted = tasks.filter { $0.status == .notStarted }.count
                let total = tasks.count

                // 计算今日可获得积分（所有任务的基础积分总和）
                let availablePoints = tasks.reduce(0) { $0 + $1.basePoints }

                // 计算今日已获得积分（已完成任务的积分总和）
                let earnedPoints = tasks.filter { $0.status == .completed }
                    .reduce(0) { $0 + ($1.actualPoints ?? $1.basePoints) }

                return TaskStatistics(
                    todayCompleted: completed,
                    todayInProgress: inProgress,
                    todayNotStarted: notStarted,
                    todayPending: pending,
                    todayTotal: total,
                    todayAvailablePoints: availablePoints,
                    todayEarnedPoints: earnedPoints
                )
            }
            .eraseToAnyPublisher()
    }

    // MARK: - 奖励相关API

    // 获取可用奖励池
    func getAvailableRewardPools() -> AnyPublisher<[RewardPool], APIError> {
        print("🎁 使用真实API获取可用奖励池数据")
        return networkManager.request(
            endpoint: .availableRewardPools,
            responseType: [RewardPool].self
        )
    }

    // 抽奖
    func drawReward(poolId: Int) -> AnyPublisher<RewardDrawResult, APIError> {
        print("🎲 使用真实API进行抽奖，奖励池ID: \(poolId)")
        return networkManager.request(
            endpoint: .drawReward(poolId: poolId),
            responseType: RewardDrawResult.self
        )
    }

    // MARK: - 兑换相关API

    // 获取可用兑换商品
    func getAvailableExchangeItems() -> AnyPublisher<[ExchangeItem], APIError> {
        print("🛒 使用真实API获取可用兑换商品数据")
        return networkManager.request(
            endpoint: .availableExchangeItems,
            responseType: [ExchangeItem].self
        )
    }

    // 按分类获取兑换商品
    func getExchangeItemsByCategory(category: String) -> AnyPublisher<[ExchangeItem], APIError> {
        print("🛒 使用真实API获取分类兑换商品数据，分类: \(category)")
        return networkManager.request(
            endpoint: .exchangeItemsByCategory(category: category),
            responseType: [ExchangeItem].self
        )
    }

    // 兑换商品
    func exchangeItem(itemId: Int) -> AnyPublisher<ExchangeRecord, APIError> {
        print("💰 使用真实API兑换商品，商品ID: \(itemId)")
        return networkManager.request(
            endpoint: .exchangeItem(itemId: itemId),
            responseType: ExchangeRecord.self
        )
    }

    // 获取兑换记录
    func getExchangeRecords() -> AnyPublisher<[ExchangeRecord], APIError> {
        print("📋 使用真实API获取兑换记录数据")
        return networkManager.request(
            endpoint: .exchangeRecords,
            responseType: [ExchangeRecord].self
        )
    }

    // 获取未使用的兑换记录
    func getUnusedExchangeRecords() -> AnyPublisher<[ExchangeRecord], APIError> {
        print("📋 使用真实API获取未使用兑换记录数据")
        return networkManager.request(
            endpoint: .unusedExchangeRecords,
            responseType: [ExchangeRecord].self
        )
    }
}
