package com.example.childreward.service.impl;

import com.example.childreward.entity.SystemConfig;
import com.example.childreward.repository.SystemConfigRepository;
import com.example.childreward.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SystemConfigServiceImpl implements SystemConfigService {

    private final SystemConfigRepository systemConfigRepository;

    @Override
    public List<SystemConfig> getAllConfigs() {
        return systemConfigRepository.findAll();
    }

    @Override
    public SystemConfig getConfigByKey(String key) {
        return systemConfigRepository.findByConfigKey(key)
                .orElse(null);
    }

    @Override
    @Transactional
    public SystemConfig createConfig(SystemConfig config) {
        return systemConfigRepository.save(config);
    }

    @Override
    @Transactional
    public SystemConfig updateConfig(String key, String value, String description) {
        SystemConfig config = systemConfigRepository.findByConfigKey(key)
                .orElseGet(() -> SystemConfig.builder().configKey(key).build());
        
        config.setConfigValue(value);
        if (description != null) {
            config.setDescription(description);
        }
        return systemConfigRepository.save(config);
    }

    @Override
    @Transactional
    public void deleteConfig(String key) {
        systemConfigRepository.findByConfigKey(key)
                .ifPresent(systemConfigRepository::delete);
    }

    @Override
    public String getStringValue(String key, String defaultValue) {
        return systemConfigRepository.findByConfigKey(key)
                .map(SystemConfig::getConfigValue)
                .orElse(defaultValue);
    }

    @Override
    public Integer getIntValue(String key, Integer defaultValue) {
        return systemConfigRepository.findByConfigKey(key)
                .map(config -> {
                    try {
                        return Integer.parseInt(config.getConfigValue());
                    } catch (NumberFormatException e) {
                        return defaultValue;
                    }
                })
                .orElse(defaultValue);
    }

    @Override
    public Boolean getBooleanValue(String key, Boolean defaultValue) {
        String value = getStringValue(key, null);
        if (value == null) {
            return defaultValue;
        }
        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }

    @Override
    @Transactional
    public void set(String key, String value, String description) {
        SystemConfig config = systemConfigRepository.findByConfigKey(key)
                .orElseGet(() -> SystemConfig.builder().configKey(key).build());

        config.setConfigValue(value);
        if (description != null) {
            config.setDescription(description);
        }
        systemConfigRepository.save(config);
    }

    @Override
    public String get(String key, String defaultValue) {
        return getStringValue(key, defaultValue);
    }

    @Override
    public Integer getInt(String key, Integer defaultValue) {
        return getIntValue(key, defaultValue);
    }

    @Override
    public Boolean getBoolean(String key, Boolean defaultValue) {
        return getBooleanValue(key, defaultValue);
    }
} 