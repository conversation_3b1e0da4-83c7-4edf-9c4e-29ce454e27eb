import SwiftUI

// MARK: - 任务卡片组件
struct TaskCard: View {
    let title: String
    let description: String
    let points: Int
    let isCompleted: Bool
    let onToggle: () -> Void
    let onTap: () -> Void
    
    // 可选参数
    var status: TaskStatus = .notStarted
    var dueDate: String? = nil
    var category: String? = nil
    var difficulty: TaskDifficulty? = nil

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 16) {
                // 卡片头部
                cardHeader
                
                // 卡片内容
                cardContent
                
                // 卡片底部
                cardFooter
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isCompleted ? Color.green.opacity(0.3) : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 子视图
    
    private var cardHeader: some View {
        HStack {
            // 状态指示器
            statusIndicator
            
            Spacer()
            
            // 积分显示
            pointsBadge
        }
    }
    
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
                .lineLimit(2)
            
            // 描述
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .lineLimit(3)
            
            // 移除单独的进度条，因为顶部已有整体进度条
            
            // 额外信息
            extraInfo
        }
    }
    
    private var cardFooter: some View {
        HStack {
            // 截止日期
            if let dueDate = dueDate {
                Label(dueDate, systemImage: "calendar")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 操作按钮
            actionButton
        }
    }
    
    private var statusIndicator: some View {
        Circle()
            .fill(statusColor)
            .frame(width: 12, height: 12)
            .overlay(
                Circle()
                    .stroke(statusColor.opacity(0.3), lineWidth: 2)
                    .frame(width: 20, height: 20)
            )
    }
    
    private var pointsBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: "star.fill")
                .font(.caption)
                .foregroundColor(.orange)
            
            Text("\(points)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.orange)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var extraInfo: some View {
        HStack(spacing: 12) {
            // 分类标签
            if let category = category {
                categoryTag(category)
            }
            
            // 难度标签
            if let difficulty = difficulty {
                difficultyTag(difficulty)
            }
            
            Spacer()
        }
    }
    
    private var actionButton: some View {
        if status == .rejected {
            // 被拒绝的任务显示状态标签而不是按钮
            HStack(spacing: 6) {
                Image(systemName: buttonIcon)
                    .font(.caption)

                Text(buttonText)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(buttonColor.opacity(0.6))
            .cornerRadius(8)
        } else {
            Button(action: onToggle) {
                HStack(spacing: 6) {
                    Image(systemName: buttonIcon)
                        .font(.caption)

                    Text(buttonText)
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(buttonColor)
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 辅助方法
    

    
    private func categoryTag(_ category: String) -> some View {
        Text(category)
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.blue)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(4)
    }
    
    private func difficultyTag(_ difficulty: TaskDifficulty) -> some View {
        Text(difficulty.displayName)
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(difficultyColor(difficulty))
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(difficultyColor(difficulty).opacity(0.1))
            .cornerRadius(4)
    }
    
    // MARK: - 计算属性
    
    private var statusColor: Color {
        if isCompleted {
            return .green
        }

        switch status {
        case .notStarted, .pending:
            return .orange
        case .inProgress:
            return .blue
        case .completed, .approved:
            return .green
        case .overdue:
            return .red
        case .cancelled:
            return .gray
        case .rejected:
            return .red
        }
    }
    
    private var buttonIcon: String {
        if isCompleted {
            return "checkmark"
        }

        switch status {
        case .notStarted, .pending:
            return "play.fill"
        case .inProgress:
            return "checkmark"
        case .completed, .approved:
            return "checkmark"
        case .overdue, .cancelled:
            return "arrow.clockwise"
        case .rejected:
            return "xmark"
        }
    }
    
    private var buttonText: String {
        if isCompleted {
            return "已完成"
        }

        switch status {
        case .notStarted, .pending:
            return "开始"
        case .inProgress:
            return "完成"
        case .completed, .approved:
            return "已完成"
        case .overdue:
            return "重新开始"
        case .cancelled:
            return "重新开始"
        case .rejected:
            return "已作废"
        }
    }
    
    private var buttonColor: Color {
        if isCompleted {
            return .green
        }

        switch status {
        case .notStarted, .pending:
            return .blue
        case .inProgress:
            return .green
        case .completed, .approved:
            return .green
        case .overdue:
            return .orange
        case .cancelled:
            return .gray
        case .rejected:
            return .red
        }
    }
    
    private func difficultyColor(_ difficulty: TaskDifficulty) -> Color {
        switch difficulty {
        case .easy:
            return .green
        case .medium:
            return .orange
        case .hard:
            return .red
        }
    }
}

// MARK: - 预览
struct TaskCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            TaskCard(
                title: "完成数学作业",
                description: "完成今天的数学练习题，包括加减乘除运算",
                points: 50,
                isCompleted: false,
                onToggle: {},
                onTap: {},
                status: .notStarted,
                dueDate: "今天 18:00",
                category: "学习",
                difficulty: .medium
            )
            
            TaskCard(
                title: "整理房间",
                description: "整理书桌和床铺，把玩具放回原位",
                points: 30,
                isCompleted: true,
                onToggle: {},
                onTap: {},
                status: .completed,
                category: "家务",
                difficulty: .easy
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}
