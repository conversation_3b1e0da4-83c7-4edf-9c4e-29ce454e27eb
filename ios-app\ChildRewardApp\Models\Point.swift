import Foundation
import SwiftUI

// 积分记录类型枚举
enum PointChangeType: String, CaseIterable, Codable {
    case taskComplete = "TASK_COMPLETE"
    case taskApprove = "TASK_APPROVE"
    case rewardDraw = "REWARD_DRAW"
    case exchange = "EXCHANGE"
    case bonus = "BONUS"
    case penalty = "PENALTY"
    
    var displayName: String {
        switch self {
        case .taskComplete: return "完成任务"
        case .taskApprove: return "任务批准"
        case .rewardDraw: return "抽奖消费"
        case .exchange: return "积分兑换"
        case .bonus: return "奖励积分"
        case .penalty: return "扣除积分"
        }
    }
    
    var icon: String {
        switch self {
        case .taskComplete: return "✅"
        case .taskApprove: return "👍"
        case .rewardDraw: return "🎰"
        case .exchange: return "🛒"
        case .bonus: return "🎁"
        case .penalty: return "⚠️"
        }
    }
    
    var isPositive: Bool {
        switch self {
        case .taskComplete, .taskApprove, .bonus:
            return true
        case .rewardDraw, .exchange, .penalty:
            return false
        }
    }
}

// 积分记录模型
struct PointRecord: Codable, Identifiable {
    let id: Int
    let pointChange: Int
    let description: String
    let changeType: String?
    let recordTime: String
    let relatedTaskId: Int?
    let relatedRewardId: Int?

    // 为了兼容性，提供别名
    var change: Int {
        return pointChange
    }

    var createdAt: String {
        return recordTime
    }

    var taskId: Int? {
        return relatedTaskId
    }

    var rewardId: Int? {
        return relatedRewardId
    }

    var type: PointChangeType? {
        guard let changeType = changeType else { return nil }
        return PointChangeType(rawValue: changeType)
    }

    // 计算属性
    var isPositive: Bool {
        return change > 0
    }

    var formattedChange: String {
        let prefix = change > 0 ? "+" : ""
        return "\(prefix)\(change)"
    }

    var formattedDate: String {
        guard let date = recordDate else {
            // 如果时间解析失败，尝试从原始字符串中提取可读信息
            print("⚠️ 时间解析失败，原始时间: \(recordTime)")

            // 尝试简单的字符串处理，提取日期部分
            if recordTime.contains("T") {
                let parts = recordTime.components(separatedBy: "T")
                if parts.count >= 2 {
                    let datePart = parts[0]
                    let timePart = parts[1].components(separatedBy: ".")[0] // 去掉毫秒部分
                    return "\(datePart) \(timePart)"
                }
            }

            // 如果包含空格，可能是 "yyyy-MM-dd HH:mm:ss" 格式
            if recordTime.contains(" ") && recordTime.count > 10 {
                let parts = recordTime.components(separatedBy: " ")
                if parts.count >= 2 {
                    return "\(parts[0]) \(parts[1].prefix(5))" // 只显示到分钟
                }
            }

            // 最后回退到原始字符串
            return recordTime
        }

        let calendar = Calendar.current
        let now = Date()

        // 检查是否是今天
        if calendar.isDateInToday(date) {
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            timeFormatter.locale = Locale(identifier: "zh_CN")
            return "今天 \(timeFormatter.string(from: date))"
        }

        // 检查是否是昨天
        if calendar.isDateInYesterday(date) {
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            timeFormatter.locale = Locale(identifier: "zh_CN")
            return "昨天 \(timeFormatter.string(from: date))"
        }

        // 检查是否是本周内
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
        if date > weekAgo {
            let dayFormatter = DateFormatter()
            dayFormatter.dateFormat = "EEEE HH:mm"
            dayFormatter.locale = Locale(identifier: "zh_CN")
            return dayFormatter.string(from: date)
        }

        // 其他情况使用完整日期
        return DateFormatter.pointRecord.string(from: date)
    }

    var displayIcon: String {
        return type?.icon ?? (change > 0 ? "⭐" : "💸")
    }

    var displayType: String {
        return type?.displayName ?? (change > 0 ? "获得积分" : "消费积分")
    }
}

// 积分余额模型
struct PointBalance: Codable {
    let totalPoints: Int

    // 为了兼容性，提供别名
    var total: Int {
        return totalPoints
    }
    var available: Int {
        return totalPoints
    }
    var frozen: Int {
        return 0
    }

    var formattedTotal: String {
        return "\(totalPoints)"
    }
}

// 今日积分变化模型（后端返回格式）
struct TodayPointChange: Codable {
    let todayChange: Int
    let todayEarned: Int?    // 今日获得积分
    let todaySpent: Int?     // 今日消费积分

    var formattedChange: String {
        let prefix = todayChange > 0 ? "+" : ""
        return "\(prefix)\(todayChange)"
    }

    var isPositive: Bool {
        return todayChange > 0
    }

    // 如果后端没有提供详细数据，使用计算值
    var calculatedEarned: Int {
        return todayEarned ?? max(0, todayChange)
    }

    var calculatedSpent: Int {
        return todaySpent ?? max(0, -todayChange)
    }
}

// 积分统计模型
struct PointStatistics: Codable {
    let todayEarned: Int
    let weekEarned: Int
    let monthEarned: Int
    let totalEarned: Int
    let totalSpent: Int
    
    var todayFormatted: String {
        return "+\(todayEarned)"
    }
    
    var weekFormatted: String {
        return "+\(weekEarned)"
    }
    
    var monthFormatted: String {
        return "+\(monthEarned)"
    }
}

// 积分页面统计数据
struct PointsStatistics {
    let totalEarned: Int    // 总获得积分
    let totalSpent: Int     // 总消费积分
    let net: Int           // 净变化

    init(totalEarned: Int, totalSpent: Int, net: Int) {
        self.totalEarned = totalEarned
        self.totalSpent = totalSpent
        self.net = net
    }

    // 从积分记录计算统计数据
    static func calculate(from records: [PointRecord]) -> PointsStatistics {
        let earned = records.filter { $0.pointChange > 0 }.reduce(0) { $0 + $1.pointChange }
        let spent = records.filter { $0.pointChange < 0 }.reduce(0) { $0 + abs($1.pointChange) }
        let net = earned - spent
        return PointsStatistics(totalEarned: earned, totalSpent: spent, net: net)
    }
}

// PointRecord UI扩展
extension PointRecord {
    // 记录类型图标
    var typeIcon: String {
        // 根据积分变化的正负来显示图标
        if pointChange > 0 {
            // 加分项：统一使用上升箭头图标
            return "arrow.up.circle.fill"
        } else {
            // 减分项：统一使用下降箭头图标
            return "arrow.down.circle.fill"
        }
    }

    // 记录类型颜色
    var typeColor: Color {
        // 根据积分变化的正负来显示颜色
        if pointChange > 0 {
            // 加分项：根据类型显示不同颜色
            switch changeType {
            case "TASK_COMPLETION":
                return .green
            case "REWARD_EXCHANGE":
                return .orange
            default:
                return .blue
            }
        } else {
            // 减分项：统一显示红色
            return .red
        }
    }

    // 格式化的积分变化
    var formattedPointChange: String {
        return pointChange > 0 ? "+\(pointChange)" : "\(pointChange)"
    }

    // 格式化的时间（带相对时间）
    var formattedTime: String {
        // 直接使用 formattedDate 属性，保持一致性
        return formattedDate
    }

    // 记录日期 - 增强时间解析兼容性
    var recordDate: Date? {
        // 尝试多种时间格式解析

        // 1. 尝试 ISO8601 格式（带毫秒）
        let iso8601WithFractional = ISO8601DateFormatter()
        iso8601WithFractional.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        if let date = iso8601WithFractional.date(from: recordTime) {
            return date
        }

        // 2. 尝试标准 ISO8601 格式
        let iso8601Standard = ISO8601DateFormatter()
        if let date = iso8601Standard.date(from: recordTime) {
            return date
        }

        // 3. 尝试常见的日期时间格式
        let commonFormats = [
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS",  // 带微秒
            "yyyy-MM-dd'T'HH:mm:ss.SSS",     // 带毫秒
            "yyyy-MM-dd'T'HH:mm:ss",         // 不带毫秒
            "yyyy-MM-dd HH:mm:ss.SSSSSS",    // 空格分隔带微秒
            "yyyy-MM-dd HH:mm:ss.SSS",       // 空格分隔带毫秒
            "yyyy-MM-dd HH:mm:ss",           // 空格分隔不带毫秒
            "yyyy/MM/dd HH:mm:ss",           // 斜杠分隔
            "MM/dd/yyyy HH:mm:ss"            // 美式格式
        ]

        for format in commonFormats {
            let formatter = DateFormatter()
            formatter.dateFormat = format
            formatter.locale = Locale(identifier: "en_US_POSIX")
            formatter.timeZone = TimeZone.current
            if let date = formatter.date(from: recordTime) {
                return date
            }
        }

        // 4. 如果所有格式都失败，尝试解析时间戳
        if let timestamp = Double(recordTime) {
            // 检查是否是毫秒时间戳
            if timestamp > 1000000000000 {
                return Date(timeIntervalSince1970: timestamp / 1000)
            } else {
                return Date(timeIntervalSince1970: timestamp)
            }
        }

        print("⚠️ 无法解析时间格式: \(recordTime)")
        return nil
    }
}

// StatCard组件
struct StatCard: View {
    let title: String
    let value: String
    let color: Color
    let icon: String

    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(color.opacity(0.1))
                .cornerRadius(8)

            VStack(spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// 日期格式化扩展
extension DateFormatter {
    static let pointRecord: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    static let pointDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}

// MARK: - 奖励和兑换相关数据模型

// 奖励池类型枚举
enum PoolType: String, Codable {
    case reward = "REWARD"
    case penalty = "PENALTY"

    var displayName: String {
        switch self {
        case .reward: return "奖励池"
        case .penalty: return "惩罚池"
        }
    }
}

// 奖励池模型 - 匹配后端RewardPool实体
struct RewardPool: Codable, Identifiable {
    let id: Int
    let name: String
    let costPoints: Int
    let isEnabled: Bool
    let poolType: PoolType
    let createdTime: String?
    let rewardItems: [RewardItemNew]?

    // 计算属性
    var isAvailable: Bool {
        return isEnabled && poolType == .reward
    }

    var canDraw: Bool {
        return isAvailable
    }

    var formattedCost: String {
        return "\(costPoints)积分"
    }

    var itemCount: Int {
        return rewardItems?.count ?? 0
    }
}

// 奖励项模型 - 匹配后端RewardItem实体
struct RewardItemNew: Codable, Identifiable {
    let id: Int
    let name: String
    let probability: Float
    let stock: Int
    let imageUrl: String?
    let createdTime: String?

    var formattedProbability: String {
        return String(format: "%.1f%%", probability * 100)
    }

    var hasStock: Bool {
        return stock > 0
    }

    var stockText: String {
        return "库存\(stock)个"
    }
}

// 兑换商品模型 - 匹配后端ExchangeItem实体
struct ExchangeItem: Codable, Identifiable {
    let id: Int
    let name: String
    let description: String?
    let requiredPoints: Int
    let stock: Int?
    let category: String?
    let imageUrl: String?
    let isActive: Bool
    let sortOrder: Int?
    let createdTime: String?
    let updatedTime: String?

    var formattedCost: String {
        return "\(requiredPoints)积分"
    }

    var hasStock: Bool {
        return stock == nil || stock == -1 || stock! > 0
    }

    var canExchange: Bool {
        return isActive && hasStock
    }

    var stockText: String? {
        guard let stock = stock, stock != -1 else { return nil }
        return stock > 0 ? "库存\(stock)件" : "缺货"
    }
}

// 抽奖结果模型
struct RewardDrawResult: Codable, Identifiable {
    let id: Int
    let rewardItem: RewardItemNew
    let drawTime: String
    let pointsSpent: Int

    var formattedDrawTime: String {
        guard let date = ISO8601DateFormatter().date(from: drawTime) else {
            return drawTime
        }
        return DateFormatter.pointRecord.string(from: date)
    }
}

// 兑换状态枚举
enum ExchangeStatus: String, CaseIterable, Codable {
    case unused = "UNUSED"
    case used = "USED"
    case expired = "EXPIRED"
    case pending = "PENDING"
    case approved = "APPROVED"
    case delivered = "DELIVERED"
    case cancelled = "CANCELLED"

    var displayName: String {
        switch self {
        case .unused: return "待使用"
        case .used: return "已使用"
        case .expired: return "已过期"
        case .pending: return "待处理"
        case .approved: return "已批准"
        case .delivered: return "已发放"
        case .cancelled: return "已取消"
        }
    }

    var color: Color {
        switch self {
        case .unused: return .orange
        case .used: return .green
        case .expired: return .red
        case .pending: return .orange
        case .approved: return .blue
        case .delivered: return .green
        case .cancelled: return .gray
        }
    }
}

// 兑换记录模型 - 匹配后端ExchangeRecord实体
struct ExchangeRecord: Identifiable, Codable {
    let id: Int
    let exchangeItemId: Int
    let itemName: String
    let pointsUsed: Int
    let exchangeTime: String
    let status: ExchangeStatus
    let usedTime: String?
    let expireTime: String?
    let notes: String?

    // 兼容旧版本的属性
    var rewardId: Int { exchangeItemId }
    var rewardName: String { itemName }
    var pointsCost: Int { pointsUsed }
    var exchangeDate: String { exchangeTime }

    var formattedDate: String {
        return formattedExchangeTime
    }

    var formattedExchangeTime: String {
        guard let date = ISO8601DateFormatter().date(from: exchangeTime) else {
            return exchangeTime
        }
        return DateFormatter.pointRecord.string(from: date)
    }

    var formattedExpireTime: String? {
        guard let expireTime = expireTime,
              let date = ISO8601DateFormatter().date(from: expireTime) else {
            return nil
        }
        return DateFormatter.pointRecord.string(from: date)
    }
}
