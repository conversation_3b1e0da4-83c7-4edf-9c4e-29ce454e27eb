-- 数据库性能检查脚本
-- 检查索引使用情况和查询性能

-- 1. 检查表大小和记录数
SELECT 
    table_name as '表名',
    table_rows as '记录数',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as '大小(MB)',
    ROUND((data_length / 1024 / 1024), 2) as '数据大小(MB)',
    ROUND((index_length / 1024 / 1024), 2) as '索引大小(MB)'
FROM information_schema.tables 
WHERE table_schema = 'child_reward_system'
ORDER BY (data_length + index_length) DESC;

-- 2. 检查索引使用情况
SELECT 
    table_name as '表名',
    index_name as '索引名',
    column_name as '列名',
    cardinality as '基数'
FROM information_schema.statistics 
WHERE table_schema = 'child_reward_system'
ORDER BY table_name, index_name;

-- 3. 检查最近的慢查询（如果启用了慢查询日志）
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SHOW VARIABLES LIKE 'long_query_time';

-- 4. 检查当前连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';
SHOW VARIABLES LIKE 'max_connections';

-- 5. 检查缓冲池使用情况
SHOW STATUS LIKE 'Innodb_buffer_pool_pages_total';
SHOW STATUS LIKE 'Innodb_buffer_pool_pages_free';
SHOW STATUS LIKE 'Innodb_buffer_pool_pages_data';

-- 6. 测试常用查询的执行时间
-- 测试今日任务查询
EXPLAIN SELECT * FROM tasks WHERE scheduled_date = CURDATE();

-- 测试待审批任务查询
EXPLAIN SELECT * FROM tasks WHERE status = 'PENDING';

-- 测试积分记录查询
EXPLAIN SELECT * FROM point_record WHERE DATE(record_time) = CURDATE();

-- 7. 检查表的碎片情况
SELECT 
    table_name as '表名',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as '总大小(MB)',
    ROUND((data_free / 1024 / 1024), 2) as '碎片大小(MB)',
    ROUND((data_free / (data_length + index_length)) * 100, 2) as '碎片率(%)'
FROM information_schema.tables 
WHERE table_schema = 'child_reward_system' 
AND data_free > 0
ORDER BY data_free DESC;
