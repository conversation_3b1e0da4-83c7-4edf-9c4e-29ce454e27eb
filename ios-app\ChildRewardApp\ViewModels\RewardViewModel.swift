import Foundation
import Combine

// MARK: - 通知名称扩展
extension Notification.Name {
    static let pointsDidChange = Notification.Name("pointsDidChange")
}

// MARK: - 奖励系统视图模型
@MainActor
class RewardViewModel: ObservableObject {
    @Published var availableRewardPools: [RewardPool] = []
    @Published var exchangeItems: [ExchangeItem] = []
    @Published var exchangeHistory: [ExchangeRecord] = []
    @Published var totalPoints: Int = 0
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?

    private let apiService = ChildAPIService.shared
    private var cancellables = Set<AnyCancellable>()

    init() {
        loadData()
    }

    // MARK: - 数据加载

    func loadData() {
        print("🔄 RewardViewModel: 强制刷新所有数据...")

        // 清除所有订阅，确保重新获取数据
        cancellables.removeAll()

        // 重置状态
        isLoading = true
        errorMessage = nil

        // 强制重新加载所有数据
        loadRewardPools()
        loadExchangeItems()
        loadExchangeHistory()
        loadPointBalance()
    }
    
    // 加载奖励池
    private func loadRewardPools() {
        print("🎁 开始加载奖励池数据...")
        isLoading = true
        errorMessage = nil

        apiService.getAvailableRewardPools()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        print("❌ 加载奖励池失败: \(error)")
                        self.errorMessage = "加载奖励池失败: \(error.localizedDescription)"
                    }
                },
                receiveValue: { pools in
                    self.availableRewardPools = pools
                    print("✅ 成功加载奖励池: \(pools.count)个")
                }
            )
            .store(in: &cancellables)
    }

    // 加载兑换商品
    private func loadExchangeItems() {
        print("🛒 开始加载兑换商品数据...")

        apiService.getAvailableExchangeItems()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载兑换商品失败: \(error)")
                        self.errorMessage = "加载兑换商品失败: \(error.localizedDescription)"
                    }
                },
                receiveValue: { items in
                    self.exchangeItems = items
                    print("✅ 成功加载兑换商品: \(items.count)个")
                }
            )
            .store(in: &cancellables)
    }

    // 加载兑换历史
    private func loadExchangeHistory() {
        print("📋 开始加载兑换历史数据...")

        apiService.getExchangeRecords()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载兑换历史失败: \(error)")
                        // 兑换历史失败不显示错误，因为可能是空数据
                    }
                },
                receiveValue: { records in
                    self.exchangeHistory = records
                    print("✅ 成功加载兑换历史: \(records.count)条")
                }
            )
            .store(in: &cancellables)
    }

    // 加载积分余额
    private func loadPointBalance() {
        print("💰 开始加载积分余额数据...")

        apiService.getPointBalance()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载积分余额失败: \(error)")
                        self.errorMessage = "加载积分余额失败: \(error.localizedDescription)"
                    }
                },
                receiveValue: { balance in
                    self.totalPoints = balance.totalPoints
                    print("✅ 成功加载积分余额: \(balance.totalPoints)")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 奖励操作

    // 抽奖
    func drawReward(from pool: RewardPool) {
        guard totalPoints >= pool.costPoints else {
            errorMessage = "积分不足，无法抽奖"
            return
        }

        print("🎲 开始抽奖，奖励池: \(pool.name)")
        isLoading = true
        errorMessage = nil

        apiService.drawReward(poolId: pool.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        print("❌ 抽奖失败: \(error)")
                        self.errorMessage = "抽奖失败: \(error.localizedDescription)"
                    }
                },
                receiveValue: { result in
                    print("✅ 抽奖成功: \(result.rewardItem.name)")
                    self.successMessage = "恭喜获得：\(result.rewardItem.name)！"
                    // 重新加载数据以更新积分和历史
                    self.loadData()
                }
            )
            .store(in: &cancellables)
    }

    // 兑换商品
    func exchangeItem(_ item: ExchangeItem) {
        guard totalPoints >= item.requiredPoints else {
            errorMessage = "积分不足，无法兑换"
            return
        }

        guard item.canExchange else {
            errorMessage = "该商品暂时无法兑换"
            return
        }

        print("💰 开始兑换商品: \(item.name)")
        isLoading = true
        errorMessage = nil

        apiService.exchangeItem(itemId: item.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        print("❌ 兑换失败: \(error)")
                        self.errorMessage = "兑换失败: \(error.localizedDescription)"
                    }
                },
                receiveValue: { record in
                    print("✅ 兑换成功: \(record.itemName)")
                    self.successMessage = "兑换成功：\(record.itemName)！"
                    // 重新加载数据以更新积分和历史
                    self.loadData()
                    // 发送通知，通知主页面刷新积分数据
                    NotificationCenter.default.post(name: .pointsDidChange, object: nil)
                }
            )
            .store(in: &cancellables)
    }

    // 清除消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }

    // MARK: - 便利方法

    // 获取可用的兑换商品（按分类筛选）
    func getExchangeItems(for category: String?) -> [ExchangeItem] {
        guard let category = category, !category.isEmpty else {
            return exchangeItems
        }
        return exchangeItems.filter { $0.category == category }
    }

    // 获取所有分类
    var availableCategories: [String] {
        let categories = Set(exchangeItems.compactMap { $0.category })
        return Array(categories).sorted()
    }
}
