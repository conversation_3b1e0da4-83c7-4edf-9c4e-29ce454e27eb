# 儿童奖励系统 - 一体化Docker部署

## 🎯 一体化方案优势

✅ **超简单部署**: 只需一个容器，一条命令启动  
✅ **资源优化**: 共享JVM和系统资源，内存占用更少  
✅ **管理方便**: 统一日志、统一监控、统一重启  
✅ **网络简化**: 内部通信无网络开销，性能更好  
✅ **端口统一**: 只需开放一个端口(80)即可访问所有功能  

## 📦 包含组件

- **Nginx**: 前端静态文件服务 + API反向代理
- **Spring Boot**: 后端API服务
- **Supervisor**: 进程管理，确保服务稳定运行

## 🚀 本地快速测试

### 1. 构建并启动
```powershell
# 首次使用 - 构建镜像并启动
.\docker-run-single.ps1 -Action build

# 查看状态
.\docker-run-single.ps1 -Action status
```

### 2. 访问测试
- **家长端**: http://localhost:8080
- **儿童端**: http://localhost:8080/child  
- **健康检查**: http://localhost:8080/actuator/health

### 3. 管理命令
```powershell
.\docker-run-single.ps1 -Action start    # 启动
.\docker-run-single.ps1 -Action stop     # 停止
.\docker-run-single.ps1 -Action restart  # 重启
.\docker-run-single.ps1 -Action logs     # 查看日志
.\docker-run-single.ps1 -Action status   # 查看状态
```

## 🏠 威联通NAS部署

### 方案1: 使用Docker Compose (推荐)

#### 1. 上传文件到NAS
将以下文件上传到NAS的 `/share/CACHEDEV1_DATA/crs/` 目录：
- `docker-compose-single.yml`

#### 2. 修改配置
```bash
# SSH连接NAS
ssh admin@你的NAS_IP

# 进入部署目录
cd /share/CACHEDEV1_DATA/crs/

# 编辑配置文件
nano docker-compose-single.yml
```

修改数据库配置：
```yaml
environment:
  - MYSQL_HOST=你的数据库IP
  - MYSQL_PORT=3306
  - MYSQL_PASSWORD=你的数据库密码
```

#### 3. 启动服务
```bash
# 启动服务
docker-compose -f docker-compose-single.yml up -d

# 查看状态
docker-compose -f docker-compose-single.yml ps

# 查看日志
docker-compose -f docker-compose-single.yml logs -f
```

### 方案2: 直接运行容器

```bash
# 拉取镜像 (如果已推送到仓库)
docker pull registry.cn-hangzhou.aliyuncs.com/zlean/crs-system:1.0.0

# 运行容器
docker run -d \
  --name crs-system \
  -p 8080:80 \
  -e MYSQL_HOST=你的数据库IP \
  -e MYSQL_PASSWORD=你的数据库密码 \
  -v /share/CACHEDEV1_DATA/crs/logs:/app/logs \
  --restart always \
  registry.cn-hangzhou.aliyuncs.com/zlean/crs-system:1.0.0
```

## ⚙️ 配置说明

### 环境变量
```bash
# 数据库配置
MYSQL_HOST=*************      # 数据库主机
MYSQL_PORT=3307               # 数据库端口  
MYSQL_DATABASE=crs            # 数据库名
MYSQL_USERNAME=root           # 用户名
MYSQL_PASSWORD=123456         # 密码

# JVM配置 (NAS优化)
JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC
```

### 端口映射
- `8080:80` - 统一访问端口 (前端 + API)
- `18080:18080` - 后端API直接访问 (可选)

### 资源限制 (可选)
```yaml
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '1.0'
```

## 📊 性能对比

| 方案 | 内存占用 | 启动时间 | 容器数量 | 管理复杂度 |
|------|----------|----------|----------|------------|
| 分离式 | 768MB+ | 60s+ | 2个 | 中等 |
| 一体化 | 512MB | 30s | 1个 | 简单 |

## 🛠️ 管理命令

### Docker Compose方式
```bash
# 启动
docker-compose -f docker-compose-single.yml up -d

# 停止  
docker-compose -f docker-compose-single.yml down

# 重启
docker-compose -f docker-compose-single.yml restart

# 查看日志
docker-compose -f docker-compose-single.yml logs -f

# 更新镜像
docker-compose -f docker-compose-single.yml pull
docker-compose -f docker-compose-single.yml up -d --force-recreate
```

### 直接容器方式
```bash
# 启动
docker start crs-system

# 停止
docker stop crs-system

# 重启  
docker restart crs-system

# 查看日志
docker logs -f crs-system

# 进入容器
docker exec -it crs-system bash
```

## 🔍 故障排除

### 1. 容器无法启动
```bash
# 查看容器状态
docker ps -a

# 查看启动日志
docker logs crs-system

# 检查端口占用
netstat -tlnp | grep 8080
```

### 2. 服务无法访问
```bash
# 检查容器内服务状态
docker exec -it crs-system supervisorctl status

# 检查Nginx配置
docker exec -it crs-system nginx -t

# 重启容器内服务
docker exec -it crs-system supervisorctl restart all
```

### 3. 数据库连接问题
```bash
# 查看后端日志
docker exec -it crs-system tail -f /var/log/supervisor/spring-boot.out.log

# 测试数据库连接
docker exec -it crs-system curl http://localhost:18080/actuator/health
```

### 4. 性能问题
```bash
# 查看资源使用
docker stats crs-system

# 调整JVM参数
docker run ... -e JAVA_OPTS="-Xms128m -Xmx256m -XX:+UseG1GC" ...
```

## 📋 健康检查

### 自动健康检查
容器内置健康检查，每30秒检查一次：
```bash
# 查看健康状态
docker inspect crs-system | grep Health -A 10
```

### 手动检查
```bash
# 前端检查
curl http://localhost:8080

# 后端检查  
curl http://localhost:8080/actuator/health

# 容器内服务检查
docker exec -it crs-system supervisorctl status
```

## 🔄 更新部署

### 1. 更新代码
```bash
# 重新构建镜像
.\docker-build-single.ps1 -Version "1.0.1"

# 停止旧容器
docker stop crs-system && docker rm crs-system

# 启动新容器
docker run -d --name crs-system ... 新镜像
```

### 2. 配置更新
```bash
# 修改环境变量后重启
docker restart crs-system
```

## 🎯 最佳实践

### 1. 生产环境建议
- 使用具体版本号，避免使用 `latest`
- 配置资源限制，防止资源耗尽
- 设置重启策略 `--restart always`
- 定期备份日志和数据

### 2. 监控建议
```bash
# 定期检查容器状态
docker ps | grep crs-system

# 监控资源使用
docker stats crs-system

# 检查日志大小
du -sh /share/CACHEDEV1_DATA/crs/logs/
```

### 3. 安全建议
- 使用非root用户运行 (生产环境)
- 定期更新基础镜像
- 限制容器网络访问
- 使用secrets管理敏感信息

---

## 🎉 总结

一体化Docker部署方案让您的儿童奖励系统：
- **部署更简单**: 一个容器搞定一切
- **性能更好**: 内部通信无网络开销  
- **管理更方便**: 统一的日志和监控
- **资源更省**: 共享系统资源，降低内存占用

现在您只需要一条命令就能在NAS上运行完整的儿童奖励系统！