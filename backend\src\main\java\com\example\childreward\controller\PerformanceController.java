package com.example.childreward.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控控制器
 */
@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/api/performance")
@RequiredArgsConstructor
public class PerformanceController {

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "healthy");
        result.put("timestamp", LocalDateTime.now());
        result.put("message", "系统运行正常");
        return ResponseEntity.ok(result);
    }

    @GetMapping("/api-status")
    public ResponseEntity<Map<String, Object>> getApiStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put("timestamp", LocalDateTime.now());
        
        // 检查各个API的状态
        Map<String, String> apiStatus = new HashMap<>();
        apiStatus.put("/api/points/total", "优化完成 - 已添加缓存");
        apiStatus.put("/api/points/today", "优化完成 - 使用数据库聚合查询");
        apiStatus.put("/api/tasks/today", "优化完成 - 已添加缓存和索引");
        apiStatus.put("/api/tasks/all", "优化完成 - 已添加分页查询");
        apiStatus.put("/api/exchange/items", "性能良好 - 使用索引查询");
        apiStatus.put("/api/exchange/items/admin", "需要分页 - 待优化");
        
        result.put("apiStatus", apiStatus);
        
        // 性能优化建议
        Map<String, String> optimizations = new HashMap<>();
        optimizations.put("数据库连接池", "已优化 - HikariCP配置");
        optimizations.put("查询缓存", "已启用 - Caffeine缓存");
        optimizations.put("SQL日志", "已关闭 - 提升性能");
        optimizations.put("数据库索引", "已添加 - 关键字段索引");
        optimizations.put("分页查询", "部分完成 - 大数据量接口已分页");
        
        result.put("optimizations", optimizations);
        
        return ResponseEntity.ok(result);
    }
}
