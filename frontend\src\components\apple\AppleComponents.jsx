import React from 'react';
import styled from 'styled-components';
import { AppleDesign, mediaQueries } from '../../styles/appleDesignSystem';

// 苹果风格按钮
export const AppleButton = styled.button`
  font-family: ${AppleDesign.typography.fontFamily.system};
  font-size: ${props => {
    switch(props.size) {
      case 'small': return AppleDesign.typography.fontSize.footnote;
      case 'large': return AppleDesign.typography.fontSize.headline;
      default: return AppleDesign.typography.fontSize.body;
    }
  }};
  font-weight: ${AppleDesign.typography.fontWeight.medium};
  
  height: ${props => {
    switch(props.size) {
      case 'small': return AppleDesign.components.button.small;
      case 'large': return AppleDesign.components.button.large;
      default: return AppleDesign.components.button.medium;
    }
  }};
  
  padding: 0 ${AppleDesign.spacing.md};
  border: none;
  border-radius: ${AppleDesign.borderRadius.lg};
  
  background: ${props => {
    switch(props.variant) {
      case 'primary': return AppleDesign.colors.systemBlue;
      case 'success': return AppleDesign.colors.systemGreen;
      case 'warning': return AppleDesign.colors.systemOrange;
      case 'danger': return AppleDesign.colors.systemRed;
      case 'secondary': return AppleDesign.colors.systemGray5;
      default: return AppleDesign.colors.systemBlue;
    }
  }};
  
  color: ${props => {
    switch(props.variant) {
      case 'secondary': return AppleDesign.colors.label;
      default: return 'white';
    }
  }};
  
  box-shadow: ${AppleDesign.shadows.button};
  cursor: pointer;
  transition: all ${AppleDesign.animation.duration.fast} ${AppleDesign.animation.easing.easeOut};
  
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${AppleDesign.spacing.sm};
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: ${AppleDesign.shadows.md};
    opacity: 0.9;
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: ${AppleDesign.shadows.sm};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: ${AppleDesign.shadows.button};
  }
  
  ${mediaQueries.touch} {
    &:hover {
      transform: none;
      opacity: 1;
    }
  }
`;

// 苹果风格卡片
export const AppleCard = styled.div`
  background: ${AppleDesign.colors.systemBackground};
  border-radius: ${AppleDesign.borderRadius.xl};
  box-shadow: ${AppleDesign.shadows.card};
  padding: ${props => props.large ? AppleDesign.components.card.paddingLarge : AppleDesign.components.card.padding};
  transition: all ${AppleDesign.animation.duration.normal} ${AppleDesign.animation.easing.easeOut};
  
  ${mediaQueries.mouse} {
    &:hover {
      box-shadow: ${AppleDesign.shadows.lg};
      transform: translateY(-2px);
    }
  }
`;

// 苹果风格输入框
export const AppleInput = styled.input`
  font-family: ${AppleDesign.typography.fontFamily.system};
  font-size: ${AppleDesign.typography.fontSize.body};
  
  width: 100%;
  height: ${props => {
    switch(props.size) {
      case 'small': return AppleDesign.components.input.small;
      case 'large': return AppleDesign.components.input.large;
      default: return AppleDesign.components.input.medium;
    }
  }};
  
  padding: 0 ${AppleDesign.spacing.md};
  border: 1px solid ${AppleDesign.colors.systemGray4};
  border-radius: ${AppleDesign.borderRadius.lg};
  background: ${AppleDesign.colors.systemBackground};
  color: ${AppleDesign.colors.label};
  
  transition: all ${AppleDesign.animation.duration.fast} ${AppleDesign.animation.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${AppleDesign.colors.systemBlue};
    box-shadow: 0 0 0 3px ${AppleDesign.colors.systemBlue}20;
  }
  
  &::placeholder {
    color: ${AppleDesign.colors.tertiaryLabel};
  }
`;

// 苹果风格标签
export const AppleLabel = styled.label`
  font-family: ${AppleDesign.typography.fontFamily.system};
  font-size: ${AppleDesign.typography.fontSize.subhead};
  font-weight: ${AppleDesign.typography.fontWeight.medium};
  color: ${AppleDesign.colors.label};
  margin-bottom: ${AppleDesign.spacing.sm};
  display: block;
`;

// 苹果风格标题
export const AppleTitle = styled.h1`
  font-family: ${AppleDesign.typography.fontFamily.system};
  font-size: ${props => {
    switch(props.level) {
      case 1: return AppleDesign.typography.fontSize.largeTitle;
      case 2: return AppleDesign.typography.fontSize.title1;
      case 3: return AppleDesign.typography.fontSize.title2;
      case 4: return AppleDesign.typography.fontSize.title3;
      default: return AppleDesign.typography.fontSize.title2;
    }
  }};
  font-weight: ${AppleDesign.typography.fontWeight.bold};
  color: ${AppleDesign.colors.label};
  margin: 0 0 ${AppleDesign.spacing.lg} 0;
  line-height: ${AppleDesign.typography.lineHeight.tight};
`;

// 苹果风格文本
export const AppleText = styled.p`
  font-family: ${AppleDesign.typography.fontFamily.system};
  font-size: ${props => {
    switch(props.size) {
      case 'small': return AppleDesign.typography.fontSize.footnote;
      case 'large': return AppleDesign.typography.fontSize.headline;
      default: return AppleDesign.typography.fontSize.body;
    }
  }};
  color: ${props => {
    switch(props.variant) {
      case 'secondary': return AppleDesign.colors.secondaryLabel;
      case 'tertiary': return AppleDesign.colors.tertiaryLabel;
      default: return AppleDesign.colors.label;
    }
  }};
  line-height: ${AppleDesign.typography.lineHeight.normal};
  margin: 0 0 ${AppleDesign.spacing.md} 0;
`;

// 苹果风格容器
export const AppleContainer = styled.div`
  max-width: ${props => {
    switch(props.size) {
      case 'small': return '600px';
      case 'large': return '1200px';
      case 'full': return '100%';
      default: return '800px';
    }
  }};
  margin: 0 auto;
  padding: 0 ${AppleDesign.spacing.md};
  
  ${mediaQueries.tablet} {
    padding: 0 ${AppleDesign.spacing.lg};
  }
  
  ${mediaQueries.desktop} {
    padding: 0 ${AppleDesign.spacing.xl};
  }
`;

// 苹果风格网格
export const AppleGrid = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || 'repeat(auto-fit, minmax(300px, 1fr))'};
  gap: ${AppleDesign.spacing.lg};
  
  ${mediaQueries.mobile} {
    grid-template-columns: 1fr;
    gap: ${AppleDesign.spacing.md};
  }
`;

// 苹果风格分隔线
export const AppleDivider = styled.hr`
  border: none;
  height: 1px;
  background: ${AppleDesign.colors.separator};
  margin: ${AppleDesign.spacing.lg} 0;
`;

// 苹果风格徽章
export const AppleBadge = styled.span`
  font-family: ${AppleDesign.typography.fontFamily.system};
  font-size: ${AppleDesign.typography.fontSize.caption1};
  font-weight: ${AppleDesign.typography.fontWeight.semibold};
  
  padding: ${AppleDesign.spacing.xs} ${AppleDesign.spacing.sm};
  border-radius: ${AppleDesign.borderRadius.full};
  
  background: ${props => {
    switch(props.variant) {
      case 'success': return AppleDesign.colors.systemGreen;
      case 'warning': return AppleDesign.colors.systemOrange;
      case 'danger': return AppleDesign.colors.systemRed;
      case 'info': return AppleDesign.colors.systemBlue;
      default: return AppleDesign.colors.systemGray;
    }
  }};
  
  color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
`;

// 苹果风格加载指示器
export const AppleSpinner = styled.div`
  width: ${props => props.size || '24px'};
  height: ${props => props.size || '24px'};
  border: 2px solid ${AppleDesign.colors.systemGray5};
  border-top: 2px solid ${AppleDesign.colors.systemBlue};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export default {
  AppleButton,
  AppleCard,
  AppleInput,
  AppleLabel,
  AppleTitle,
  AppleText,
  AppleContainer,
  AppleGrid,
  AppleDivider,
  AppleBadge,
  AppleSpinner
};
