#!/bin/bash

# CRS前端部署到nginx脚本
# 使用方法: sudo bash deploy-to-nginx.sh

echo "🚀 开始部署CRS前端到nginx..."
echo

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo权限运行此脚本"
    echo "使用方法: sudo bash deploy-to-nginx.sh"
    exit 1
fi

# 检查nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "❌ nginx未安装，请先安装nginx"
    echo "安装命令: sudo apt update && sudo apt install nginx"
    exit 1
fi

# 检查部署包是否存在
if [ ! -f "crs-frontend-deploy.zip" ]; then
    echo "❌ 部署包 crs-frontend-deploy.zip 不存在"
    echo "请先运行构建脚本生成部署包"
    exit 1
fi

echo "✅ 检查通过，开始部署..."
echo

# 1. 备份原配置
echo "📋 备份原nginx配置..."
cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)

# 2. 备份原网站文件
echo "📋 备份原网站文件..."
if [ -d "/var/www/html.backup" ]; then
    rm -rf /var/www/html.backup.old
    mv /var/www/html.backup /var/www/html.backup.old
fi
cp -r /var/www/html /var/www/html.backup

# 3. 清理原网站目录
echo "🧹 清理原网站目录..."
rm -rf /var/www/html/*

# 4. 解压新的前端文件
echo "📦 解压前端文件到 /var/www/html/..."
unzip -q crs-frontend-deploy.zip -d /var/www/html/

# 5. 设置文件权限
echo "🔐 设置文件权限..."
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/

# 6. 更新nginx配置
echo "⚙️  更新nginx配置..."
if [ -f "nginx-complete-config.conf" ]; then
    cp nginx-complete-config.conf /etc/nginx/sites-available/default
    echo "✅ nginx配置已更新"
else
    echo "⚠️  nginx-complete-config.conf 文件不存在，请手动配置"
fi

# 7. 测试nginx配置
echo "🧪 测试nginx配置..."
if nginx -t; then
    echo "✅ nginx配置测试通过"
else
    echo "❌ nginx配置测试失败，恢复备份配置"
    cp /etc/nginx/sites-available/default.backup.$(date +%Y%m%d)* /etc/nginx/sites-available/default
    exit 1
fi

# 8. 重启nginx
echo "🔄 重启nginx服务..."
systemctl restart nginx

if systemctl is-active --quiet nginx; then
    echo "✅ nginx服务重启成功"
else
    echo "❌ nginx服务重启失败"
    systemctl status nginx
    exit 1
fi

# 9. 检查部署结果
echo
echo "🎉 部署完成！"
echo
echo "📋 部署信息:"
echo "- 网站目录: /var/www/html/"
echo "- nginx配置: /etc/nginx/sites-available/default"
echo "- 访问地址: http://$(hostname -I | awk '{print $1}')/"
echo "- 后端API: http://*************:18080/api"
echo
echo "🔍 验证步骤:"
echo "1. 访问: http://$(hostname -I | awk '{print $1}')/"
echo "2. 测试路由: http://$(hostname -I | awk '{print $1}')/child/rewards"
echo "3. 检查API连接是否正常"
echo
echo "📊 nginx状态:"
systemctl status nginx --no-pager -l

echo
echo "📝 如果遇到问题:"
echo "- 查看nginx错误日志: sudo tail -f /var/log/nginx/crs_error.log"
echo "- 查看nginx访问日志: sudo tail -f /var/log/nginx/crs_access.log"
echo "- 恢复备份配置: sudo cp /var/www/html.backup/* /var/www/html/"