version: '3.8'

services:
  # 儿童奖励系统 - 一体化服务
  crs-system:
    build:
      context: .
      dockerfile: Dockerfile.all-in-one
    container_name: crs-system
    ports:
      - "8080:80"      # 前端和API统一入口
      - "18080:18080"  # 后端API直接访问 (可选)
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100
      - MYSQL_HOST=${MYSQL_HOST:-*************}
      - MYSQL_PORT=${MYSQL_PORT:-3307}
      - MYSQL_DATABASE=crs
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=123456
    volumes:
      - ./logs:/app/logs
      - ./supervisor-logs:/var/log/supervisor
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  crs-logs:
  supervisor-logs: