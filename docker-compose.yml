version: '3.8'

services:
  # 儿童奖励系统后端服务
  crs-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crs-backend
    ports:
      - "18080:18080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC
      - MYSQL_HOST=${MYSQL_HOST:-*************}
      - MYSQL_PORT=${MYSQL_PORT:-3307}
      - MYSQL_DATABASE=crs
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=123456
    volumes:
      - ./logs:/app/logs
    networks:
      - crs-net
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 (Nginx + 静态文件)
  crs-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: crs-frontend
    ports:
      - "8080:80"
    depends_on:
      - crs-backend
    networks:
      - crs-net
    restart: always

networks:
  crs-net:
    driver: bridge

volumes:
  crs-logs: