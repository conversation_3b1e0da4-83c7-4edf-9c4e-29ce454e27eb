//
//  PointRecordTimeFormatTests.swift
//  ChildRewardAppTests
//
//  Created by AI Assistant on 2024-07-29.
//

import XCTest
@testable import ChildRewardApp

class PointRecordTimeFormatTests: XCTestCase {
    
    func testTodayTimeFormat() {
        // 创建今天的时间
        let now = Date()
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        let todayTimeString = formatter.string(from: now)
        
        let record = PointRecord(
            id: 1,
            pointChange: 10,
            description: "测试任务",
            changeType: "TASK_COMPLETION",
            recordTime: todayTimeString,
            relatedTaskId: nil,
            relatedRewardId: nil
        )
        
        // 验证今天的时间格式包含"今天"
        XCTAssertTrue(record.formattedTime.contains("今天"))
        XCTAssertTrue(record.formattedDate.contains("今天"))
    }
    
    func testYesterdayTimeFormat() {
        // 创建昨天的时间
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        let yesterdayTimeString = formatter.string(from: yesterday)
        
        let record = PointRecord(
            id: 2,
            pointChange: -5,
            description: "测试惩罚",
            changeType: "TASK_PENALTY",
            recordTime: yesterdayTimeString,
            relatedTaskId: nil,
            relatedRewardId: nil
        )
        
        // 验证昨天的时间格式包含"昨天"
        XCTAssertTrue(record.formattedTime.contains("昨天"))
        XCTAssertTrue(record.formattedDate.contains("昨天"))
    }
    
    func testWeekTimeFormat() {
        // 创建本周内的时间（3天前）
        let threeDaysAgo = Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date()
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        let threeDaysAgoTimeString = formatter.string(from: threeDaysAgo)
        
        let record = PointRecord(
            id: 3,
            pointChange: 15,
            description: "测试奖励",
            changeType: "REWARD_EXCHANGE",
            recordTime: threeDaysAgoTimeString,
            relatedTaskId: nil,
            relatedRewardId: nil
        )
        
        // 验证本周内的时间格式包含星期几
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "EEEE"
        dayFormatter.locale = Locale(identifier: "zh_CN")
        let expectedDay = dayFormatter.string(from: threeDaysAgo)
        
        XCTAssertTrue(record.formattedTime.contains(expectedDay))
        XCTAssertTrue(record.formattedDate.contains(expectedDay))
    }
    
    func testOldTimeFormat() {
        // 创建一个月前的时间
        let oneMonthAgo = Calendar.current.date(byAdding: .month, value: -1, to: Date()) ?? Date()
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        let oneMonthAgoTimeString = formatter.string(from: oneMonthAgo)
        
        let record = PointRecord(
            id: 4,
            pointChange: 20,
            description: "旧记录",
            changeType: "MANUAL_ADJUST",
            recordTime: oneMonthAgoTimeString,
            relatedTaskId: nil,
            relatedRewardId: nil
        )
        
        // 验证较旧的时间使用完整日期格式
        let expectedFormat = DateFormatter.pointRecord.string(from: oneMonthAgo)
        XCTAssertEqual(record.formattedTime, expectedFormat)
        XCTAssertEqual(record.formattedDate, expectedFormat)
    }
    
    func testPointChangeFormatting() {
        let positiveRecord = PointRecord(
            id: 5,
            pointChange: 25,
            description: "正积分",
            changeType: "TASK_COMPLETION",
            recordTime: ISO8601DateFormatter().string(from: Date()),
            relatedTaskId: nil,
            relatedRewardId: nil
        )
        
        let negativeRecord = PointRecord(
            id: 6,
            pointChange: -10,
            description: "负积分",
            changeType: "TASK_PENALTY",
            recordTime: ISO8601DateFormatter().string(from: Date()),
            relatedTaskId: nil,
            relatedRewardId: nil
        )
        
        // 验证积分格式化
        XCTAssertEqual(positiveRecord.formattedPointChange, "+25")
        XCTAssertEqual(negativeRecord.formattedPointChange, "-10")
        
        XCTAssertTrue(positiveRecord.isPositive)
        XCTAssertFalse(negativeRecord.isPositive)
    }
}
