package com.example.childreward.repository;

import com.example.childreward.entity.ScheduledTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 计划任务（ScheduledTask）的JPA仓库接口
 */
public interface ScheduledTaskRepository extends JpaRepository<ScheduledTask, Long> {

    /**
     * 查找所有激活状态的计划任务
     * @return 激活的计划任务列表
     */
    List<ScheduledTask> findByActiveTrue();

    /**
     * 查找所有计划任务，按状态（已启用的放前面）和ID倒序排列
     * @return 排序后的计划任务列表
     */
    @Query("SELECT s FROM ScheduledTask s ORDER BY s.active DESC, s.id DESC")
    List<ScheduledTask> findAllOrderByActiveAndCreatedTime();

}