package com.example.childreward.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ScheduledTaskServiceTest {

    @Mock
    private TaskService taskService;

    @InjectMocks
    private ScheduledTaskService scheduledTaskService;

    @Test
    void processOverdueTasks_shouldCallTaskService() {
        // When
        scheduledTaskService.processOverdueTasks();

        // Then
        verify(taskService).processOverdueTasks();
    }
} 