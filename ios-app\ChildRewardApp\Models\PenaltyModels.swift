import Foundation
import SwiftUI

// MARK: - 惩罚项目
struct PenaltyItem: Identifiable, Codable {
    let id: Int
    let name: String
    let description: String
    let pointsReward: Int // 完成惩罚后获得的积分
    let iconName: String
    let colorHex: String
    let category: PenaltyCategory
    let difficulty: PenaltyDifficulty
    let estimatedMinutes: Int
    
    var color: Color {
        // 简化颜色处理，使用预定义颜色
        switch colorHex.lowercased() {
        case "#ff0000", "red": return .red
        case "#00ff00", "green": return .green
        case "#0000ff", "blue": return .blue
        case "#ffff00", "yellow": return .yellow
        case "#ff8000", "orange": return .orange
        case "#800080", "purple": return .purple
        case "#ffc0cb", "pink": return .pink
        case "#00ffff", "cyan": return .cyan
        default: return .red
        }
    }
}

// MARK: - 惩罚分类
enum PenaltyCategory: String, CaseIterable, Codable {
    case chores = "CHORES"           // 家务
    case study = "STUDY"             // 学习
    case behavior = "BEHAVIOR"       // 行为规范
    case exercise = "EXERCISE"       // 运动
    case restriction = "RESTRICTION" // 限制
    
    var displayName: String {
        switch self {
        case .chores: return "家务劳动"
        case .study: return "学习任务"
        case .behavior: return "行为规范"
        case .exercise: return "体能训练"
        case .restriction: return "权限限制"
        }
    }
    
    var iconName: String {
        switch self {
        case .chores: return "house"
        case .study: return "book"
        case .behavior: return "person.badge.clock"
        case .exercise: return "figure.run"
        case .restriction: return "hand.raised"
        }
    }
}

// MARK: - 惩罚难度
enum PenaltyDifficulty: String, CaseIterable, Codable {
    case easy = "EASY"
    case medium = "MEDIUM"
    case hard = "HARD"
    
    var displayName: String {
        switch self {
        case .easy: return "轻度"
        case .medium: return "中度"
        case .hard: return "重度"
        }
    }
    
    var color: Color {
        switch self {
        case .easy: return .orange
        case .medium: return .red
        case .hard: return .purple
        }
    }
    
    var pointsRange: ClosedRange<Int> {
        switch self {
        case .easy: return 5...15
        case .medium: return 15...30
        case .hard: return 30...50
        }
    }
}

// MARK: - 惩罚执行记录
struct PenaltyRecord: Identifiable, Codable {
    let id: Int
    let penalty: PenaltyItem
    let executedAt: String
    let completedAt: String?
    let status: PenaltyStatus
    let notes: String?
    let pointsAwarded: Int?
    
    var formattedExecutedDate: String {
        formatDate(executedAt)
    }
    
    var formattedCompletedDate: String? {
        guard let completedAt = completedAt else { return nil }
        return formatDate(completedAt)
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日 HH:mm"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return dateString
    }
}

// MARK: - 惩罚状态
enum PenaltyStatus: String, CaseIterable, Codable {
    case pending = "PENDING"       // 待执行
    case inProgress = "IN_PROGRESS" // 执行中
    case completed = "COMPLETED"   // 已完成
    case skipped = "SKIPPED"       // 已跳过
    case cancelled = "CANCELLED"   // 已取消
    
    var displayName: String {
        switch self {
        case .pending: return "待执行"
        case .inProgress: return "执行中"
        case .completed: return "已完成"
        case .skipped: return "已跳过"
        case .cancelled: return "已取消"
        }
    }
    
    var color: Color {
        switch self {
        case .pending: return .orange
        case .inProgress: return .blue
        case .completed: return .green
        case .skipped: return .gray
        case .cancelled: return .red
        }
    }
}

// 不再使用mock数据，所有惩罚数据从API获取
