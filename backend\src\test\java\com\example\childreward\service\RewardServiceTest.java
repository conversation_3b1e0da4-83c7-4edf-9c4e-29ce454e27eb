package com.example.childreward.service;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.RewardItem;
import com.example.childreward.entity.RewardPool;
import com.example.childreward.entity.RewardPool.PoolType;
import com.example.childreward.repository.RewardItemRepository;
import com.example.childreward.repository.RewardPoolRepository;
import com.example.childreward.service.impl.RewardServiceImpl;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RewardServiceTest {

    @Mock
    private RewardPoolRepository rewardPoolRepository;

    @Mock
    private RewardItemRepository rewardItemRepository;

    @Mock
    private PointService pointService;

    @InjectMocks
    private RewardServiceImpl rewardService;

    private RewardPool testRewardPool;
    private RewardPool testPenaltyPool;
    private RewardItem testRewardItem;
    private RewardItem testPenaltyItem;

    @BeforeEach
    void setUp() {
        testRewardPool = RewardPool.builder()
                .id(1L)
                .name("测试奖励池")
                .costPoints(50)
                .isEnabled(true)
                .poolType(PoolType.REWARD)
                .build();

        testPenaltyPool = RewardPool.builder()
                .id(2L)
                .name("测试惩罚池")
                .costPoints(20)
                .isEnabled(true)
                .poolType(PoolType.PENALTY)
                .build();

        testRewardItem = RewardItem.builder()
                .id(1L)
                .name("测试奖品")
                .probability(0.5f)
                .stock(10)
                .rewardPool(testRewardPool)
                .build();

        testPenaltyItem = RewardItem.builder()
                .id(2L)
                .name("测试惩罚")
                .probability(0.5f)
                .stock(10)
                .rewardPool(testPenaltyPool)
                .build();
    }

    @Test
    void createRewardPool_shouldSaveAndReturnPool() {
        // Given
        when(rewardPoolRepository.save(any(RewardPool.class))).thenReturn(testRewardPool);

        // When
        RewardPool result = rewardService.createRewardPool(testRewardPool);

        // Then
        assertEquals(testRewardPool, result);
        verify(rewardPoolRepository).save(testRewardPool);
    }

    @Test
    void updateRewardPool_shouldUpdateFieldsAndSave() {
        // Given
        RewardPool updatedPool = RewardPool.builder()
                .name("更新后的名称")
                .costPoints(100)
                .isEnabled(false)
                .poolType(PoolType.PENALTY)
                .build();

        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));
        when(rewardPoolRepository.save(any(RewardPool.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        RewardPool result = rewardService.updateRewardPool(1L, updatedPool);

        // Then
        assertEquals("更新后的名称", result.getName());
        assertEquals(100, result.getCostPoints());
        assertFalse(result.getIsEnabled());
        assertEquals(PoolType.PENALTY, result.getPoolType());
        verify(rewardPoolRepository).save(testRewardPool);
    }

    @Test
    void deleteRewardPool_shouldDeleteExistingPool() {
        // Given
        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));

        // When
        rewardService.deleteRewardPool(1L);

        // Then
        verify(rewardPoolRepository).delete(testRewardPool);
    }

    @Test
    void getAllEnabledPools_shouldReturnEnabledPools() {
        // Given
        List<RewardPool> enabledPools = Arrays.asList(testRewardPool, testPenaltyPool);
        when(rewardPoolRepository.findByIsEnabledTrue()).thenReturn(enabledPools);

        // When
        List<RewardPool> result = rewardService.getAllEnabledPools();

        // Then
        assertEquals(enabledPools, result);
    }

    @Test
    void getAllEnabledRewardPools_shouldReturnEnabledRewardPools() {
        // Given
        List<RewardPool> enabledRewardPools = Arrays.asList(testRewardPool);
        when(rewardPoolRepository.findByIsEnabledTrueAndPoolType(PoolType.REWARD)).thenReturn(enabledRewardPools);

        // When
        List<RewardPool> result = rewardService.getAllEnabledRewardPools();

        // Then
        assertEquals(enabledRewardPools, result);
    }

    @Test
    void getAllEnabledPenaltyPools_shouldReturnEnabledPenaltyPools() {
        // Given
        List<RewardPool> enabledPenaltyPools = Arrays.asList(testPenaltyPool);
        when(rewardPoolRepository.findByIsEnabledTrueAndPoolType(PoolType.PENALTY)).thenReturn(enabledPenaltyPools);

        // When
        List<RewardPool> result = rewardService.getAllEnabledPenaltyPools();

        // Then
        assertEquals(enabledPenaltyPools, result);
    }

    @Test
    void getRewardPoolById_shouldReturnPoolWhenExists() {
        // Given
        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));

        // When
        RewardPool result = rewardService.getRewardPoolById(1L);

        // Then
        assertEquals(testRewardPool, result);
    }

    @Test
    void getRewardPoolById_shouldThrowExceptionWhenNotExists() {
        // Given
        when(rewardPoolRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class, () -> rewardService.getRewardPoolById(99L));
    }

    @Test
    void addRewardItem_shouldSetPoolAndSave() {
        // Given
        RewardItem newItem = RewardItem.builder()
                .name("新奖品")
                .probability(0.3f)
                .stock(5)
                .build();

        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));
        when(rewardItemRepository.save(any(RewardItem.class))).thenReturn(newItem);

        // When
        RewardItem result = rewardService.addRewardItem(1L, newItem);

        // Then
        assertEquals(testRewardPool, newItem.getRewardPool());
        assertEquals(newItem, result);
        verify(rewardItemRepository).save(newItem);
    }

    @Test
    void updateRewardItem_shouldUpdateFieldsAndSave() {
        // Given
        RewardItem updatedItem = RewardItem.builder()
                .name("更新后的奖品")
                .probability(0.8f)
                .stock(15)
                .imageUrl("新图片URL")
                .build();

        when(rewardItemRepository.findById(1L)).thenReturn(Optional.of(testRewardItem));
        when(rewardItemRepository.save(any(RewardItem.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        RewardItem result = rewardService.updateRewardItem(1L, updatedItem);

        // Then
        assertEquals("更新后的奖品", result.getName());
        assertEquals(0.8f, result.getProbability());
        assertEquals(15, result.getStock());
        assertEquals("新图片URL", result.getImageUrl());
        verify(rewardItemRepository).save(testRewardItem);
    }

    @Test
    void deleteRewardItem_shouldDeleteExistingItem() {
        // Given
        when(rewardItemRepository.findById(1L)).thenReturn(Optional.of(testRewardItem));

        // When
        rewardService.deleteRewardItem(1L);

        // Then
        verify(rewardItemRepository).delete(testRewardItem);
    }

    @Test
    void getRewardItemById_shouldReturnItemWhenExists() {
        // Given
        when(rewardItemRepository.findById(1L)).thenReturn(Optional.of(testRewardItem));

        // When
        RewardItem result = rewardService.getRewardItemById(1L);

        // Then
        assertEquals(testRewardItem, result);
    }

    @Test
    void getRewardItemById_shouldThrowExceptionWhenNotExists() {
        // Given
        when(rewardItemRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class, () -> rewardService.getRewardItemById(99L));
    }

    @Test
    void drawReward_shouldDeductPointsAndReduceStock() {
        // Given
        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));
        when(pointService.getTotalPoints()).thenReturn(100);
        
        List<RewardItem> availableItems = Arrays.asList(testRewardItem);
        when(rewardItemRepository.findByRewardPoolAndStockGreaterThan(testRewardPool, 0)).thenReturn(availableItems);
        when(rewardItemRepository.save(any(RewardItem.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        RewardItem result = rewardService.drawReward(1L);

        // Then
        assertEquals(testRewardItem, result);
        assertEquals(9, testRewardItem.getStock());
        verify(pointService).deductPoints(eq(50), eq(PointRecord.ChangeType.REWARD_EXCHANGE), anyString(), eq(1L));
    }

    @Test
    void drawReward_shouldThrowExceptionWhenNotEnoughPoints() {
        // Given
        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));
        when(pointService.getTotalPoints()).thenReturn(30);

        // When & Then
        assertThrows(IllegalStateException.class, () -> rewardService.drawReward(1L));
    }

    @Test
    void drawReward_shouldThrowExceptionWhenNoAvailableItems() {
        // Given
        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));
        when(pointService.getTotalPoints()).thenReturn(100);
        when(rewardItemRepository.findByRewardPoolAndStockGreaterThan(testRewardPool, 0)).thenReturn(List.of());

        // When & Then
        assertThrows(IllegalStateException.class, () -> rewardService.drawReward(1L));
    }

    @Test
    void drawReward_shouldThrowExceptionWhenPoolIsNotRewardType() {
        // Given
        when(rewardPoolRepository.findById(2L)).thenReturn(Optional.of(testPenaltyPool));

        // When & Then
        assertThrows(IllegalStateException.class, () -> rewardService.drawReward(2L));
    }

    @Test
    void drawPenalty_shouldAddPointsAndReduceStock() {
        // Given
        when(rewardPoolRepository.findById(2L)).thenReturn(Optional.of(testPenaltyPool));
        
        List<RewardItem> availableItems = Arrays.asList(testPenaltyItem);
        when(rewardItemRepository.findByRewardPoolAndStockGreaterThan(testPenaltyPool, 0)).thenReturn(availableItems);
        when(rewardItemRepository.save(any(RewardItem.class))).thenAnswer(i -> i.getArguments()[0]);

        // When
        RewardItem result = rewardService.drawPenalty(2L);

        // Then
        assertEquals(testPenaltyItem, result);
        assertEquals(9, testPenaltyItem.getStock());
        verify(pointService).addPoints(eq(20), eq(PointRecord.ChangeType.TASK_PENALTY), anyString(), eq(2L));
    }

    @Test
    void drawPenalty_shouldThrowExceptionWhenNoAvailableItems() {
        // Given
        when(rewardPoolRepository.findById(2L)).thenReturn(Optional.of(testPenaltyPool));
        when(rewardItemRepository.findByRewardPoolAndStockGreaterThan(testPenaltyPool, 0)).thenReturn(List.of());

        // When & Then
        assertThrows(IllegalStateException.class, () -> rewardService.drawPenalty(2L));
    }

    @Test
    void drawPenalty_shouldThrowExceptionWhenPoolIsNotPenaltyType() {
        // Given
        when(rewardPoolRepository.findById(1L)).thenReturn(Optional.of(testRewardPool));

        // When & Then
        assertThrows(IllegalStateException.class, () -> rewardService.drawPenalty(1L));
    }
} 