/**
 * 苹果风格儿童端主页
 * 完全重新设计，基于苹果设计标准
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import apiService, { childApi, pointApi } from '../../api/apiService';
import {
  ChildContainer,
  ChildHeader,
  ChildTitle,
  ChildSubtitle,
  ChildGrid,
  ChildCard,
  ChildCardTitle,
  ChildCardContent,
  ChildButton,
  ChildTabBar,
  ChildTabItem,
  ChildBadge
} from '../../components/child/ChildAppleUI';
import { ApplePageTransition } from '../../components/apple/AppleAnimations';

const ChildDashboardNew = () => {
  const navigate = useNavigate();
  const [greeting, setGreeting] = useState('');
  const [stats, setStats] = useState({
    totalPoints: 0,
    todayTasks: 0,
    completedTasks: 0
  });
  const [recentTasks, setRecentTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setGreeting(getTimeBasedGreeting());
    fetchDashboardData();
  }, []);

  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  // 转换任务状态
  const getTaskStatus = (status) => {
    if (!status) return 'pending';
    const upperStatus = status.toString().toUpperCase();
    if (['COMPLETED', 'APPROVED', 'COMPLETE'].includes(upperStatus)) {
      return 'completed';
    }
    if (['PENDING', 'WAITING', 'IN_PROGRESS'].includes(upperStatus)) {
      return 'pending';
    }
    return 'pending';
  };

  // 格式化任务时间
  const formatTaskTime = (timeStr) => {
    if (!timeStr) return '未知时间';

    try {
      const taskTime = new Date(timeStr);
      const now = new Date();
      const diffMs = now - taskTime;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffDays === 0) return '今天';
      if (diffDays === 1) return '昨天';
      if (diffDays <= 7) return `${diffDays}天前`;
      return taskTime.toLocaleDateString();
    } catch (error) {
      return '未知时间';
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // 获取今日任务
      const todayTasksResponse = await childApi.getTodayTasks();
      const todayTasks = todayTasksResponse.data || [];

      // 获取过去任务（用于显示最近任务）
      const pastTasksResponse = await childApi.getPastTasks();
      const pastTasks = pastTasksResponse.data || [];

      // 获取积分记录
      const pointsResponse = await pointApi.getRecentPointChanges(7);
      const pointsData = pointsResponse.data || [];

      // 计算统计数据
      const totalPoints = pointsData.reduce((sum, record) => sum + (record.points || 0), 0);
      const completedTasks = [...todayTasks, ...pastTasks].filter(task =>
        task.status && ['COMPLETED', 'APPROVED', 'COMPLETE'].includes(task.status.toUpperCase())
      );

      setStats({
        totalPoints: Math.max(0, totalPoints),
        todayTasks: todayTasks.length,
        completedTasks: completedTasks.length
      });

      // 合并今日任务和过去任务，取最近的几个
      const allTasks = [...todayTasks, ...pastTasks];
      const recentTasksData = allTasks
        .sort((a, b) => new Date(b.createdAt || b.createTime || 0) - new Date(a.createdAt || a.createTime || 0))
        .slice(0, 5)
        .map(task => ({
          id: task.id,
          title: task.title || task.taskName || '未知任务',
          points: task.points || task.score || 0,
          status: getTaskStatus(task.status),
          time: formatTaskTime(task.createdAt || task.createTime)
        }));

      setRecentTasks(recentTasksData);

    } catch (error) {
      console.error('获取数据失败:', error);
      // 设置默认值避免页面崩溃
      setStats({
        totalPoints: 0,
        todayTasks: 0,
        completedTasks: 0
      });
      setRecentTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 功能函数
  const showTaskList = async () => {
    try {
      const response = await childApi.getTodayTasks();
      const tasks = response.data || [];

      if (tasks.length === 0) {
        alert('📝 任务列表\n\n今天还没有任务哦！\n\n可以让爸爸妈妈给你安排一些任务～');
        return;
      }

      const taskList = tasks.map(task =>
        `• ${task.title || task.taskName} (${task.points || task.score || 0}积分) - ${getTaskStatusText(getTaskStatus(task.status))}`
      ).join('\n');

      alert(`📝 今日任务列表\n\n${taskList}\n\n点击任务可以标记完成！`);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      alert('📝 获取任务列表失败\n\n请检查网络连接后重试');
    }
  };

  const showRewards = async () => {
    try {
      const response = await childApi.getAvailableRewardPools();
      const rewards = response.data || [];

      if (rewards.length === 0) {
        alert('🎁 奖励中心\n\n暂时没有可用的奖励哦！\n\n继续完成任务赚取积分吧～');
        return;
      }

      const rewardList = rewards.map(reward =>
        `• ${reward.name} (${reward.requiredPoints || 0}积分)`
      ).join('\n');

      alert(`🎁 奖励中心\n\n可兑换奖励：\n${rewardList}\n\n你现在有${stats.totalPoints}积分！`);
    } catch (error) {
      console.error('获取奖励列表失败:', error);
      alert('🎁 获取奖励列表失败\n\n请检查网络连接后重试');
    }
  };

  const showSpinGame = async () => {
    try {
      // 获取可用的奖励池
      const response = await childApi.getAvailableRewardPools();
      const pools = response.data || [];

      if (pools.length === 0) {
        alert('🎯 幸运转盘\n\n暂时没有可用的奖励池！\n\n请联系爸爸妈妈设置奖励～');
        return;
      }

      // 使用第一个奖励池进行抽奖
      const poolId = pools[0].id;
      const drawResponse = await childApi.drawReward(poolId);

      if (drawResponse.data && drawResponse.data.reward) {
        const reward = drawResponse.data.reward;
        alert(`🎯 幸运转盘\n\n转盘转动中...\n\n🎉 恭喜你获得：${reward.name}！\n\n奖励已添加到你的账户～`);
      } else {
        alert('🎯 幸运转盘\n\n很遗憾，这次没有中奖！\n\n明天再来试试吧～');
      }
    } catch (error) {
      console.error('抽奖失败:', error);
      alert('🎯 抽奖失败\n\n请检查网络连接后重试');
    }
  };

  const showProgress = async () => {
    try {
      const [tasksResponse, pointsResponse] = await Promise.all([
        childApi.getTodayTasks(),
        pointApi.getRecentPointChanges(7)
      ]);

      const tasks = tasksResponse.data || [];
      const points = pointsResponse.data || [];

      const completedCount = tasks.filter(task =>
        ['COMPLETED', 'APPROVED', 'COMPLETE'].includes((task.status || '').toUpperCase())
      ).length;

      const totalPoints = points.reduce((sum, record) => sum + (record.points || 0), 0);

      alert(`📊 成长记录\n\n本周表现：\n• 完成任务：${completedCount}/${tasks.length}\n• 获得积分：${totalPoints}\n• 任务完成率：${tasks.length > 0 ? Math.round(completedCount / tasks.length * 100) : 0}%\n\n继续加油，你很棒！`);
    } catch (error) {
      console.error('获取成长记录失败:', error);
      alert('📊 获取成长记录失败\n\n请检查网络连接后重试');
    }
  };

  const handleTaskClick = async (task) => {
    if (task.status === 'completed') {
      alert(`✅ 任务已完成\n\n${task.title}\n获得积分：+${task.points}\n完成时间：${task.time}\n\n太棒了！`);
    } else {
      const confirm = window.confirm(`📝 ${task.title}\n\n这个任务还没完成哦！\n完成后可以获得 ${task.points} 积分\n\n现在就去完成吗？`);
      if (confirm) {
        try {
          // 先开始任务，再完成任务
          await childApi.startTask(task.id);
          await childApi.completeTask(task.id);

          alert('🎉 任务完成！\n\n恭喜你完成了任务！\n积分 +' + task.points + '\n\n继续加油！');

          // 刷新数据
          fetchDashboardData();
        } catch (error) {
          console.error('完成任务失败:', error);
          alert('❌ 任务完成失败\n\n请检查网络连接后重试');
        }
      }
    }
  };

  const quickActions = [
    {
      title: '我的任务',
      description: '查看今天的任务',
      icon: '📝',
      color: 'primary',
      action: () => showTaskList(),
      badge: stats.todayTasks
    },
    {
      title: '奖励中心',
      description: '兑换心仪的奖励',
      icon: '🎁',
      color: 'secondary',
      action: () => showRewards()
    },
    {
      title: '转盘游戏',
      description: '幸运转盘抽奖',
      icon: '🎯',
      color: 'success',
      action: () => showSpinGame()
    },
    {
      title: '成长记录',
      description: '查看我的进步',
      icon: '📊',
      color: 'info',
      action: () => showProgress()
    }
  ];

  const tabItems = [
    { id: 'home', icon: '🏠', label: '首页', path: '/child' },
    { id: 'profile', icon: '👶', label: '我的', path: '/child/profile' },
    { id: 'games', icon: '🎮', label: '游戏', path: '/child/games' }
  ];

  const getTaskStatusColor = (status) => {
    switch (status) {
      case 'completed': return AppleDesignSystem.colors.semantic.systemGreen;
      case 'pending': return AppleDesignSystem.colors.semantic.systemOrange;
      default: return AppleDesignSystem.colors.semantic.label;
    }
  };

  const getTaskStatusText = (status) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'pending': return '进行中';
      default: return '未开始';
    }
  };

  if (loading) {
    return (
      <ChildContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          flexDirection: 'column',
          gap: AppleDesignSystem.spacing.md
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: `4px solid ${AppleDesignSystem.colors.semantic.systemFill}`,
            borderTop: `4px solid ${AppleDesignSystem.colors.child.primary}`,
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div style={{
            fontSize: AppleDesignSystem.typography.textStyles.headline.fontSize,
            color: AppleDesignSystem.colors.semantic.secondaryLabel
          }}>
            正在加载...
          </div>
        </div>
        
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </ChildContainer>
    );
  }

  return (
    <ApplePageTransition>
      <ChildContainer>
        {/* 头部问候 */}
        <ChildHeader>
          <div>
            <ChildTitle>{greeting}，小朋友！</ChildTitle>
            <ChildSubtitle>今天也要加油哦～ 你现在有 {stats.totalPoints} 积分</ChildSubtitle>
          </div>
          <div style={{
            fontSize: '48px',
            background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
            borderRadius: '50%',
            width: '64px',
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 4px 15px rgba(255, 107, 107, 0.3)'
          }}>
            👶
          </div>
        </ChildHeader>

        {/* 积分统计 */}
        <ChildGrid>
          <ChildCard style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '36px', marginBottom: '8px' }}>💰</div>
              <ChildCardTitle style={{ color: 'white', margin: 0 }}>
                {stats.totalPoints}
              </ChildCardTitle>
              <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
                总积分
              </ChildCardContent>
            </div>
          </ChildCard>

          <ChildCard style={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '36px', marginBottom: '8px' }}>📝</div>
              <ChildCardTitle style={{ color: 'white', margin: 0 }}>
                {stats.todayTasks}
              </ChildCardTitle>
              <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
                今日任务
              </ChildCardContent>
            </div>
          </ChildCard>

          <ChildCard style={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '36px', marginBottom: '8px' }}>✅</div>
              <ChildCardTitle style={{ color: 'white', margin: 0 }}>
                {stats.completedTasks}
              </ChildCardTitle>
              <ChildCardContent style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>
                已完成
              </ChildCardContent>
            </div>
          </ChildCard>
        </ChildGrid>

        {/* 快捷操作 */}
        <ChildCard fullWidth>
          <ChildCardTitle>
            🚀 快捷操作
          </ChildCardTitle>
          <ChildCardContent>
            <ChildGrid>
              {quickActions.map((action, index) => (
                <ChildCard
                  key={index}
                  interactive
                  onClick={() => action.action && action.action()}
                  style={{ cursor: 'pointer', position: 'relative' }}
                >
                  {action.badge > 0 && (
                    <ChildBadge 
                      style={{
                        position: 'absolute',
                        top: AppleDesignSystem.spacing.sm,
                        right: AppleDesignSystem.spacing.sm
                      }}
                    >
                      {action.badge}
                    </ChildBadge>
                  )}
                  <div style={{ 
                    fontSize: '48px', 
                    marginBottom: AppleDesignSystem.spacing.md,
                    textAlign: 'center'
                  }}>
                    {action.icon}
                  </div>
                  <ChildCardTitle style={{ margin: 0, textAlign: 'center' }}>
                    {action.title}
                  </ChildCardTitle>
                  <ChildCardContent style={{ textAlign: 'center', margin: 0 }}>
                    {action.description}
                  </ChildCardContent>
                </ChildCard>
              ))}
            </ChildGrid>
          </ChildCardContent>
        </ChildCard>

        {/* 最近任务 */}
        <ChildCard fullWidth>
          <ChildCardTitle>
            📋 最近任务
          </ChildCardTitle>
          <ChildCardContent>
            {recentTasks.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: AppleDesignSystem.spacing.xl,
                color: AppleDesignSystem.colors.semantic.secondaryLabel
              }}>
                <div style={{ fontSize: '48px', marginBottom: AppleDesignSystem.spacing.md }}>📝</div>
                <div>暂无任务</div>
              </div>
            ) : (
              <div style={{ display: 'grid', gap: AppleDesignSystem.spacing.md }}>
                {recentTasks.map((task) => (
                  <div
                    key={task.id}
                    onClick={() => handleTaskClick(task)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: AppleDesignSystem.spacing.md,
                      padding: AppleDesignSystem.spacing.md,
                      background: AppleDesignSystem.colors.semantic.systemFill,
                      borderRadius: AppleDesignSystem.borderRadius.medium,
                      transition: 'all 0.2s ease',
                      cursor: 'pointer'
                    }}
                  >
                    <div style={{
                      fontSize: '24px',
                      width: '40px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: getTaskStatusColor(task.status),
                      borderRadius: AppleDesignSystem.borderRadius.small,
                      color: 'white'
                    }}>
                      {task.status === 'completed' ? '✅' : '📝'}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: AppleDesignSystem.typography.textStyles.subheadline.fontSize,
                        fontWeight: '600',
                        color: AppleDesignSystem.colors.semantic.label,
                        marginBottom: '2px'
                      }}>
                        {task.title}
                      </div>
                      <div style={{
                        fontSize: AppleDesignSystem.typography.textStyles.footnote.fontSize,
                        color: getTaskStatusColor(task.status)
                      }}>
                        {getTaskStatusText(task.status)} • {task.time}
                      </div>
                    </div>
                    <div style={{
                      fontSize: AppleDesignSystem.typography.textStyles.subheadline.fontSize,
                      fontWeight: '600',
                      color: AppleDesignSystem.colors.semantic.systemBlue
                    }}>
                      +{task.points}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ChildCardContent>
        </ChildCard>

        {/* 底部标签栏 */}
        <ChildTabBar>
          {tabItems.map((item) => (
            <ChildTabItem
              key={item.id}
              active={item.path === '/child'}
              onClick={() => navigate(item.path)}
            >
              <div style={{ fontSize: '24px', marginBottom: '4px' }}>
                {item.icon}
              </div>
              <div style={{ fontSize: '12px' }}>
                {item.label}
              </div>
            </ChildTabItem>
          ))}
        </ChildTabBar>
      </ChildContainer>
    </ApplePageTransition>
  );
};

export default ChildDashboardNew;
