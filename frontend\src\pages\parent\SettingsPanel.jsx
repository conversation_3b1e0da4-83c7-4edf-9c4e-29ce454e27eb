import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { configApi, pointApi } from '../../api/apiService';

const SettingsPanel = ({ onLogout }) => {
  const [settings, setSettings] = useState({
    penaltyThreshold: 3, // 惩罚阈值 - 每日未完成任务超过多少个触发惩罚
    penaltyPoints: 10, // 每次惩罚扣除积分
    overduePenalty: true, // 是否启用过期任务惩罚
    delayedTaskPenalty: {
      enabled: true,
      // 超时系数设置
      penalties: [
        { threshold: 1.2, factor: 0.9 }, // 超过预计时间20%，获得90%积分
        { threshold: 1.5, factor: 0.8 }, // 超过预计时间50%，获得80%积分
        { threshold: 2.0, factor: 0.6 }, // 超过预计时间100%，获得60%积分
        { threshold: 2.5, factor: 0.5 }  // 超过预计时间150%，获得50%积分
      ]
    },
    notificationsEnabled: true, // 是否启用通知
    finishReminder: true, // 任务即将到期提醒
    reminderMinutes: 30, // 提前多少分钟提醒
    automaticApproval: false, // 自动审批
    pinCode: '0907', // PIN码
    language: 'zh-CN', // 语言设置
    parentPin: "0907",
    childPin: "9876",
    taskExpiredPenalty: 5,
    requiredTaskMultiplier: 1.5,
    bonusRewardProbability: 10,
    pointLimits: {
      dailyMax: 50,
      overdraftAllowed: true,
      overdraftLimit: 20
    },
    notifications: {
      taskReminder: true,
      approvalNeeded: true,
      pointsEarned: true,
      overdraftAlert: true,
    },
  });
  const [loading, setLoading] = useState(true);

  // 获取系统配置
  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        // 获取所有系统配置
        const response = await configApi.getAllConfigs();
        const configs = response.data;
        
        // 初始化设置对象
        const newSettings = { ...settings };
        
        // 处理配置数据
        configs.forEach(config => {
          switch (config.configKey) {
            case 'PARENT_PIN':
              newSettings.parentPin = config.configValue;
              break;
            case 'CHILD_PIN':
              newSettings.childPin = config.configValue;
              break;
            case 'PENALTY_THRESHOLD':
              newSettings.penaltyThreshold = parseInt(config.configValue);
              break;
            case 'PENALTY_POINTS':
              newSettings.penaltyPoints = parseInt(config.configValue);
              break;
            case 'TASK_EXPIRED_PENALTY':
              newSettings.taskExpiredPenalty = parseInt(config.configValue);
              break;
            case 'REQUIRED_TASK_MULTIPLIER':
              newSettings.requiredTaskMultiplier = parseFloat(config.configValue);
              break;
            case 'OVERDUE_PENALTY_ENABLED':
              newSettings.overduePenalty = config.configValue === 'true';
              break;
            case 'DELAYED_TASK_PENALTY_ENABLED':
              newSettings.delayedTaskPenalty.enabled = config.configValue === 'true';
              break;
            case 'BONUS_REWARD_PROBABILITY':
              newSettings.bonusRewardProbability = parseInt(config.configValue);
              break;
            case 'DAILY_MAX_POINTS':
              newSettings.pointLimits.dailyMax = parseInt(config.configValue);
              break;
            case 'OVERDRAFT_ALLOWED':
              newSettings.pointLimits.overdraftAllowed = config.configValue === 'true';
              break;
            case 'OVERDRAFT_LIMIT':
              newSettings.pointLimits.overdraftLimit = parseInt(config.configValue);
              break;
            case 'NOTIFICATION_TASK_REMINDER':
              newSettings.notifications.taskReminder = config.configValue === 'true';
              break;
            case 'NOTIFICATION_APPROVAL_NEEDED':
              newSettings.notifications.approvalNeeded = config.configValue === 'true';
              break;
            case 'NOTIFICATION_POINTS_EARNED':
              newSettings.notifications.pointsEarned = config.configValue === 'true';
              break;
            case 'NOTIFICATION_OVERDRAFT_ALERT':
              newSettings.notifications.overdraftAlert = config.configValue === 'true';
              break;
            default:
              // 忽略未知配置
          }
        });
        
        setSettings(newSettings);
      } catch (error) {
        console.error('获取系统配置失败:', error);
        // 保持默认值
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleInputChange = (e, category, subCategory = null) => {
    const { name, value, type, checked } = e.target;
    
    if (subCategory) {
      setSettings({
        ...settings,
        [category]: {
          ...settings[category],
          [subCategory]: {
            ...settings[category][subCategory],
            [name]: type === 'checkbox' ? checked : 
                   type === 'number' ? parseFloat(value) : value,
          }
        }
      });
    } else if (category) {
      setSettings({
        ...settings,
        [category]: {
          ...settings[category],
          [name]: type === 'checkbox' ? checked : 
                 type === 'number' ? parseFloat(value) : value,
        }
      });
    } else {
      setSettings({
        ...settings,
        [name]: type === 'checkbox' ? checked : 
               type === 'number' ? parseFloat(value) : value,
      });
    }
  };

  const handleNestedChange = (parent, name, value) => {
    setSettings({
      ...settings,
      [parent]: {
        ...settings[parent],
        [name]: value
      }
    });
  };

  const handlePenaltyChange = (index, field, value) => {
    const newPenalties = [...settings.delayedTaskPenalty.penalties];
    newPenalties[index] = { ...newPenalties[index], [field]: parseFloat(value) };
    setSettings({
      ...settings,
      delayedTaskPenalty: {
        ...settings.delayedTaskPenalty,
        penalties: newPenalties
      }
    });
  };

  const addPenalty = () => {
    const newPenalties = [...settings.delayedTaskPenalty.penalties];
    const lastPenalty = newPenalties[newPenalties.length - 1];
    newPenalties.push({
      threshold: lastPenalty.threshold + 0.5,
      factor: lastPenalty.factor - 0.1 > 0 ? lastPenalty.factor - 0.1 : 0.1
    });
    setSettings({
      ...settings,
      delayedTaskPenalty: {
        ...settings.delayedTaskPenalty,
        penalties: newPenalties
      }
    });
  };

  const removePenalty = (index) => {
    if (settings.delayedTaskPenalty.penalties.length <= 1) return;
    
    const newPenalties = [...settings.delayedTaskPenalty.penalties];
    newPenalties.splice(index, 1);
    setSettings({
      ...settings,
      delayedTaskPenalty: {
        ...settings.delayedTaskPenalty,
        penalties: newPenalties
      }
    });
  };

  const handlePinChange = (e, pinType) => {
    const value = e.target.value;
    // 只允许输入数字，且长度为4
    if (/^\d{0,4}$/.test(value)) {
      setSettings({
        ...settings,
        [pinType]: value,
      });
    }
  };

  const handleSaveSettings = async () => {
    try {
      // 构建配置更新请求
      const updatePromises = [
        configApi.updateConfig('PARENT_PIN', settings.parentPin, '家长PIN码'),
        configApi.updateConfig('CHILD_PIN', settings.childPin, '儿童PIN码'),
        configApi.updateConfig('PENALTY_THRESHOLD', settings.penaltyThreshold.toString(), '任务惩罚阈值'),
        configApi.updateConfig('PENALTY_POINTS', settings.penaltyPoints.toString(), '惩罚扣除积分'),
        configApi.updateConfig('TASK_EXPIRED_PENALTY', settings.taskExpiredPenalty.toString(), '任务过期惩罚分值'),
        configApi.updateConfig('OVERDUE_PENALTY_ENABLED', settings.overduePenalty.toString(), '是否启用过期任务惩罚'),
        configApi.updateConfig('DELAYED_TASK_PENALTY_ENABLED', settings.delayedTaskPenalty.enabled.toString(), '超时任务积分递减'),
        configApi.updateConfig('REQUIRED_TASK_MULTIPLIER', settings.requiredTaskMultiplier.toString(), '必做任务乘数'),
        configApi.updateConfig('BONUS_REWARD_PROBABILITY', settings.bonusRewardProbability.toString(), '奖励概率加成'),
        configApi.updateConfig('DAILY_MAX_POINTS', settings.pointLimits.dailyMax.toString(), '每日最大积分'),
        configApi.updateConfig('OVERDRAFT_ALLOWED', settings.pointLimits.overdraftAllowed.toString(), '允许透支积分'),
        configApi.updateConfig('OVERDRAFT_LIMIT', settings.pointLimits.overdraftLimit.toString(), '积分透支限额'),
        configApi.updateConfig('NOTIFICATION_TASK_REMINDER', settings.notifications.taskReminder.toString(), '任务提醒通知'),
        configApi.updateConfig('NOTIFICATION_APPROVAL_NEEDED', settings.notifications.approvalNeeded.toString(), '审批提醒通知'),
        configApi.updateConfig('NOTIFICATION_POINTS_EARNED', settings.notifications.pointsEarned.toString(), '积分获得通知'),
        configApi.updateConfig('NOTIFICATION_OVERDRAFT_ALERT', settings.notifications.overdraftAlert.toString(), '透支提醒通知'),
      ];
      
      // 执行所有更新
      await Promise.all(updatePromises);
      
      alert('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存设置失败，请稍后再试');
    }
  };

  const handleResetPoints = async () => {
    if (window.confirm('确定要清零所有积分吗？此操作无法撤销！')) {
      try {
        // 获取当前总积分
        const response = await pointApi.getTotalPoints();
        const currentPoints = response.data.totalPoints;
        
        // 如果当前积分大于0，进行扣减
        if (currentPoints > 0) {
          await pointApi.deductPoints(currentPoints, '系统管理员重置积分');
        } 
        // 如果当前积分小于0，进行添加
        else if (currentPoints < 0) {
          await pointApi.addPoints(Math.abs(currentPoints), '系统管理员重置积分');
        }
        
        alert('所有积分已重置为零');
      } catch (error) {
        console.error('重置积分失败:', error);
        alert('重置积分失败，请稍后再试');
      }
    }
  };

  return (
    <SettingsContainer>
      <Header>
        <Title>系统设置</Title>
        <SaveButton 
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleSaveSettings}
        >
          保存设置
        </SaveButton>
      </Header>

      <SettingsGrid>
        <SettingsCard>
          <CardHeader>
            <CardTitle>任务与惩罚设置</CardTitle>
          </CardHeader>
          <CardBody>
            <FormGroup>
              <Label htmlFor="penaltyThreshold">任务惩罚阈值</Label>
              <FormRow>
                <Input 
                  type="number" 
                  id="penaltyThreshold" 
                  name="penaltyThreshold"
                  min="1" 
                  max="10" 
                  value={settings.penaltyThreshold}
                  onChange={(e) => handleInputChange(e)}
                />
                <InputDescription>
                  每日未完成任务超过此数量将触发自动扣分
                </InputDescription>
              </FormRow>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="penaltyPoints">惩罚扣除积分</Label>
              <FormRow>
                <Input 
                  type="number" 
                  id="penaltyPoints" 
                  name="penaltyPoints"
                  min="1" 
                  max="100" 
                  value={settings.penaltyPoints}
                  onChange={(e) => handleInputChange(e)}
                />
                <InputDescription>
                  每次触发惩罚时扣除的积分数量
                </InputDescription>
              </FormRow>
            </FormGroup>

            <FormGroup>
              <CheckboxContainer>
                <Checkbox 
                  type="checkbox" 
                  id="overduePenalty" 
                  name="overduePenalty"
                  checked={settings.overduePenalty}
                  onChange={(e) => handleInputChange(e)}
                />
                <CheckboxLabel htmlFor="overduePenalty">过期任务惩罚</CheckboxLabel>
              </CheckboxContainer>
              <InputDescription>
                启用后，未在规定时间内完成的任务将不计积分
              </InputDescription>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="taskExpiredPenalty">任务过期惩罚分值</Label>
              <FormRow>
                <Input
                  type="number"
                  id="taskExpiredPenalty"
                  name="taskExpiredPenalty"
                  min="0"
                  max="50"
                  value={settings.taskExpiredPenalty}
                  onChange={(e) => handleInputChange(e)}
                />
                <InputDescription>未完成任务的扣分值（未按时完成必做任务）</InputDescription>
              </FormRow>
            </FormGroup>

            <FormGroup>
              <CheckboxContainer>
                <Checkbox 
                  type="checkbox" 
                  id="delayedTaskPenaltyEnabled" 
                  checked={settings.delayedTaskPenalty.enabled}
                  onChange={(e) => handleNestedChange('delayedTaskPenalty', 'enabled', e.target.checked)}
                />
                <CheckboxLabel htmlFor="delayedTaskPenaltyEnabled">超时任务积分递减</CheckboxLabel>
              </CheckboxContainer>
              <InputDescription>
                启用后，超时完成的任务将按照下方系数计算积分
              </InputDescription>
            </FormGroup>

            {settings.delayedTaskPenalty.enabled && (
              <PenaltyTable>
                <thead>
                  <tr>
                    <th>超时倍率</th>
                    <th>积分系数</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {settings.delayedTaskPenalty.penalties.map((penalty, index) => (
                    <tr key={index}>
                      <td>
                        <Input 
                          type="number"
                          step="0.1"
                          min="1.0"
                          max="5.0"
                          value={penalty.threshold}
                          onChange={(e) => handlePenaltyChange(index, 'threshold', e.target.value)}
                        />
                      </td>
                      <td>
                        <Input 
                          type="number"
                          step="0.1"
                          min="0.1"
                          max="1.0"
                          value={penalty.factor}
                          onChange={(e) => handlePenaltyChange(index, 'factor', e.target.value)}
                        />
                      </td>
                      <td>
                        <RemoveButton 
                          onClick={() => removePenalty(index)}
                          disabled={settings.delayedTaskPenalty.penalties.length <= 1}
                        >
                          删除
                        </RemoveButton>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3">
                      <AddButton onClick={addPenalty}>
                        + 添加超时规则
                      </AddButton>
                    </td>
                  </tr>
                </tfoot>
              </PenaltyTable>
            )}
          </CardBody>
        </SettingsCard>

        <SettingsCard>
          <CardHeader>
            <CardTitle>积分与奖励设置</CardTitle>
          </CardHeader>
          <CardBody>
            <FormGroup>
              <Label htmlFor="requiredTaskMultiplier">必做任务乘数</Label>
              <FormRow>
                <Input
                  type="number"
                  id="requiredTaskMultiplier"
                  name="requiredTaskMultiplier"
                  min="1"
                  max="3"
                  step="0.1"
                  value={settings.requiredTaskMultiplier}
                  onChange={(e) => handleInputChange(e)}
                />
                <InputDescription>必做任务积分的奖励乘数</InputDescription>
              </FormRow>
            </FormGroup>
            <FormGroup>
              <Label htmlFor="bonusRewardProbability">奖励概率加成</Label>
              <FormRow>
                <Input
                  type="number"
                  id="bonusRewardProbability"
                  name="bonusRewardProbability"
                  min="0"
                  max="100"
                  value={settings.bonusRewardProbability}
                  onChange={(e) => handleInputChange(e)}
                />
                <InputDescription>增加抽到稀有奖品的概率（百分比）</InputDescription>
              </FormRow>
            </FormGroup>
            <FormGroup>
              <Label htmlFor="dailyMaxPoints">每日最大积分</Label>
              <FormRow>
                <Input
                  type="number"
                  id="dailyMaxPoints"
                  name="dailyMax"
                  min="0"
                  max="200"
                  value={settings.pointLimits.dailyMax}
                  onChange={(e) => handleInputChange(e, 'pointLimits')}
                />
                <InputDescription>每天可以获得的最大积分值</InputDescription>
              </FormRow>
            </FormGroup>
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="overdraftAllowed"
                  name="overdraftAllowed"
                  checked={settings.pointLimits.overdraftAllowed}
                  onChange={(e) => handleInputChange(e, 'pointLimits')}
                />
                <CheckboxLabel htmlFor="overdraftAllowed">允许积分透支</CheckboxLabel>
              </CheckboxContainer>
              <InputDescription>允许积分为负值（透支）</InputDescription>
            </FormGroup>
            <FormGroup>
              <Label htmlFor="overdraftLimit">积分透支限额</Label>
              <FormRow>
                <Input
                  type="number"
                  id="overdraftLimit"
                  name="overdraftLimit"
                  min="0"
                  max="100"
                  disabled={!settings.pointLimits.overdraftAllowed}
                  value={settings.pointLimits.overdraftLimit}
                  onChange={(e) => handleInputChange(e, 'pointLimits')}
                />
                <InputDescription>最大允许透支的积分数量</InputDescription>
              </FormRow>
            </FormGroup>
          </CardBody>
        </SettingsCard>

        <SettingsCard>
          <CardHeader>
            <CardTitle>通知设置</CardTitle>
          </CardHeader>
          <CardBody>
            <FormGroup>
              <CheckboxContainer>
                <Checkbox 
                  type="checkbox" 
                  id="notificationsEnabled" 
                  name="notificationsEnabled"
                  checked={settings.notifications.taskReminder}
                  onChange={(e) => handleInputChange(e, 'notifications', 'taskReminder')}
                />
                <CheckboxLabel htmlFor="notificationsEnabled">启用系统通知</CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
            <FormGroup>
              <CheckboxContainer>
                <Checkbox 
                  type="checkbox" 
                  id="finishReminder" 
                  name="finishReminder"
                  checked={settings.finishReminder}
                  disabled={!settings.notifications.taskReminder}
                  onChange={(e) => handleInputChange(e, null, 'finishReminder')}
                />
                <CheckboxLabel htmlFor="finishReminder">任务到期提醒</CheckboxLabel>
              </CheckboxContainer>
              <InputDescription>
                任务即将到期时发送提醒通知
              </InputDescription>
            </FormGroup>
            <FormGroup>
              <Label htmlFor="reminderMinutes">提前提醒时间(分钟)</Label>
              <FormRow>
                <Input 
                  type="number" 
                  id="reminderMinutes" 
                  name="reminderMinutes"
                  min="5" 
                  max="120" 
                  value={settings.reminderMinutes}
                  disabled={!settings.notifications.taskReminder || !settings.finishReminder}
                  onChange={(e) => handleInputChange(e, null, 'reminderMinutes')}
                />
                <InputDescription>
                  任务截止前多少分钟发送提醒
                </InputDescription>
              </FormRow>
            </FormGroup>
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="approvalNeeded"
                  name="approvalNeeded"
                  checked={settings.notifications.approvalNeeded}
                  onChange={(e) => handleInputChange(e, 'notifications')}
                />
                <CheckboxLabel htmlFor="approvalNeeded">任务审批提醒</CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="pointsEarned"
                  name="pointsEarned"
                  checked={settings.notifications.pointsEarned}
                  onChange={(e) => handleInputChange(e, 'notifications')}
                />
                <CheckboxLabel htmlFor="pointsEarned">积分获得提醒</CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="overdraftAlert"
                  name="overdraftAlert"
                  checked={settings.notifications.overdraftAlert}
                  onChange={(e) => handleInputChange(e, 'notifications')}
                />
                <CheckboxLabel htmlFor="overdraftAlert">积分透支提醒</CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
          </CardBody>
        </SettingsCard>

        <SettingsCard>
          <CardHeader>
            <CardTitle>安全与账户</CardTitle>
          </CardHeader>
          <CardBody>
            <FormGroup>
              <Label htmlFor="parentPin">家长PIN码</Label>
              <FormRow>
                <PinInput
                  type="password"
                  id="parentPin"
                  name="parentPin"
                  value={settings.parentPin}
                  onChange={(e) => handlePinChange(e, 'parentPin')}
                  maxLength="4"
                  placeholder="请输入4位数字"
                />
                <InputDescription>设置家长控制面板的访问PIN码（4位数字）</InputDescription>
              </FormRow>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="childPin">儿童PIN码</Label>
              <FormRow>
                <PinInput
                  type="password"
                  id="childPin"
                  name="childPin"
                  value={settings.childPin}
                  onChange={(e) => handlePinChange(e, 'childPin')}
                  maxLength="4"
                  placeholder="请输入4位数字"
                />
                <InputDescription>设置儿童面板的访问PIN码（4位数字）</InputDescription>
              </FormRow>
            </FormGroup>

             <FormGroup>
                <Label>账户操作</Label>
                <LogoutButton onClick={onLogout}>
                  退出登录
                </LogoutButton>
             </FormGroup>
          </CardBody>
        </SettingsCard>

        <SettingsCard>
          <CardHeader>
            <CardTitle>系统数据</CardTitle>
          </CardHeader>
          <CardBody>
            <FormGroup>
              <DangerButton onClick={handleResetPoints}>
                重置所有积分
              </DangerButton>
              <InputDescription>将所有积分重置为零（此操作不可撤销）</InputDescription>
            </FormGroup>
          </CardBody>
        </SettingsCard>
      </SettingsGrid>
    </SettingsContainer>
  );
};

// 样式组件
const SettingsContainer = styled.div`
  padding: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: ${parentTheme.textColor};
  margin: 0;
`;

const SaveButton = styled(motion.button)`
  padding: 0.6rem 1.2rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-weight: 600;
  cursor: pointer;
`;

const SettingsGrid = styled.div`
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
`;

const SettingsCard = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const CardHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #eee;
`;

const CardTitle = styled.h2`
  margin: 0;
  font-size: 1.1rem;
  color: ${parentTheme.textColor};
`;

const CardBody = styled.div`
  padding: 1.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const FormRow = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  max-width: 120px;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
  
  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const PinInput = styled(Input)`
  letter-spacing: 0.5rem;
  font-size: 1.2rem;
  text-align: center;
`;

const Select = styled.select`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  max-width: 200px;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const InputDescription = styled.div`
  font-size: 0.8rem;
  color: #999;
  margin-top: 0.25rem;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
`;

const Checkbox = styled.input`
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.75rem;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
  color: ${parentTheme.textColor};
`;

const PenaltyTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 0.75rem 0.5rem;
    text-align: left;
  }
  
  th {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
  }
  
  td {
    border-top: 1px solid #eee;
  }
  
  tfoot td {
    padding-top: 1rem;
    border-top: none;
  }
`;

const RemoveButton = styled.button`
  padding: 0.4rem 0.8rem;
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.8rem;
  cursor: pointer;
  
  &:hover:not(:disabled) {
    background-color: rgba(255, 59, 48, 0.2);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AddButton = styled.button`
  width: 100%;
  padding: 0.6rem;
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
  cursor: pointer;
  
  &:hover {
    background-color: rgba(0, 122, 255, 0.2);
  }
`;

const DangerButton = styled.button`
  padding: 0.8rem 1rem;
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 0.5rem;
  
  &:hover {
    background-color: rgba(255, 59, 48, 0.2);
  }
`;

const LogoutButton = styled.button`
  width: 100%;
  padding: 0.8rem;
  margin-top: 1rem;
  background: ${parentTheme.secondaryColor};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  
  &:hover {
    background: #d32f2f;
  }
`;

export default SettingsPanel; 