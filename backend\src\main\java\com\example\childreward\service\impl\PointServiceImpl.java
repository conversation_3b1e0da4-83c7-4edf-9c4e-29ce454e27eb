package com.example.childreward.service.impl;

import com.example.childreward.entity.PointBalance;
import com.example.childreward.entity.PointRecord;
import com.example.childreward.repository.PointBalanceRepository;
import com.example.childreward.repository.PointRecordRepository;
import com.example.childreward.service.PointService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PointServiceImpl implements PointService {

    private final PointRecordRepository pointRecordRepository;
    private final PointBalanceRepository pointBalanceRepository;

    @Override
    @Cacheable(value = "totalPoints", key = "'current'")
    public Integer getTotalPoints() {
        PointBalance balance = pointBalanceRepository.getCurrentBalance();
        return balance.getTotalPoints();
    }

    @Override
    @Transactional
    @CacheEvict(value = {"totalPoints", "todayPoints"}, allEntries = true)
    public PointRecord addPoints(Integer points, PointRecord.ChangeType changeType, String description, Long relatedId) {
        if (points <= 0) {
            throw new IllegalArgumentException("添加积分必须为正数");
        }

        return changePoints(points, changeType, description, relatedId);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"totalPoints", "todayPoints"}, allEntries = true)
    public PointRecord deductPoints(Integer points, PointRecord.ChangeType changeType, String description, Long relatedId) {
        if (points <= 0) {
            throw new IllegalArgumentException("扣减积分必须为正数");
        }

        return changePoints(-points, changeType, description, relatedId);
    }

    @Override
    public List<PointRecord> getPointRecords(LocalDateTime startTime, LocalDateTime endTime) {
        return pointRecordRepository.findByRecordTimeBetweenOrderByRecordTimeDesc(startTime, endTime);
    }

    @Override
    @Cacheable(value = "todayPoints", key = "'today'")
    public Integer getTodayPointsChange() {
        return pointRecordRepository.getTodayPointsChange();
    }

    @Override
    public Integer getPointsChangeByDate(LocalDate date) {
        return pointRecordRepository.getPointsChangeByDate(date);
    }
    
    @Override
    public List<PointRecord> getAllRecords() {
        System.out.println("正在获取积分记录...");
        try {
            // 限制查询数量，避免全表查询导致性能问题
            Pageable pageable = PageRequest.of(0, 1000);
            List<PointRecord> records = pointRecordRepository.findAll(pageable).getContent();
            System.out.println("查询到的积分记录数量: " + records.size());
            return records;
        } catch (Exception e) {
            System.err.println("获取积分记录时发生错误: " + e.getMessage());
            e.printStackTrace();
            return List.of(); // 返回空列表而不是null
        }
    }
    
    @Override
    public PointRecord saveRecord(PointRecord record) {
        try {
            return pointRecordRepository.save(record);
        } catch (Exception e) {
            System.err.println("保存积分记录时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 内部方法：变更积分
     */
    private PointRecord changePoints(Integer pointChange, PointRecord.ChangeType changeType, String description, Long relatedId) {
        // 创建积分记录
        PointRecord record = PointRecord.builder()
                .pointChange(pointChange)
                .changeType(changeType)
                .description(description)
                .recordTime(LocalDateTime.now())
                .build();
        
        // 根据类型设置关联ID
        if (changeType == PointRecord.ChangeType.TASK_COMPLETION || changeType == PointRecord.ChangeType.TASK_PENALTY) {
            record.setRelatedTaskId(relatedId);
        } else if (changeType == PointRecord.ChangeType.REWARD_EXCHANGE) {
            record.setRelatedRewardId(relatedId);
        }
        
        // 保存积分记录
        pointRecordRepository.save(record);
        
        // 更新积分余额
        PointBalance balance = pointBalanceRepository.getCurrentBalance();
        balance.setTotalPoints(balance.getTotalPoints() + pointChange);
        pointBalanceRepository.save(balance);
        
        return record;
    }
} 