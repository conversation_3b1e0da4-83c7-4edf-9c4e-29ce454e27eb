@echo off
echo 正在构建前端项目用于本地部署...
echo.

REM 设置开发环境
set NODE_ENV=development

REM 执行构建
npm run build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo.
    echo 📁 构建文件位置: dist/
    echo 🌐 API地址配置: http://127.0.0.1:18080/api
    echo.
    echo 🚀 本地部署说明:
    echo 1. 确保后端服务运行在 127.0.0.1:18080
    echo 2. 使用 nginx 或其他 web 服务器指向 dist 目录
    echo 3. 或者使用 serve 命令: npx serve dist
) else (
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause