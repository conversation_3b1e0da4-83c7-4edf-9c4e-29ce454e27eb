/**
 * 家长端苹果风格UI组件
 * 专业管理界面，支持多设备响应式
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import { AppleDesignSystem, mediaQueries } from '../../design/AppleDesignSystem';

// ==================== 家长端主容器 ====================
export const ParentContainer = styled.div`
  min-height: 100vh;
  background: ${AppleDesignSystem.colors.parent.background};
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  
  /* 响应式布局 */
  ${mediaQueries.iphone} {
    padding: ${AppleDesignSystem.spacing.md};
  }
  
  ${mediaQueries.ipad} {
    padding: ${AppleDesignSystem.spacing.lg};
  }
  
  ${mediaQueries.mac} {
    padding: ${AppleDesignSystem.spacing.xl};
    max-width: 1400px;
    margin: 0 auto;
  }
`;

// ==================== 家长端头部 ====================
export const ParentHeader = styled(motion.header)`
  background: ${AppleDesignSystem.colors.parent.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  margin-bottom: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: ${AppleDesignSystem.spacing.md};
  
  ${mediaQueries.iphone} {
    flex-direction: column;
    text-align: center;
  }
  
  ${mediaQueries.mac} {
    padding: ${AppleDesignSystem.spacing.xl};
  }
`;

// ==================== 家长端标题 ====================
export const ParentTitle = styled.h1`
  font-family: ${AppleDesignSystem.typography.fontFamily.display};
  font-size: ${AppleDesignSystem.typography.textStyles.largeTitle.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.bold};
  color: ${AppleDesignSystem.colors.parent.primary};
  margin: 0;
  line-height: ${AppleDesignSystem.typography.textStyles.largeTitle.lineHeight};
  
  ${mediaQueries.iphone} {
    font-size: ${AppleDesignSystem.typography.textStyles.title1.fontSize};
  }
`;

export const ParentSubtitle = styled.p`
  font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  margin: ${AppleDesignSystem.spacing.xs} 0 0 0;
  line-height: ${AppleDesignSystem.typography.textStyles.headline.lineHeight};
  
  ${mediaQueries.iphone} {
    font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  }
`;

// ==================== 响应式网格 ====================
export const ParentGrid = styled.div`
  display: grid;
  gap: ${AppleDesignSystem.spacing.lg};
  
  /* 默认单列 */
  grid-template-columns: 1fr;
  
  /* 平板双列 */
  ${mediaQueries.ipad} {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 桌面三列 */
  ${mediaQueries.mac} {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* 大屏四列 */
  @media (min-width: ${AppleDesignSystem.breakpoints.xxl}) {
    grid-template-columns: repeat(4, 1fr);
  }
  
  ${mediaQueries.iphone} {
    gap: ${AppleDesignSystem.spacing.md};
  }
`;

// ==================== 家长端卡片 ====================
export const ParentCard = styled(motion.div).withConfig({
  shouldForwardProp: (prop) => !['fullWidth', 'halfWidth', 'interactive'].includes(prop)
})`
  background: ${AppleDesignSystem.colors.parent.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};
  transition: all ${AppleDesignSystem.animation.duration.normal} ${AppleDesignSystem.animation.easing.standard};
  
  ${props => props.fullWidth && css`
    grid-column: 1 / -1;
  `}
  
  ${props => props.halfWidth && css`
    ${mediaQueries.mac} {
      grid-column: span 2;
    }
  `}
  
  ${props => props.interactive && css`
    cursor: pointer;
    
    ${mediaQueries.mouse} {
      &:hover {
        box-shadow: ${AppleDesignSystem.shadows.lg};
        transform: translateY(-2px);
      }
    }
    
    ${mediaQueries.touch} {
      &:active {
        transform: scale(0.98);
      }
    }
  `}
`;

// ==================== 卡片标题 ====================
export const ParentCardTitle = styled.h3`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.title3.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  color: ${AppleDesignSystem.colors.semantic.label};
  margin: 0 0 ${AppleDesignSystem.spacing.md} 0;
  line-height: ${AppleDesignSystem.typography.textStyles.title3.lineHeight};
  
  display: flex;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.sm};
`;

export const ParentCardContent = styled.div`
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  line-height: ${AppleDesignSystem.typography.textStyles.body.lineHeight};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
`;

// ==================== 统计组件 ====================
export const StatCard = styled(ParentCard)`
  text-align: center;
  
  ${props => props.variant && css`
    background: linear-gradient(135deg, 
      ${AppleDesignSystem.colors.parent[props.variant]}1A 0%, 
      ${AppleDesignSystem.colors.parent[props.variant]}0D 100%);
    border: 1px solid ${AppleDesignSystem.colors.parent[props.variant]}26;
  `}
`;

export const StatNumber = styled.div`
  font-family: ${AppleDesignSystem.typography.fontFamily.display};
  font-size: ${AppleDesignSystem.typography.textStyles.largeTitle.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.bold};
  line-height: 1;
  margin-bottom: ${AppleDesignSystem.spacing.xs};
  
  color: ${props => {
    const variant = props.variant || 'primary';
    return AppleDesignSystem.colors.parent[variant] || AppleDesignSystem.colors.semantic.label;
  }};
  
  ${mediaQueries.iphone} {
    font-size: ${AppleDesignSystem.typography.textStyles.title1.fontSize};
  }
`;

export const StatLabel = styled.div`
  font-size: ${AppleDesignSystem.typography.textStyles.subheadline.fontSize};
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  font-weight: ${AppleDesignSystem.typography.fontWeight.medium};
`;

// ==================== 统计卡片组件 ====================
export const ParentStatCard = ({ title, value, icon, color, trend, ...props }) => {
  return (
    <StatCard {...props}>
      <div className="stat-icon">{icon}</div>
      <div className="stat-value">{value}</div>
      <div className="stat-title">{title}</div>
      {trend && <div className="stat-trend">{trend}</div>}
    </StatCard>
  );
};

// ==================== 家长端按钮 ====================
export const ParentButton = styled(motion.button)`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  
  height: ${AppleDesignSystem.components.button.height.medium};
  padding: 0 ${AppleDesignSystem.spacing.lg};
  border: none;
  border-radius: ${AppleDesignSystem.borderRadius.ios.button};
  cursor: pointer;
  
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${AppleDesignSystem.spacing.sm};
  
  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};
  
  ${props => {
    const variant = props.variant || 'filled';
    const color = props.color || 'primary';
    const systemColor = AppleDesignSystem.colors.parent[color] || AppleDesignSystem.colors.system.blue;
    
    const styles = {
      filled: css`
        background: ${systemColor};
        color: white;
        box-shadow: ${AppleDesignSystem.shadows.button};
        
        &:hover:not(:disabled) {
          background: ${systemColor}E6;
          transform: translateY(-1px);
          box-shadow: ${AppleDesignSystem.shadows.md};
        }
      `,
      
      tinted: css`
        background: ${systemColor}1A;
        color: ${systemColor};
        
        &:hover:not(:disabled) {
          background: ${systemColor}26;
        }
      `,
      
      outline: css`
        background: transparent;
        color: ${systemColor};
        border: 1px solid ${systemColor}4D;
        
        &:hover:not(:disabled) {
          background: ${systemColor}0D;
          border-color: ${systemColor};
        }
      `,
      
      plain: css`
        background: transparent;
        color: ${systemColor};
        
        &:hover:not(:disabled) {
          background: ${AppleDesignSystem.colors.semantic.systemFill};
        }
      `
    };
    return styles[variant];
  }}
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: ${AppleDesignSystem.shadows.sm};
  }
  
  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none !important;
  }
  
  ${mediaQueries.iphone} {
    width: 100%;
  }
  
  ${mediaQueries.touch} {
    &:hover {
      transform: none;
    }
    
    &:active:not(:disabled) {
      transform: scale(0.96);
    }
  }
`;

// ==================== 操作按钮组 ====================
export const ParentActionGroup = styled.div`
  display: flex;
  gap: ${AppleDesignSystem.spacing.sm};
  align-items: center;
  flex-wrap: wrap;
  
  ${mediaQueries.iphone} {
    flex-direction: column;
    width: 100%;
  }
`;

// ==================== 搜索框 ====================
export const ParentSearchBox = styled.input`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  
  width: 100%;
  max-width: 400px;
  height: ${AppleDesignSystem.components.input.height.medium};
  padding: 0 ${AppleDesignSystem.spacing.md};
  border: 1px solid ${AppleDesignSystem.colors.semantic.separator};
  border-radius: ${AppleDesignSystem.borderRadius.ios.input};
  background: ${AppleDesignSystem.colors.parent.surface};
  color: ${AppleDesignSystem.colors.semantic.label};
  outline: none;
  
  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};
  
  &:focus {
    border-color: ${AppleDesignSystem.colors.parent.primary};
    box-shadow: 0 0 0 3px ${AppleDesignSystem.colors.parent.primary}1A;
  }
  
  &::placeholder {
    color: ${AppleDesignSystem.colors.semantic.tertiaryLabel};
  }
  
  ${mediaQueries.iphone} {
    max-width: none;
  }
`;

// ==================== 状态徽章 ====================
export const ParentStatusBadge = styled.span`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.caption1.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  padding: ${AppleDesignSystem.spacing.xs} ${AppleDesignSystem.spacing.sm};
  border-radius: ${AppleDesignSystem.borderRadius.full};
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  
  ${props => {
    const colors = {
      COMPLETED: AppleDesignSystem.colors.parent.success,
      IN_PROGRESS: AppleDesignSystem.colors.parent.warning,
      PENDING: AppleDesignSystem.colors.parent.secondary,
      REJECTED: AppleDesignSystem.colors.parent.error,
      ACTIVE: AppleDesignSystem.colors.parent.success,
      INACTIVE: AppleDesignSystem.colors.semantic.systemGray
    };
    return css`
      background: ${colors[props.status] || colors.PENDING};
      color: white;
    `;
  }}
`;

// ==================== 表格组件 ====================
export const ParentTable = styled.div`
  background: ${AppleDesignSystem.colors.parent.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  overflow: hidden;
  box-shadow: ${AppleDesignSystem.shadows.card};
  
  ${mediaQueries.iphone} {
    overflow-x: auto;
  }
`;

export const ParentTableHeader = styled.div`
  background: ${AppleDesignSystem.colors.semantic.secondarySystemBackground};
  padding: ${AppleDesignSystem.spacing.md} ${AppleDesignSystem.spacing.lg};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  color: ${AppleDesignSystem.colors.semantic.label};
  border-bottom: 1px solid ${AppleDesignSystem.colors.semantic.separator};
  font-size: ${AppleDesignSystem.typography.textStyles.subheadline.fontSize};
`;

export const ParentTableRow = styled.div`
  padding: ${AppleDesignSystem.spacing.md} ${AppleDesignSystem.spacing.lg};
  border-bottom: 1px solid ${AppleDesignSystem.colors.semantic.separator};
  transition: background-color ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: ${AppleDesignSystem.colors.semantic.secondarySystemBackground};
  }
`;

// ==================== 侧边栏组件 ====================
export const ParentSidebar = styled(motion.aside)`
  background: ${AppleDesignSystem.colors.parent.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};

  ${mediaQueries.iphone} {
    display: none; /* 手机端隐藏侧边栏 */
  }

  ${mediaQueries.mac} {
    position: sticky;
    top: ${AppleDesignSystem.spacing.lg};
    height: fit-content;
  }
`;

export const ParentSidebarItem = styled(motion.button)`
  width: 100%;
  background: none;
  border: none;
  padding: ${AppleDesignSystem.spacing.md};
  border-radius: ${AppleDesignSystem.borderRadius.ios.button};
  cursor: pointer;

  display: flex;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.md};

  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.medium};
  text-align: left;

  color: ${props => props.active
    ? AppleDesignSystem.colors.parent.primary
    : AppleDesignSystem.colors.semantic.label};

  background: ${props => props.active
    ? `${AppleDesignSystem.colors.parent.primary}1A`
    : 'transparent'};

  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};

  &:hover {
    background: ${props => props.active
      ? `${AppleDesignSystem.colors.parent.primary}26`
      : AppleDesignSystem.colors.semantic.systemFill};
  }

  &:active {
    transform: scale(0.98);
  }

  .icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
  }
`;

export default {
  ParentContainer,
  ParentHeader,
  ParentTitle,
  ParentSubtitle,
  ParentGrid,
  ParentCard,
  ParentCardTitle,
  ParentCardContent,
  StatCard,
  StatNumber,
  StatLabel,
  ParentStatCard,
  ParentButton,
  ParentActionGroup,
  ParentSearchBox,
  ParentStatusBadge,
  ParentTable,
  ParentTableHeader,
  ParentTableRow,
  ParentSidebar,
  ParentSidebarItem
};
