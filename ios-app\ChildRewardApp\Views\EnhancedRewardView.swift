import SwiftUI

// MARK: - 增强版奖励页面
struct EnhancedRewardView: View {
    @State private var currentPoints: Int = 85
    @State private var selectedTab: RewardTab = .rewards
    @State private var showingSpinWheel = false
    @State private var showingPenaltyWheel = false
    @State private var showingResult = false
    @State private var resultMessage = ""
    @State private var resultIcon = ""
    @State private var resultColor = Color.blue
    
    enum RewardTab: CaseIterable {
        case rewards, penalties, history
        
        var title: String {
            switch self {
            case .rewards: return "🎁 奖励区"
            case .penalties: return "⚡ 惩罚区"
            case .history: return "📋 记录"
            }
        }
        
        var icon: String {
            switch self {
            case .rewards: return "gift.fill"
            case .penalties: return "exclamationmark.triangle.fill"
            case .history: return "clock.fill"
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部积分卡片
            pointsCard
            
            // 标签页选择器
            tabSelector
            
            // 内容区域
            TabView(selection: $selectedTab) {
                rewardsContent
                    .tag(RewardTab.rewards)
                
                penaltiesContent
                    .tag(RewardTab.penalties)
                
                historyContent
                    .tag(RewardTab.history)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        }
        .background(Color(.systemGroupedBackground))
        .sheet(isPresented: $showingSpinWheel) {
            SpinWheelView(items: rewardWheelItems) { item in
                handleSpinResult(item, isReward: true)
                showingSpinWheel = false
            }
        }
        .sheet(isPresented: $showingPenaltyWheel) {
            SpinWheelView(items: penaltyWheelItems) { item in
                handleSpinResult(item, isReward: false)
                showingPenaltyWheel = false
            }
        }
        .alert("结果", isPresented: $showingResult) {
            Button("确定") {
                showingResult = false
            }
        } message: {
            Text(resultMessage)
        }
    }
    
    // MARK: - 积分卡片
    private var pointsCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("我的积分")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("\(currentPoints)")
                        .font(.system(size: 42, weight: .bold, design: .rounded))
                        .foregroundColor(currentPoints >= 0 ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    HStack(spacing: 6) {
                        Circle()
                            .fill(currentPoints >= 0 ? .green : .red)
                            .frame(width: 8, height: 8)
                        
                        Text(currentPoints >= 0 ? "可获奖励" : "需要惩罚")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(currentPoints >= 0 ? .green : .red)
                    }
                    
                    Text("状态指示")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 积分进度条
            ProgressView(value: max(0, Double(currentPoints)), total: 100)
                .progressViewStyle(LinearProgressViewStyle(tint: currentPoints >= 0 ? .green : .red))
                .scaleEffect(y: 2)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - 标签页选择器
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(RewardTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                        
                        Text(tab.title)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .blue : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedTab == tab ? .blue.opacity(0.1) : .clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - 奖励内容
    private var rewardsContent: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 转盘抽奖卡片
                spinWheelCard(
                    title: "🎰 幸运转盘",
                    subtitle: "转动转盘，获得惊喜奖励！",
                    buttonText: "开始抽奖",
                    cost: 50,
                    canUse: currentPoints >= 50,
                    action: { showingSpinWheel = true }
                )
                
                // 直接兑换区域
                directRewardsSection
                
                Spacer(minLength: 32)
            }
            .padding(.horizontal, 20)
        }
    }
    
    // MARK: - 惩罚内容
    private var penaltiesContent: some View {
        ScrollView {
            VStack(spacing: 24) {
                if currentPoints < 0 {
                    // 惩罚转盘
                    spinWheelCard(
                        title: "⚡ 惩罚轮盘",
                        subtitle: "接受惩罚，重新获得积分！",
                        buttonText: "接受惩罚",
                        cost: 0,
                        canUse: true,
                        action: { showingPenaltyWheel = true }
                    )
                } else {
                    // 积分为正时的提示
                    VStack(spacing: 16) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.green)
                        
                        Text("表现良好！")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("你的积分为正，暂时不需要接受惩罚")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(40)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.green.opacity(0.1))
                    )
                }
                
                // 惩罚说明
                penaltyExplanationSection
                
                Spacer(minLength: 32)
            }
            .padding(.horizontal, 20)
        }
    }
    
    // MARK: - 历史记录内容
    private var historyContent: some View {
        ScrollView {
            VStack(spacing: 16) {
                Text("功能开发中...")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding(40)
            }
            .padding(.horizontal, 20)
        }
    }

    // MARK: - 转盘卡片
    private func spinWheelCard(
        title: String,
        subtitle: String,
        buttonText: String,
        cost: Int,
        canUse: Bool,
        action: @escaping () -> Void
    ) -> some View {
        VStack(spacing: 20) {
            spinWheelCardHeader(title: title, subtitle: subtitle)

            spinWheelPreview

            if cost > 0 {
                Text("消耗 \(cost) 积分")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Button(action: action) {
                Text(buttonText)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(canUse ? Color.blue : Color.gray)
                    )
            }
            .disabled(!canUse)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.gray.opacity(0.1))
                .shadow(color: .black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
    }

    // MARK: - 直接兑换区域
    private var directRewardsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🛍️ 直接兑换")
                .font(.title3)
                .fontWeight(.bold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(quickRewards, id: \.name) { reward in
                    QuickRewardCard(
                        reward: reward,
                        canAfford: currentPoints >= reward.cost
                    ) {
                        // 兑换逻辑
                        if currentPoints >= reward.cost {
                            currentPoints -= reward.cost
                            showResult(
                                message: "成功兑换「\(reward.name)」！",
                                icon: reward.icon,
                                color: .green
                            )
                        }
                    }
                }
            }
        }
    }

    // MARK: - 惩罚说明区域
    private var penaltyExplanationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("💡 惩罚说明")
                .font(.title3)
                .fontWeight(.bold)

            VStack(spacing: 12) {
                PenaltyExplanationRow(
                    icon: "exclamationmark.triangle.fill",
                    color: .orange,
                    title: "轻度惩罚",
                    description: "简单的家务或学习任务，完成后获得5-15积分"
                )

                PenaltyExplanationRow(
                    icon: "exclamationmark.octagon.fill",
                    color: .red,
                    title: "中度惩罚",
                    description: "较复杂的任务或短期限制，完成后获得15-30积分"
                )

                PenaltyExplanationRow(
                    icon: "exclamationmark.shield.fill",
                    color: .purple,
                    title: "重度惩罚",
                    description: "困难的任务或长期限制，完成后获得30-50积分"
                )
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
            )
        }
    }

    // MARK: - 数据和逻辑
    private var rewardWheelItems: [SpinWheelItem] {
        [
            SpinWheelItem(name: "看动画片", iconName: "tv", color: .blue, value: 30, type: .reward),
            SpinWheelItem(name: "冰淇淋", iconName: "snowflake", color: .cyan, value: 1, type: .reward),
            SpinWheelItem(name: "10积分", iconName: "star.fill", color: .orange, value: 10, type: .points),
            SpinWheelItem(name: "小玩具", iconName: "teddybear", color: .purple, value: 1, type: .reward),
            SpinWheelItem(name: "晚睡30分钟", iconName: "moon.stars", color: .indigo, value: 30, type: .reward),
            SpinWheelItem(name: "选择晚餐", iconName: "fork.knife", color: .green, value: 1, type: .reward),
            SpinWheelItem(name: "5积分", iconName: "star", color: .yellow, value: 5, type: .points),
            SpinWheelItem(name: "特殊奖励", iconName: "gift", color: .pink, value: 1, type: .special)
        ]
    }

    private var penaltyWheelItems: [SpinWheelItem] {
        [
            SpinWheelItem(name: "整理书桌", iconName: "desk", color: .orange, value: 10, type: .penalty),
            SpinWheelItem(name: "洗碗筷", iconName: "drop.circle", color: .red, value: 20, type: .penalty),
            SpinWheelItem(name: "背诵古诗", iconName: "book.closed", color: .blue, value: 12, type: .penalty),
            SpinWheelItem(name: "做俯卧撑", iconName: "figure.strengthtraining.traditional", color: .green, value: 8, type: .penalty),
            SpinWheelItem(name: "禁止看电视", iconName: "tv.slash", color: .purple, value: 15, type: .penalty),
            SpinWheelItem(name: "额外作业", iconName: "pencil.and.outline", color: .indigo, value: 25, type: .penalty)
        ]
    }

    private var quickRewards: [QuickReward] {
        [
            QuickReward(name: "看动画片", icon: "tv", cost: 20, color: .blue),
            QuickReward(name: "冰淇淋", icon: "snowflake", cost: 30, color: .cyan),
            QuickReward(name: "小零食", icon: "bag", cost: 15, color: .orange),
            QuickReward(name: "晚睡30分钟", icon: "moon.stars", cost: 40, color: .purple)
        ]
    }

    private func handleSpinResult(_ item: SpinWheelItem, isReward: Bool) {
        if isReward {
            currentPoints -= 50 // 抽奖消耗积分
            if item.type == .points {
                currentPoints += item.value
                showResult(
                    message: "恭喜获得 \(item.value) 积分！",
                    icon: "star.fill",
                    color: .orange
                )
            } else {
                showResult(
                    message: "恭喜获得「\(item.name)」！",
                    icon: item.iconName,
                    color: item.color
                )
            }
        } else {
            showResult(
                message: "需要执行「\(item.name)」，完成后可获得 \(item.value) 积分",
                icon: item.iconName,
                color: item.color
            )
        }
    }

    private func showResult(message: String, icon: String, color: Color) {
        resultMessage = message
        resultIcon = icon
        resultColor = color
        showingResult = true
    }
}

// MARK: - 快速奖励结构
struct QuickReward {
    let name: String
    let icon: String
    let cost: Int
    let color: Color
}

// MARK: - 快速奖励卡片
struct QuickRewardCard: View {
    let reward: QuickReward
    let canAfford: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                Image(systemName: reward.icon)
                    .font(.title2)
                    .foregroundColor(reward.color)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(reward.color.opacity(0.2))
                    )

                Text(reward.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)

                Text("\(reward.cost) 积分")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .foregroundColor(canAfford ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(canAfford ? reward.color.opacity(0.3) : Color.clear, lineWidth: 1)
                    )
            )
        }
        .disabled(!canAfford)
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 惩罚说明行
struct PenaltyExplanationRow: View {
    let icon: String
    let color: Color
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// MARK: - 颜色扩展
extension Color {
    static var random: Color {
        let colors: [Color] = [.red, .blue, .green, .orange, .purple, .pink, .yellow, .cyan]
        return colors.randomElement() ?? .blue
    }
}

// MARK: - 预览
struct EnhancedRewardView_Previews: PreviewProvider {
    static var previews: some View {
        EnhancedRewardView()
            .preferredColorScheme(.light)
    }
}

// MARK: - 辅助视图扩展
extension EnhancedRewardView {

    // MARK: - 转盘卡片标题
    private func spinWheelCardHeader(title: String, subtitle: String) -> some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)

            Text(subtitle)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    // MARK: - 转盘预览图
    private var spinWheelPreview: some View {
        ZStack {
            Circle()
                .fill(Color.gray.opacity(0.2))
                .frame(width: 120, height: 120)
                .shadow(color: .black.opacity(0.1), radius: 8)

            // 简化的转盘扇形
            ForEach(0..<6, id: \.self) { index in
                spinWheelSegment(index: index)
            }
            .frame(width: 120, height: 120)

            Circle()
                .fill(Color.white)
                .frame(width: 20, height: 20)
                .shadow(color: .black.opacity(0.2), radius: 2)
        }
    }

    // MARK: - 转盘扇形
    private func spinWheelSegment(index: Int) -> some View {
        Path { path in
            let center = CGPoint(x: 60, y: 60)
            let angle = 60.0
            let startAngle = Double(index) * angle
            path.move(to: center)
            path.addArc(
                center: center,
                radius: 55,
                startAngle: .degrees(startAngle - 90),
                endAngle: .degrees(startAngle + angle - 90),
                clockwise: false
            )
            path.closeSubpath()
        }
        .foregroundColor(Color.blue.opacity(0.7))
        .overlay(
            Path { path in
                let center = CGPoint(x: 60, y: 60)
                let angle = 60.0
                let startAngle = Double(index) * angle
                path.move(to: center)
                path.addArc(
                    center: center,
                    radius: 55,
                    startAngle: .degrees(startAngle - 90),
                    endAngle: .degrees(startAngle + angle - 90),
                    clockwise: false
                )
                path.closeSubpath()
            }
            .stroke(Color.white, lineWidth: 1)
        )
    }
}
