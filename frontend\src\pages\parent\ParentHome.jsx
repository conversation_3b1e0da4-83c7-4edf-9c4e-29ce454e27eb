import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Routes, Route, Outlet } from 'react-router-dom';
import { parentTheme } from '../../utils/themes';

// 组件
import ParentNavigation from '../../components/parent/ParentNavigation';
import ParentMobileNavigation from '../../components/parent/ParentMobileNavigation';
import ParentDashboard from './ParentDashboard';
import TaskManagement from './TaskManagement';
import ApprovalCenter from './ApprovalCenter';
import RewardManagement from './RewardManagement';
import ExchangeManagement from './ExchangeManagement';
import PointHistory from './PointHistory';
import SettingsPanel from './SettingsPanel';
import ScheduledTasksPage from '../ScheduledTasksPage';

const PARENT_AUTH_KEY = 'parentAuth';

const ParentHome = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [pinAttempts, setPinAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  
  useEffect(() => {
    const authData = localStorage.getItem(PARENT_AUTH_KEY);
    if (authData) {
      const { token, expiry } = JSON.parse(authData);
      if (token === 'VALID_PARENT_TOKEN' && new Date().getTime() < expiry) {
        setIsAuthenticated(true);
      } else {
        localStorage.removeItem(PARENT_AUTH_KEY);
      }
    }
  }, []);
  
  // 验证PIN码
  const handlePinSubmit = (e) => {
    e.preventDefault();
    
    // 检查账户是否锁定
    if (isLocked) {
      setError('账户已锁定，请等待1小时后重试');
      return;
    }
    
    // 模拟PIN验证 (实际项目中应通过API验证)
    if (pin === '0907') {
      const expiry = new Date().getTime() + 24 * 60 * 60 * 1000; // 24小时后过期
      const authData = { token: 'VALID_PARENT_TOKEN', expiry };
      localStorage.setItem(PARENT_AUTH_KEY, JSON.stringify(authData));
      setIsAuthenticated(true);
      setError('');
      setPinAttempts(0);
    } else {
      const attempts = pinAttempts + 1;
      setPinAttempts(attempts);
      
      if (attempts >= 3) {
        setIsLocked(true);
        setError('连续输入错误3次，账户已锁定1小时');
        
        // 1小时后解锁
        setTimeout(() => {
          setIsLocked(false);
          setPinAttempts(0);
        }, 60 * 60 * 1000);
      } else {
        setError(`PIN码错误，您还有${3 - attempts}次尝试机会`);
      }
      
      setPin('');
    }
  };
  
  // PIN输入处理
  const handlePinChange = (e) => {
    const value = e.target.value;
    if (/^\d{0,4}$/.test(value)) {
      setPin(value);
      setError('');
    }
  };

  return (
    <>
      {!isAuthenticated ? (
        <AuthWrapper>
          <AuthContainer
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <AuthTitle>家长验证</AuthTitle>
            <AuthDescription>请输入4位PIN码访问家长控制面板</AuthDescription>
            
            <AuthForm onSubmit={handlePinSubmit}>
              <PinInput
                type="password"
                placeholder="请输入PIN码"
                value={pin}
                onChange={handlePinChange}
                maxLength={4}
                pattern="\d{4}"
                required
                disabled={isLocked}
              />
              
              {error && <ErrorMessage>{error}</ErrorMessage>}
              
              <SubmitButton 
                type="submit"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={pin.length !== 4 || isLocked}
              >
                {isLocked ? '账户已锁定' : '验证'}
              </SubmitButton>
            </AuthForm>
            
            <SecurityNote>
              注意：连续输入错误3次将锁定1小时
            </SecurityNote>
          </AuthContainer>
        </AuthWrapper>
      ) : (
        <DashboardWrapper>
          <SidebarArea>
            <ParentNavigation />
          </SidebarArea>

          <ContentArea>
            <Routes>
              <Route path="/" element={<ParentDashboard />} />
              <Route path="/tasks/*" element={<TaskManagement />} />
              <Route path="/scheduled-tasks" element={<ScheduledTasksPage />} />
              <Route path="/approvals" element={<ApprovalCenter />} />
              <Route path="/rewards/*" element={<RewardManagement />} />
              <Route path="/exchange" element={<ExchangeManagement />} />
              <Route path="/points" element={<PointHistory />} />
              <Route path="/settings" element={<SettingsPanel onLogout={() => {
                localStorage.removeItem(PARENT_AUTH_KEY);
                setIsAuthenticated(false);
              }} />} />
            </Routes>
          </ContentArea>

          {/* 移动端底部导航 */}
          <ParentMobileNavigation />
        </DashboardWrapper>
      )}
    </>
  );
};

const AuthWrapper = styled.div`
  width: 100vw;
  height: 100vh;
  background-color: ${parentTheme.backgroundColor};
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Noto Sans SC', sans-serif;
`;

const AuthContainer = styled(motion.div)`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  padding: 2rem;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 15px ${parentTheme.shadowColor};
  text-align: center;
`;

const AuthTitle = styled.h1`
  color: ${parentTheme.primaryColor};
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
`;

const AuthDescription = styled.p`
  color: #666;
  margin-bottom: 2rem;
`;

const AuthForm = styled.form`
  display: flex;
  flex-direction: column;
`;

const PinInput = styled.input`
  padding: 1rem;
  font-size: 1.5rem;
  text-align: center;
  letter-spacing: 0.5rem;
  border: 2px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  margin-bottom: 1.5rem;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
  
  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: ${parentTheme.secondaryColor};
  margin-bottom: 1rem;
  font-size: 0.9rem;
`;

const SubmitButton = styled(motion.button)`
  padding: 1rem;
  background: ${props => props.disabled ? '#ccc' : parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  
  &:focus {
    outline: none;
  }
`;

const SecurityNote = styled.p`
  color: #999;
  font-size: 0.8rem;
  margin-top: 1.5rem;
`;

const DashboardWrapper = styled.div`
  width: 100vw;
  height: 100vh;
  display: grid;
  grid-template-columns: 240px 1fr;
  background-color: #f5f7fa;
  overflow: hidden;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding-bottom: 90px; /* 为更大的底部导航留出空间 */
  }
`;

const SidebarArea = styled.aside`
  background: white;
  height: 100%;
  overflow-y: auto;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  z-index: 10;

  @media (max-width: 768px) {
    display: none; /* 手机端隐藏侧边栏，使用底部导航 */
  }
`;

const ContentArea = styled.main`
  overflow-y: auto;
  padding: 1.5rem;

  @media (max-width: 768px) {
    padding: 1rem;
    height: calc(100vh - 90px); /* 减去底部导航的高度 */
    padding-bottom: 1.5rem; /* 额外的底部间距 */
  }
`;

export default ParentHome; 