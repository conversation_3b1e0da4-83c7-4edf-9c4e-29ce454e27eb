import SwiftUI

// MARK: - 快捷操作按钮组件
struct QuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    // 可选参数
    var subtitle: String? = nil
    var badge: Int? = nil
    var isEnabled: Bool = true
    var style: QuickActionStyle = .standard
    
    var body: some View {
        Button(action: action) {
            switch style {
            case .standard:
                standardLayout
            case .compact:
                compactLayout
            case .large:
                largeLayout
            case .minimal:
                minimalLayout
            }
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!isEnabled)
        .opacity(isEnabled ? 1.0 : 0.6)
    }
    
    // MARK: - 布局样式
    
    private var standardLayout: some View {
        HStack(spacing: 12) {
            iconView
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            chevronView
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(backgroundColor)
        .cornerRadius(12)
    }
    
    private var compactLayout: some View {
        HStack(spacing: 8) {
            iconView
                .font(.body)
                .frame(width: 16)
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
            
            if let badge = badge, badge > 0 {
                badgeView
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(backgroundColor)
        .cornerRadius(8)
    }
    
    private var largeLayout: some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 60, height: 60)
                
                iconView
                    .font(.title2)
                
                if let badge = badge, badge > 0 {
                    VStack {
                        HStack {
                            Spacer()
                            badgeView
                                .offset(x: 8, y: -8)
                        }
                        Spacer()
                    }
                }
            }
            
            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
        }
        .padding(20)
        .frame(maxWidth: .infinity)
        .background(backgroundColor)
        .cornerRadius(16)
    }
    
    private var minimalLayout: some View {
        HStack(spacing: 8) {
            iconView
                .font(.body)
            
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .foregroundColor(color)
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - 子视图组件
    
    private var iconView: some View {
        Image(systemName: icon)
            .foregroundColor(color)
            .frame(width: 20)
    }
    
    private var chevronView: some View {
        Image(systemName: "chevron.right")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    
    private var badgeView: some View {
        Text("\(badge ?? 0)")
            .font(.caption2)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.red)
            .cornerRadius(8)
    }
    
    private var backgroundColor: some View {
        RoundedRectangle(cornerRadius: style.cornerRadius)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: style.cornerRadius)
                    .stroke(color.opacity(0.2), lineWidth: 1)
            )
    }
}

// MARK: - 快捷操作样式
enum QuickActionStyle {
    case standard
    case compact
    case large
    case minimal
    
    var cornerRadius: CGFloat {
        switch self {
        case .standard: return 12
        case .compact: return 8
        case .large: return 16
        case .minimal: return 8
        }
    }
}

// MARK: - 快捷操作组
struct QuickActionGroup: View {
    let actions: [QuickActionConfig]
    let columns: Int
    let spacing: CGFloat
    
    init(actions: [QuickActionConfig], columns: Int = 2, spacing: CGFloat = 16) {
        self.actions = actions
        self.columns = columns
        self.spacing = spacing
    }
    
    var body: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: spacing), count: columns),
            spacing: spacing
        ) {
            ForEach(actions, id: \.id) { config in
                QuickActionButton(
                    icon: config.icon,
                    title: config.title,
                    color: config.color,
                    action: config.action,
                    subtitle: config.subtitle,
                    badge: config.badge,
                    isEnabled: config.isEnabled,
                    style: config.style
                )
            }
        }
    }
}

// MARK: - 快捷操作配置
struct QuickActionConfig: Identifiable {
    let id = UUID()
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    let subtitle: String?
    let badge: Int?
    let isEnabled: Bool
    let style: QuickActionStyle
    
    init(
        icon: String,
        title: String,
        color: Color,
        action: @escaping () -> Void,
        subtitle: String? = nil,
        badge: Int? = nil,
        isEnabled: Bool = true,
        style: QuickActionStyle = .standard
    ) {
        self.icon = icon
        self.title = title
        self.color = color
        self.action = action
        self.subtitle = subtitle
        self.badge = badge
        self.isEnabled = isEnabled
        self.style = style
    }
}

// MARK: - 预设快捷操作
extension QuickActionConfig {
    static func addTask(action: @escaping () -> Void) -> QuickActionConfig {
        QuickActionConfig(
            icon: "plus.circle.fill",
            title: "添加任务",
            color: .blue,
            action: action,
            subtitle: "创建新任务"
        )
    }
    
    static func viewRewards(action: @escaping () -> Void, badge: Int? = nil) -> QuickActionConfig {
        QuickActionConfig(
            icon: "gift.fill",
            title: "查看奖励",
            color: .purple,
            action: action,
            subtitle: "浏览可兑换奖励",
            badge: badge
        )
    }
    
    static func viewStats(action: @escaping () -> Void) -> QuickActionConfig {
        QuickActionConfig(
            icon: "chart.bar.fill",
            title: "统计数据",
            color: .green,
            action: action,
            subtitle: "查看完成情况"
        )
    }
    
    static func settings(action: @escaping () -> Void) -> QuickActionConfig {
        QuickActionConfig(
            icon: "gearshape.fill",
            title: "设置",
            color: .gray,
            action: action,
            subtitle: "应用设置"
        )
    }
    
    static func help(action: @escaping () -> Void) -> QuickActionConfig {
        QuickActionConfig(
            icon: "questionmark.circle.fill",
            title: "帮助",
            color: .orange,
            action: action,
            subtitle: "使用帮助"
        )
    }
    
    static func sync(action: @escaping () -> Void, isEnabled: Bool = true) -> QuickActionConfig {
        QuickActionConfig(
            icon: "arrow.clockwise",
            title: "同步数据",
            color: .blue,
            action: action,
            subtitle: isEnabled ? "同步最新数据" : "同步中...",
            isEnabled: isEnabled
        )
    }
}

// MARK: - 快捷操作列表
struct QuickActionList: View {
    let actions: [QuickActionConfig]
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(actions, id: \.id) { config in
                QuickActionButton(
                    icon: config.icon,
                    title: config.title,
                    color: config.color,
                    action: config.action,
                    subtitle: config.subtitle,
                    badge: config.badge,
                    isEnabled: config.isEnabled,
                    style: .standard
                )
            }
        }
    }
}

// MARK: - 预览
struct QuickActionButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // 标准样式
            QuickActionButton(
                icon: "plus.circle.fill",
                title: "添加任务",
                color: .blue,
                action: {},
                subtitle: "创建新的任务"
            )
            
            // 紧凑样式
            QuickActionButton(
                icon: "gift.fill",
                title: "奖励",
                color: .purple,
                action: {},
                badge: 3,
                style: .compact
            )
            
            // 大型样式
            QuickActionButton(
                icon: "chart.bar.fill",
                title: "统计",
                color: .green,
                action: {},
                subtitle: "查看完成情况",
                style: .large
            )
            
            // 最小样式
            QuickActionButton(
                icon: "gearshape.fill",
                title: "设置",
                color: .gray,
                action: {},
                style: .minimal
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}
