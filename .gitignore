# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace
*.sublime-*
.atom/
.brackets.json
.history/
.ionide/

# 日志文件
logs/
*.log
*.log.*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 上传文件目录
upload/
uploads/

# 临时文件
tmp/
temp/
.tmp/

# 缓存文件
.cache/
*.cache

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# Java相关
backend/target/
backend/.mvn/
backend/mvnw
backend/mvnw.cmd
target/
.mvn/
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
.factorypath
.project
.settings/
.classpath
.springBeans
.sts4-cache/
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# Node.js相关
node_modules/
frontend/node_modules/
frontend/dist/
frontend/build/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm/
.yarn/
.pnp.*
.yarn-integrity

# 防止根目录意外安装Node.js包
/package.json
/package-lock.json
/yarn.lock
/pnpm-lock.yaml
/node_modules/

# 打包输出目录
packages/
temp-package/

# 系统特定文件
*.orig
*.rej
.nfs*

# 测试覆盖率报告
coverage/
*.lcov

# 构建输出
build/
dist/
out/
lib/
lib-cov/

# 包管理器锁文件（可选择性忽略）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ESLint缓存
.eslintcache

# Stylelint缓存
.stylelintcache

# Parcel缓存
.parcel-cache/

# Next.js构建输出
.next/

# Nuxt.js构建输出
.nuxt/

# Gatsby文件
.cache/
public/

# Storybook构建输出
storybook-static/

# Temporary folders
.tmp/
.temp/

# 本地配置文件
config/local.json
config/local.yml
config/local.yaml

# 证书文件
*.pem
*.key
*.crt
*.p12
*.pfx

# 文档生成文件
docs/build/
site/

# Python相关（微信作业监控项目）
__pycache__/
*.py[cod]
*.so
*.egg
*.egg-info/
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# 微信作业监控项目特定文件
wechat-homework-monitor/logs/
wechat-homework-monitor/data/
wechat-homework-monitor/config_backups/
wechat-homework-monitor/release/
wechat-homework-monitor/build/
wechat-homework-monitor/dist/
wechat-homework-monitor/*.exe
wechat-homework-monitor/*.spec
wechat-homework-monitor/screenshots/
wechat-homework-monitor/first_run_completed.txt
wechat-homework-monitor/微信作业监听系统_*.zip

# OCR和AI相关临时文件
wechat-homework-monitor/*_processed.png
wechat-homework-monitor/*_debug.png
wechat-homework-monitor/test_*.py
wechat-homework-monitor/debug_*.py
wechat-homework-monitor/manual_*.py
wechat-homework-monitor/precise_*.py
wechat-homework-monitor/realtime_*.py
wechat-homework-monitor/simple_*.py

# 本地配置文件
wechat-homework-monitor/config_local.yaml
wechat-homework-monitor/config_local.yml

# iOS应用开发相关文件
ios-app/build/
ios-app/DerivedData/
ios-app/*.ipa
ios-app/*.app
ios-app/*.dSYM

# Xcode相关文件
ios-app/*.xcodeproj/xcuserdata/
ios-app/*.xcodeproj/project.xcworkspace/xcuserdata/
ios-app/*.xcworkspace/xcuserdata/
ios-app/.swiftpm/
ios-app/Packages/
ios-app/*.resolved

# iOS模拟器和设备文件
ios-app/*.hmap
ios-app/*.ipa
ios-app/build/
ios-app/DerivedData/

# Swift Package Manager
ios-app/.swiftpm/
ios-app/Packages/
ios-app/Package.resolved

# CocoaPods（如果使用）
ios-app/Pods/
ios-app/Podfile.lock

# Carthage（如果使用）
ios-app/Carthage/

# fastlane
ios-app/fastlane/report.xml
ios-app/fastlane/Preview.html
ios-app/fastlane/screenshots/**/*.png
ios-app/fastlane/test_output

# 临时构建文件
ios-app/temp_*
ios-app/Payload*

# 调试和测试文件（不应该提交到版本控制）
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.webp
!ChildRewardApp/Assets.xcassets/**/*.png
!ios-app/ChildRewardApp/Assets.xcassets/**/*.png
!**/Assets.xcassets/**/*.png
!**/AppIcon.appiconset/*.png
!**/LaunchImage.launchimage/*.png

# 临时脚本文件
add_*.py
fix_*.py
clean_*.py
ui_test_*.py
test_*.py
debug_*.py
verify_*.sh
debug_*.sh
check_*.sh
create_*.sh

# 测试Swift文件
test_*.swift
debug_*.swift
temp_*.swift

# 开发过程中的临时文件
*.md.backup
*_backup.*
*_temp.*
*_test.*
current_*.png
ios_app_*.png
app_*.png
before_*.png
after_*.png
final_*.png
debug_*.png
environment_*.png
task_*.png
tasks_*.png
no_*.png
with_*.png

# 开发报告文件（可选择性保留重要的）
*_REPORT.md
*_DIAGNOSIS.md
*_FIX_REPORT.md
*_DEVELOPMENT_REPORT.md
*_IMPROVEMENTS.md
*_OPTIMIZATION_REPORT.md
api_integration_status.md
test_environment_switch.md
