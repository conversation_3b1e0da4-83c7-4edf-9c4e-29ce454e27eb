import Foundation

// 任务详情模型 - 包含富文本内容
struct TaskDetail: Codable, Identifiable {
    let id: Int
    let sourceTemplateId: Int?
    let title: String
    let description: String?  // 富文本内容，可能包含base64图片
    let expectedMinutes: Int
    let basePoints: Int
    let dueTime: String?
    let dueDate: String?
    let status: TaskStatus
    let taskType: String?
    let startTime: String?
    let endTime: String?
    let actualPoints: Int?
    let createdTime: String?
    let scheduledDate: String?
    let actualMinutes: Int?
    
    // 计算属性
    var isCompleted: Bool {
        return status == .completed || status == .approved
    }
    
    var isOverdue: Bool {
        return status == .overdue
    }
    
    var canStart: Bool {
        return status == .notStarted || status == .pending
    }
    
    var canComplete: Bool {
        return status == .inProgress
    }
    
    // 格式化时间
    var formattedDueDate: String? {
        guard let dueDate = dueDate else { return nil }
        return DateFormatter.taskDate.string(from: ISO8601DateFormatter().date(from: dueDate) ?? Date())
    }
    
    var formattedExpectedTime: String {
        if expectedMinutes < 60 {
            return "\(expectedMinutes)分钟"
        } else {
            let hours = expectedMinutes / 60
            let minutes = expectedMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }
    
    // 获取任务类型显示名称
    var taskTypeDisplayName: String {
        switch taskType {
        case "STUDY":
            return "学习"
        case "LIFE":
            return "生活"
        case "EXERCISE":
            return "运动"
        case "ART":
            return "艺术"
        case "SOCIAL":
            return "社交"
        default:
            return taskType ?? "其他"
        }
    }
    
    // 获取难度等级
    var difficultyLevel: String {
        // 根据预期时间和积分计算难度
        let timeScore = expectedMinutes / 15  // 每15分钟1分
        let pointScore = basePoints / 5       // 每5积分1分
        let totalScore = timeScore + pointScore
        
        switch totalScore {
        case 0...2:
            return "简单"
        case 3...5:
            return "中等"
        case 6...8:
            return "困难"
        default:
            return "挑战"
        }
    }
}

// 任务详情响应模型
struct TaskDetailResponse: Codable {
    let success: Bool
    let data: TaskDetail?
    let message: String?
    let code: Int?
}

// 任务列表响应模型
struct TaskListResponse: Codable {
    let success: Bool
    let data: [TaskSummary]?
    let message: String?
    let code: Int?
}

// 任务操作响应模型
struct TaskActionResponse: Codable {
    let success: Bool
    let data: TaskActionResult?
    let message: String?
    let code: Int?
}

struct TaskActionResult: Codable {
    let taskId: Int
    let status: TaskStatus
    let points: Int?
    let message: String?
}
