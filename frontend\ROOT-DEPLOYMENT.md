# CRS 前端根目录部署说明

## 📦 部署包信息

- **文件名**: `crs-frontend-deploy.zip`
- **部署方式**: 根目录部署（不使用子目录）
- **访问方式**: 直接通过 IP:端口 访问

## 🚀 部署步骤

### Apache 部署
1. 将 `crs-frontend-deploy.zip` 上传到服务器
2. 解压到 Apache 网站根目录（如 `/var/www/html/` 或 `htdocs/`）
3. 确保 `.htaccess` 文件存在且 Apache 启用了 `mod_rewrite`
4. 访问: `http://your-server-ip:port/`

### Nginx 部署
1. 将 `crs-frontend-deploy.zip` 上传到服务器
2. 解压到 Nginx 网站根目录
3. 使用提供的 `nginx-root.conf` 配置文件
4. 重启 Nginx 服务
5. 访问: `http://your-server-ip:port/`

## 🔧 配置信息

### API 配置
- **后端地址**: `http://*************:18080/api`
- **跨域**: 前端直接调用后端API，可能需要后端配置CORS

### 静态资源路径
- **根路径**: `/`
- **JS文件**: `/assets/index-xxx.js`
- **图标**: `/star.svg`

## 📋 部署包内容

```
crs-frontend-deploy.zip
├── index.html          # 主页面
├── .htaccess          # Apache配置（支持路由）
├── star.svg           # 图标文件
└── assets/
    └── index-xxx.js   # 打包后的JS文件
```

## 🌐 Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/files;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 可选：API代理（统一域名）
    location /api/ {
        proxy_pass http://*************:18080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🔍 故障排除

### 问题：页面白屏
1. 检查浏览器控制台是否有错误
2. 检查网络面板，确认静态资源加载正常
3. 检查后端服务是否运行在 `*************:18080`

### 问题：API请求失败
1. 检查后端服务状态
2. 检查防火墙设置
3. 考虑配置 Nginx/Apache 代理API请求

### 问题：路由不工作
1. 确保 Apache 启用了 `mod_rewrite`
2. 确保 `.htaccess` 文件存在
3. 确保 Nginx 配置了 `try_files $uri $uri/ /index.html;`

## ✅ 验证清单

部署后请检查：
- [ ] 页面能正常加载（不是白屏）
- [ ] 静态资源无404错误
- [ ] API请求能正常发送
- [ ] 页面路由功能正常
- [ ] 登录和基本功能正常

## 🔄 如果仍有问题

如果根目录部署仍有问题，可能需要：
1. 检查后端CORS配置
2. 使用Nginx/Apache代理API请求
3. 检查网络连接和防火墙设置