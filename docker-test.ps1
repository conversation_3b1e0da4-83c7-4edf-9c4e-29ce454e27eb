# Simple Docker test script
param (
    [string]$Action = "build"
)

$CONTAINER_NAME = "crs-system"
$IMAGE_NAME = "zlean/crs-system:1.0.0"

function Build-System {
    Write-Host "Building Child Reward System..." -ForegroundColor Green
    
    # 1. Build backend
    Write-Host "Step 1: Building backend JAR..." -ForegroundColor Yellow
    Set-Location backend
    mvn clean package -DskipTests -q
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Backend build failed" -ForegroundColor Red
        return $false
    }
    Set-Location ..
    Write-Host "Backend build successful!" -ForegroundColor Green
    
    # 2. Build frontend
    Write-Host "Step 2: Building frontend..." -ForegroundColor Yellow
    Set-Location frontend
    if (!(Test-Path "node_modules")) {
        npm install --registry=https://registry.npmmirror.com --silent
    }
    npm run build --silent
    if (!(Test-Path "dist")) {
        Write-Host "ERROR: Frontend build failed" -ForegroundColor Red
        return $false
    }
    Set-Location ..
    Write-Host "Frontend build successful!" -ForegroundColor Green
    
    # 3. Build Docker image
    Write-Host "Step 3: Building Docker image..." -ForegroundColor Yellow
    docker build --progress=plain -f Dockerfile.all-in-one -t $IMAGE_NAME .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Docker build failed" -ForegroundColor Red
        return $false
    }
    Write-Host "Docker image build successful!" -ForegroundColor Green
    
    return $true
}

function Start-Container {
    Write-Host "Starting container..." -ForegroundColor Green
    
    # Stop existing container
    docker stop $CONTAINER_NAME 2>$null
    docker rm $CONTAINER_NAME 2>$null
    
    # Start new container
    docker run -d `
        --name $CONTAINER_NAME `
        -p 8080:80 `
        -p 18080:18080 `
        -e SPRING_PROFILES_ACTIVE=prod `
        -e JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC" `
        -e MYSQL_HOST=localhost `
        -e MYSQL_PORT=3306 `
        -e MYSQL_DATABASE=crs `
        -e MYSQL_USERNAME=root `
        -e MYSQL_PASSWORD=123456 `
        -v "${PWD}/logs:/app/logs" `
        $IMAGE_NAME
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Container started successfully!" -ForegroundColor Green
        Write-Host "Waiting for services to start..." -ForegroundColor Yellow
        Start-Sleep -Seconds 20
        Show-Status
    } else {
        Write-Host "Container start failed" -ForegroundColor Red
    }
}

function Show-Status {
    Write-Host "`n======= Container Status =======" -ForegroundColor Cyan
    docker ps --filter "name=$CONTAINER_NAME"
    
    Write-Host "`n======= Service Test =======" -ForegroundColor Cyan
    
    # Test frontend
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5
        Write-Host "Frontend: OK (Status: $($response.StatusCode))" -ForegroundColor Green
    } catch {
        Write-Host "Frontend: FAILED" -ForegroundColor Red
    }
    
    # Test backend health
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8080/actuator/health" -TimeoutSec 5
        Write-Host "Backend Health: $($response.status)" -ForegroundColor Green
    } catch {
        Write-Host "Backend Health: FAILED" -ForegroundColor Red
    }
    
    Write-Host "`n======= Access URLs =======" -ForegroundColor Cyan
    Write-Host "Parent Portal: http://localhost:8080" -ForegroundColor White
    Write-Host "Child Portal: http://localhost:8080/child" -ForegroundColor White
    Write-Host "Health Check: http://localhost:8080/actuator/health" -ForegroundColor White
}

function Show-Logs {
    Write-Host "Container logs:" -ForegroundColor Yellow
    docker logs -f $CONTAINER_NAME
}

# Main execution
switch ($Action.ToLower()) {
    "build" {
        if (Build-System) {
            Start-Container
        }
    }
    "start" {
        Start-Container
    }
    "status" {
        Show-Status
    }
    "logs" {
        Show-Logs
    }
    default {
        Write-Host "Usage: .\docker-test.ps1 -Action [build|start|status|logs]" -ForegroundColor Yellow
    }
}