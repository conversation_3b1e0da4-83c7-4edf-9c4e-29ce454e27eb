import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { childTheme } from '../../utils/themes';

const StatusBar = ({ currentPoints, dailyPointsChange }) => {
  const [showPointsChange, setShowPointsChange] = useState(false);
  const [lastChange, setLastChange] = useState(0);
  
  // 监听积分变化，显示浮动效果
  useEffect(() => {
    let lastKnownPoints = dailyPointsChange;
    
    if (lastKnownPoints !== lastChange) {
      const change = lastKnownPoints - lastChange;
      if (change !== 0) {
        setLastChange(lastKnownPoints);
        setShowPointsChange(true);
        
        // 3秒后隐藏动画
        const timer = setTimeout(() => {
          setShowPointsChange(false);
        }, 3000);
        
        return () => clearTimeout(timer);
      }
    }
  }, [dailyPointsChange, lastChange]);

  return (
    <StatusBarContainer>
      <PointsDisplayContainer>
        <PointsLabel>当前积分</PointsLabel>
        <PointsValueWrapper>
          <StarIcon>⭐</StarIcon>
          <PointsValue>{currentPoints}</PointsValue>
          <AnimatePresence>
            {showPointsChange && (
              <PointsChangeIndicator
                initial={{ opacity: 0, y: 0 }}
                animate={{ opacity: 1, y: -20 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                {dailyPointsChange > lastChange ? '+' : ''}
                {dailyPointsChange - lastChange}
              </PointsChangeIndicator>
            )}
          </AnimatePresence>
        </PointsValueWrapper>
      </PointsDisplayContainer>
    </StatusBarContainer>
  );
};

const StatusBarContainer = styled.div`
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: ${childTheme.borderRadius};
  padding: 1.2rem 1.5rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, ${childTheme.primaryColor}, ${childTheme.secondaryColor});
    z-index: 1;
  }
`;

const PointsDisplayContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const PointsLabel = styled.div`
  font-size: 1.1rem;
  font-weight: 600;
  color: #666;
  margin-bottom: 0.5rem;
`;

const PointsValueWrapper = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

const StarIcon = styled.span`
  font-size: 2rem;
  margin-right: 0.8rem;
  color: #ffd700;
  text-shadow: 0 2px 5px rgba(255, 215, 0, 0.5);
  animation: spin 10s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const PointsValue = styled.span`
  font-size: 2.2rem;
  font-weight: 800;
  color: ${childTheme.primaryColor};
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
`;

const PointsChangeIndicator = styled(motion.div)`
  position: absolute;
  right: -20px;
  top: 0;
  font-weight: 700;
  color: ${props => props.children >= 0 ? childTheme.statusColors.completed : childTheme.statusColors.notStarted};
  font-size: 1.2rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
`;

export default StatusBar; 