import Foundation
import Combine

// MARK: - 任务服务
class TaskService: ObservableObject {
    private let networkManager = NetworkManager.shared
    private let networkService = NetworkService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 任务API方法

    /// 获取今日任务列表
    func fetchTodayTasks() -> AnyPublisher<[TaskSummary], APIError> {
        print("📋 TaskService: 开始获取今日任务")
        return networkManager.request(
            endpoint: .todayTasks,
            responseType: [TaskSummary].self  // 直接解析任务数组，不是包装响应
        )
        .map { tasks in
            print("📋 TaskService: 成功解析 \(tasks.count) 个任务")
            return tasks
        }
        .eraseToAnyPublisher()
    }

    /// 获取所有任务
    func fetchAllTasks() -> AnyPublisher<[TaskSummary], APIError> {
        print("📋 TaskService: 开始获取所有任务")
        return networkManager.request(
            endpoint: .todayTasks,  // 暂时使用今日任务，后续可以添加新的endpoint
            responseType: [TaskSummary].self  // 直接解析任务数组，不是包装响应
        )
        .map { tasks in
            print("📋 TaskService: 成功解析 \(tasks.count) 个任务")
            return tasks
        }
        .eraseToAnyPublisher()
    }

    /// 获取任务详情（包含富文本内容）
    func fetchTaskDetail(taskId: Int) -> AnyPublisher<TaskDetail, APIError> {
        print("📋 TaskService: 开始获取任务详情，ID: \(taskId)")
        return networkManager.request(
            endpoint: .taskDetail(taskId),
            responseType: TaskModel.self  // 后端返回TaskModel，我们将其转换为TaskDetail
        )
        .map { task in
            print("📋 TaskService: 成功获取任务详情: \(task.title)")
            // 将TaskModel转换为TaskDetail
            return TaskDetail(
                id: task.id,
                sourceTemplateId: task.sourceTemplateId,
                title: task.title,
                description: task.description,
                expectedMinutes: task.expectedMinutes,
                basePoints: task.basePoints,
                dueTime: task.dueTime,
                dueDate: task.dueDate,
                status: task.status,
                taskType: task.taskType,
                startTime: task.startTime,
                endTime: task.endTime,
                actualPoints: task.actualPoints,
                createdTime: task.createdTime,
                scheduledDate: task.scheduledDate,
                actualMinutes: task.actualMinutes
            )
        }
        .eraseToAnyPublisher()
    }
    
    /// 开始任务
    func startTask(_ taskId: Int) -> AnyPublisher<TaskActionResult, APIError> {
        print("📋 TaskService: 开始任务，ID: \(taskId)")
        return networkManager.request(
            endpoint: .startTask(taskId),
            responseType: TaskModel.self  // 后端返回更新后的任务对象
        )
        .map { task in
            print("📋 TaskService: 任务开始成功: \(task.title)")
            // 将TaskModel转换为TaskActionResult
            return TaskActionResult(
                taskId: task.id,
                status: task.status,
                points: task.actualPoints,
                message: "任务已开始"
            )
        }
        .eraseToAnyPublisher()
    }

    /// 完成任务
    func completeTask(_ taskId: Int) -> AnyPublisher<TaskActionResult, APIError> {
        print("📋 TaskService: 完成任务，ID: \(taskId)")
        return networkManager.request(
            endpoint: .completeTask(taskId),
            responseType: TaskModel.self  // 后端返回更新后的任务对象
        )
        .map { task in
            print("📋 TaskService: 任务完成成功: \(task.title)")
            // 将TaskModel转换为TaskActionResult
            return TaskActionResult(
                taskId: task.id,
                status: task.status,
                points: task.actualPoints,
                message: "任务已完成"
            )
        }
        .eraseToAnyPublisher()
    }
    
    /// 暂停任务
    func pauseTask(_ taskId: Int) async throws -> TaskModel {
        return try await networkService.post(
            endpoint: "/tasks/\(taskId)/pause",
            body: EmptyBody(),
            responseType: TaskModel.self
        )
    }
    
    /// 取消任务
    func cancelTask(_ taskId: Int) async throws -> TaskModel {
        return try await networkService.post(
            endpoint: "/tasks/\(taskId)/cancel",
            body: EmptyBody(),
            responseType: TaskModel.self
        )
    }
    
    /// 重新开始任务
    func restartTask(_ taskId: Int) async throws -> TaskModel {
        return try await networkService.post(
            endpoint: "/tasks/\(taskId)/restart",
            body: EmptyBody(),
            responseType: TaskModel.self
        )
    }
    
    /// 提交任务结果
    func submitTaskResult(_ taskId: Int, result: TaskResult) async throws -> TaskModel {
        return try await networkService.post(
            endpoint: "/tasks/\(taskId)/submit",
            body: result,
            responseType: TaskModel.self
        )
    }
    
    /// 获取任务统计
    func fetchTaskStats(period: StatsPeriod = .week) async throws -> TaskStats {
        return try await networkService.get(
            endpoint: "/tasks/stats",
            responseType: TaskStats.self,
            queryParameters: ["period": period.rawValue]
        )
    }
    
    /// 获取任务历史
    func fetchTaskHistory(page: Int = 1, pageSize: Int = 20) async throws -> PaginatedResponse<TaskModel> {
        return try await networkService.get(
            endpoint: "/tasks/history",
            responseType: PaginatedResponse<TaskModel>.self,
            queryParameters: [
                "page": String(page),
                "pageSize": String(pageSize)
            ]
        )
    }
}

// MARK: - 辅助模型
struct EmptyBody: Codable {}

struct TaskResult: Codable {
    let actualMinutes: Int?
    let notes: String?
    let rating: Int? // 1-5星评分
    let attachments: [String]? // 附件URL列表
}

struct TaskStats: Codable {
    let totalTasks: Int
    let completedTasks: Int
    let inProgressTasks: Int
    let pendingTasks: Int
    let totalPoints: Int
    let averageCompletionTime: Double
    let completionRate: Double
    let streakDays: Int
    let bestStreak: Int
    
    var completionPercentage: Double {
        guard totalTasks > 0 else { return 0 }
        return Double(completedTasks) / Double(totalTasks) * 100
    }
}

enum StatsPeriod: String, CaseIterable {
    case day = "day"
    case week = "week"
    case month = "month"
    case year = "year"
    
    var displayName: String {
        switch self {
        case .day: return "今日"
        case .week: return "本周"
        case .month: return "本月"
        case .year: return "今年"
        }
    }
}

// MARK: - 任务模板相关
extension TaskService {
    /// 获取任务模板列表
    func fetchTaskTemplates() async throws -> [TaskTemplate] {
        return try await networkService.get(
            endpoint: "/task-templates",
            responseType: [TaskTemplate].self
        )
    }
    
    /// 从模板创建任务
    func createTaskFromTemplate(_ templateId: Int, scheduledDate: String) async throws -> TaskModel {
        let request = CreateTaskRequest(
            templateId: templateId,
            scheduledDate: scheduledDate
        )
        
        return try await networkService.post(
            endpoint: "/tasks/from-template",
            body: request,
            responseType: TaskModel.self
        )
    }
}

struct TaskTemplate: Codable, Identifiable {
    let id: Int
    let title: String
    let description: String?
    let expectedMinutes: Int
    let basePoints: Int
    let category: String
    let difficulty: TaskDifficulty
    let isActive: Bool
    let tags: [String]
}

enum TaskDifficulty: String, CaseIterable, Codable {
    case easy = "EASY"
    case medium = "MEDIUM"
    case hard = "HARD"
    
    var displayName: String {
        switch self {
        case .easy: return "简单"
        case .medium: return "中等"
        case .hard: return "困难"
        }
    }
    
    var pointsMultiplier: Double {
        switch self {
        case .easy: return 1.0
        case .medium: return 1.2
        case .hard: return 1.5
        }
    }
}

struct CreateTaskRequest: Codable {
    let templateId: Int
    let scheduledDate: String
    let customTitle: String?
    let customPoints: Int?
    let customMinutes: Int?
    
    init(templateId: Int, scheduledDate: String, customTitle: String? = nil, customPoints: Int? = nil, customMinutes: Int? = nil) {
        self.templateId = templateId
        self.scheduledDate = scheduledDate
        self.customTitle = customTitle
        self.customPoints = customPoints
        self.customMinutes = customMinutes
    }
}
