package com.example.childreward.service;

import com.example.childreward.entity.SystemConfig;

import java.util.List;

public interface SystemConfigService {
    
    /**
     * 获取所有系统配置
     */
    List<SystemConfig> getAllConfigs();
    
    /**
     * 根据键获取系统配置
     */
    SystemConfig getConfigByKey(String key);
    
    /**
     * 创建系统配置
     */
    SystemConfig createConfig(SystemConfig config);
    
    /**
     * 更新系统配置
     */
    SystemConfig updateConfig(String key, String value, String description);
    
    /**
     * 删除系统配置
     * @param key 要删除的配置键
     */
    void deleteConfig(String key);
    
    /**
     * 获取配置值（字符串）
     */
    String getStringValue(String key, String defaultValue);
    
    /**
     * 获取配置值（整数）
     */
    Integer getIntValue(String key, Integer defaultValue);
    
    /**
     * 获取配置值（布尔值）
     */
    Boolean getBooleanValue(String key, Boolean defaultValue);

    /**
     * 创建或更新一个配置项。
     * @param key 配置键
     * @param value 配置值
     * @param description 配置描述
     */
    void set(String key, String value, String description);

    /**
     * 获取配置值（字符串）
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    String get(String key, String defaultValue);

    /**
     * 获取配置值（整数）
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    Integer getInt(String key, Integer defaultValue);

    /**
     * 获取配置值（布尔值）
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    Boolean getBoolean(String key, Boolean defaultValue);
} 