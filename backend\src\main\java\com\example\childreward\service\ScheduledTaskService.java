package com.example.childreward.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduledTaskService {

    private final TaskService taskService;

    /**
     * 每天凌晨0点处理逾期任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void processOverdueTasks() {
        log.info("开始处理逾期任务");
        taskService.processOverdueTasks();
        log.info("逾期任务处理完成");
    }
} 