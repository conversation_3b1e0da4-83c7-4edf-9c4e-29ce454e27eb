import { createChildApiClient } from './apiConfig.js';

// 儿童端专用API服务
export const childApi = {
  // 获取今日任务
  getTodayTasks: () => {
    const client = createChildApiClient();
    return client.get('/tasks/today');
  },

  // 获取昨日任务
  getYesterdayTasks: () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0]; // YYYY-MM-DD格式
    const client = createChildApiClient();
    return client.get(`/tasks/date/${yesterdayStr}`);
  },

  // 获取过期任务
  getOverdueTasks: () => {
    const client = createChildApiClient();
    return client.get('/tasks/overdue');
  },

  // 获取任务详情（包含完整description字段）
  getTaskById: (taskId) => {
    const client = createChildApiClient();
    return client.get(`/tasks/${taskId}`);
  },

  // 开始任务
  startTask: (taskId) => {
    const client = createChildApiClient();
    return client.post(`/tasks/${taskId}/start`);
  },

  // 完成任务
  completeTask: (taskId) => {
    const client = createChildApiClient();
    return client.post(`/tasks/${taskId}/complete`);
  },

  // 获取积分余额
  getPointBalance: () => {
    const client = createChildApiClient();
    return client.get('/points/total');
  },

  // 获取今日积分变化
  getTodayPointChange: () => {
    const client = createChildApiClient();
    return client.get('/points/today');
  },

  // 获取可用奖品池
  getAvailableRewardPools: () => {
    const client = createChildApiClient();
    return client.get('/rewards/pools/available');
  },

  // 抽奖
  drawReward: (poolId) => {
    const client = createChildApiClient();
    return client.post(`/rewards/pools/${poolId}/draw`);
  },

  // 获取积分记录
  getPointRecords: (startTime, endTime) => {
    const client = createChildApiClient();
    
    if (!startTime || !endTime) {
      return client.get('/points/records');
    }
    
    const formattedStartTime = startTime.toISOString();
    const formattedEndTime = endTime.toISOString();
    
    return client.get('/points/records', {
      params: {
        startTime: formattedStartTime,
        endTime: formattedEndTime
      }
    });
  }
};
