-- 创建CRS数据库初始化脚本
USE crs;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `role` varchar(20) NOT NULL DEFAULT 'CHILD' COMMENT '角色：PARENT, CHILD',
  `name` varchar(100) NOT NULL COMMENT '姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建任务表
CREATE TABLE IF NOT EXISTS `task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `points` int NOT NULL DEFAULT '0' COMMENT '奖励积分',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING, IN_PROGRESS, COMPLETED, CANCELLED',
  `assigned_to` bigint DEFAULT NULL COMMENT '分配给的用户ID',
  `created_by` bigint NOT NULL COMMENT '创建者ID',
  `due_date` date DEFAULT NULL COMMENT '截止日期',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `assigned_to` (`assigned_to`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `task_ibfk_1` FOREIGN KEY (`assigned_to`) REFERENCES `user` (`id`),
  CONSTRAINT `task_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- 创建积分记录表
CREATE TABLE IF NOT EXISTS `point_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `points` int NOT NULL COMMENT '积分变化',
  `type` varchar(20) NOT NULL COMMENT '类型：EARN, SPEND',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `task_id` bigint DEFAULT NULL COMMENT '关联任务ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `task_id` (`task_id`),
  CONSTRAINT `point_record_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `point_record_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `task` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 创建定时任务表
CREATE TABLE IF NOT EXISTS `scheduled_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `points` int NOT NULL DEFAULT '0' COMMENT '奖励积分',
  `assigned_to` bigint DEFAULT NULL COMMENT '分配给的用户ID',
  `created_by` bigint NOT NULL COMMENT '创建者ID',
  `schedule_time` time NOT NULL COMMENT '执行时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `direct_to_review` int NOT NULL DEFAULT '0' COMMENT '是否直接进入审核：0-否，1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `assigned_to` (`assigned_to`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `scheduled_task_ibfk_1` FOREIGN KEY (`assigned_to`) REFERENCES `user` (`id`),
  CONSTRAINT `scheduled_task_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务表';

-- 插入测试数据
-- 插入父母用户
INSERT INTO `user` (`username`, `password`, `role`, `name`) VALUES 
('parent', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkHWjFOqOrAh2LGpSy', 'PARENT', '父母');

-- 插入孩子用户
INSERT INTO `user` (`username`, `password`, `role`, `name`) VALUES 
('child1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkHWjFOqOrAh2LGpSy', 'CHILD', '孩子1'),
('child2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkHWjFOqOrAh2LGpSy', 'CHILD', '孩子2');

-- 插入测试任务
INSERT INTO `task` (`title`, `description`, `points`, `assigned_to`, `created_by`, `due_date`) VALUES 
('完成作业', '完成今天的数学作业', 10, 2, 1, CURDATE()),
('整理房间', '把房间整理干净', 15, 3, 1, CURDATE());

-- 插入定时任务
INSERT INTO `scheduled_task` (`title`, `description`, `points`, `assigned_to`, `created_by`, `schedule_time`) VALUES 
('每日阅读', '每天阅读30分钟', 5, 2, 1, '19:00:00'),
('刷牙洗脸', '早晚刷牙洗脸', 3, 3, 1, '08:00:00');
