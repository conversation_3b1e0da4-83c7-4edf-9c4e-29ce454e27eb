import SwiftUI

// 任务统计卡片
struct TaskStatisticsCard: View {
    @ObservedObject var viewModel: TaskViewModel
    
    var body: some View {
        VStack(spacing: 16) {
            // 今日进度环
            HStack(spacing: 24) {
                // 进度环
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.2), lineWidth: 8)
                        .frame(width: 80, height: 80)
                    
                    Circle()
                        .trim(from: 0, to: viewModel.todayCompletionRate)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color(red: 52/255, green: 199/255, blue: 89/255),
                                    Color(red: 102/255, green: 219/255, blue: 129/255)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            style: StrokeStyle(lineWidth: 8, lineCap: .round)
                        )
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1.0), value: viewModel.todayCompletionRate)
                    
                    VStack(spacing: 2) {
                        Text("\(viewModel.todayCompletedCount)")
                            .font(.system(size: 20, weight: .bold, design: .rounded))
                            .foregroundColor(Color(red: 28/255, green: 28/255, blue: 30/255))
                        
                        Text("已完成")
                            .font(.system(size: 10, weight: .medium, design: .rounded))
                            .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))
                    }
                }
                
                // 统计信息
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        StatisticItem(
                            title: "今日任务",
                            value: "\(viewModel.todayTotalCount)",
                            icon: "list.bullet",
                            color: Color(red: 0/255, green: 122/255, blue: 255/255)
                        )

                        Spacer()

                        // 当前已用时显示
                        VStack(alignment: .trailing, spacing: 2) {
                            Text("当前已用时")
                                .font(.system(size: 11, weight: .medium, design: .rounded))
                                .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))

                            Text(viewModel.formattedTodayTotalUsedTime)
                                .font(.system(size: 13, weight: .semibold, design: .rounded))
                                .foregroundColor(Color(red: 255/255, green: 149/255, blue: 0/255))
                        }
                    }

                    StatisticItem(
                        title: "完成率",
                        value: viewModel.statistics.formattedCompletionRate,
                        icon: "chart.line.uptrend.xyaxis",
                        color: Color(red: 52/255, green: 199/255, blue: 89/255)
                    )

                    if viewModel.hasOverdueTasks {
                        StatisticItem(
                            title: "过期任务",
                            value: "\(viewModel.overdueCount)",
                            icon: "exclamationmark.triangle",
                            color: Color(red: 255/255, green: 59/255, blue: 48/255)
                        )
                    }
                }
                
                Spacer()
            }
            
            // 快速操作按钮
            HStack(spacing: 12) {
                QuickActionButton(
                    title: "刷新",
                    icon: "arrow.clockwise",
                    color: Color(red: 0/255, green: 122/255, blue: 255/255)
                ) {
                    viewModel.loadAllTasks()
                }
                
                if viewModel.hasOverdueTasks {
                    QuickActionButton(
                        title: "过期提醒",
                        icon: "bell.badge",
                        color: Color(red: 255/255, green: 149/255, blue: 0/255)
                    ) {
                        // 处理过期任务提醒
                    }
                }
                
                Spacer()
                
                // 今日目标
                TodayGoalView(
                    completed: viewModel.todayCompletedCount,
                    total: viewModel.todayTotalCount
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(
                    color: Color.black.opacity(0.08),
                    radius: 12,
                    x: 0,
                    y: 4
                )
        )
    }
}

// 统计项目
struct StatisticItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 12, weight: .medium, design: .rounded))
                    .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))
                
                Text(value)
                    .font(.system(size: 16, weight: .bold, design: .rounded))
                    .foregroundColor(Color(red: 28/255, green: 28/255, blue: 30/255))
            }
            
            Spacer()
        }
    }
}

// 快速操作按钮
struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .semibold))
                Text(title)
                    .font(.system(size: 14, weight: .semibold, design: .rounded))
            }
            .foregroundColor(color)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(color.opacity(0.1))
            .cornerRadius(16)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 今日目标视图
struct TodayGoalView: View {
    let completed: Int
    let total: Int
    
    private var isGoalReached: Bool {
        return total > 0 && completed >= total
    }
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            HStack(spacing: 4) {
                Image(systemName: isGoalReached ? "star.fill" : "star")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(isGoalReached ? Color(red: 255/255, green: 204/255, blue: 0/255) : Color.gray)
                
                Text("今日目标")
                    .font(.system(size: 12, weight: .medium, design: .rounded))
                    .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))
            }
            
            Text("\(completed)/\(total)")
                .font(.system(size: 16, weight: .bold, design: .rounded))
                .foregroundColor(isGoalReached ? Color(red: 52/255, green: 199/255, blue: 89/255) : Color(red: 28/255, green: 28/255, blue: 30/255))
            
            if isGoalReached {
                Text("🎉 目标达成!")
                    .font(.system(size: 10, weight: .semibold, design: .rounded))
                    .foregroundColor(Color(red: 52/255, green: 199/255, blue: 89/255))
            }
        }
    }
}

// 预览
struct TaskStatisticsCard_Previews: PreviewProvider {
    static var previews: some View {
        TaskStatisticsCard(viewModel: TaskViewModel())
            .padding()
            .background(Color(red: 248/255, green: 248/255, blue: 248/255))
            .previewDevice("iPad Pro (12.9-inch) (6th generation)")
    }
}
