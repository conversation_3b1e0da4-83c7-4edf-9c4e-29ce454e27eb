import Foundation
import SwiftUI

// MARK: - 奖励模型
struct RewardItem: Identifiable {
    let id: Int
    let name: String
    let pointsCost: Int
    let iconName: String
    let color: Color
    let description: String?
    let category: RewardCategory?
    let isAvailable: Bool
    
    init(id: Int, name: String, pointsCost: Int, iconName: String, color: Color, description: String? = nil, category: RewardCategory? = nil, isAvailable: Bool = true) {
        self.id = id
        self.name = name
        self.pointsCost = pointsCost
        self.iconName = iconName
        self.color = color
        self.description = description
        self.category = category
        self.isAvailable = isAvailable
    }
}

// MARK: - 奖励分类
enum RewardCategory: String, CaseIterable, Codable {
    case entertainment = "ENTERTAINMENT"
    case food = "FOOD"
    case toys = "TOYS"
    case activities = "ACTIVITIES"
    case privileges = "PRIVILEGES"
    
    var displayName: String {
        switch self {
        case .entertainment: return "娱乐"
        case .food: return "美食"
        case .toys: return "玩具"
        case .activities: return "活动"
        case .privileges: return "特权"
        }
    }
    
    var iconName: String {
        switch self {
        case .entertainment: return "tv"
        case .food: return "fork.knife"
        case .toys: return "gamecontroller"
        case .activities: return "figure.walk"
        case .privileges: return "star"
        }
    }
}

// MARK: - 兑换记录
struct ExchangeRecord: Identifiable, Codable {
    let id: Int
    let exchangeItemId: Int
    let itemName: String
    let pointsUsed: Int
    let exchangeTime: String
    let status: ExchangeStatus
    let usedTime: String?
    let expireTime: String?
    let notes: String?

    // 兼容旧版本的属性
    var rewardId: Int { exchangeItemId }
    var rewardName: String { itemName }
    var pointsCost: Int { pointsUsed }
    var exchangeDate: String { exchangeTime }

    var formattedDate: String {
        return formattedExchangeTime
    }

    var formattedExchangeTime: String {
        guard let date = ISO8601DateFormatter().date(from: exchangeTime) else {
            return exchangeTime
        }
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }

    var formattedExpireTime: String? {
        guard let expireTime = expireTime,
              let date = ISO8601DateFormatter().date(from: expireTime) else {
            return nil
        }
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 兑换状态
enum ExchangeStatus: String, CaseIterable, Codable {
    case unused = "UNUSED"
    case used = "USED"
    case expired = "EXPIRED"
    case pending = "PENDING"
    case approved = "APPROVED"
    case delivered = "DELIVERED"
    case cancelled = "CANCELLED"

    var displayName: String {
        switch self {
        case .unused: return "待使用"
        case .used: return "已使用"
        case .expired: return "已过期"
        case .pending: return "待处理"
        case .approved: return "已批准"
        case .delivered: return "已发放"
        case .cancelled: return "已取消"
        }
    }

    var color: Color {
        switch self {
        case .unused: return .orange
        case .used: return .green
        case .expired: return .red
        case .pending: return .orange
        case .approved: return .blue
        case .delivered: return .green
        case .cancelled: return .gray
        }
    }
}

// 不再使用mock数据，所有奖励数据从API获取

// MARK: - 新的数据模型（匹配后端）

// 奖励池类型枚举
enum PoolType: String, Codable {
    case reward = "REWARD"
    case penalty = "PENALTY"

    var displayName: String {
        switch self {
        case .reward: return "奖励池"
        case .penalty: return "惩罚池"
        }
    }
}

// 奖励池模型 - 匹配后端RewardPool实体
struct RewardPool: Codable, Identifiable {
    let id: Int
    let name: String
    let costPoints: Int
    let isEnabled: Bool
    let poolType: PoolType
    let createdTime: String?
    let rewardItems: [RewardItemNew]?

    // 计算属性
    var isAvailable: Bool {
        return isEnabled && poolType == .reward
    }

    var canDraw: Bool {
        return isAvailable
    }

    var formattedCost: String {
        return "\(costPoints)积分"
    }

    var itemCount: Int {
        return rewardItems?.count ?? 0
    }
}

// 奖励项模型 - 匹配后端RewardItem实体
struct RewardItemNew: Codable, Identifiable {
    let id: Int
    let name: String
    let probability: Float
    let stock: Int
    let imageUrl: String?
    let createdTime: String?

    var formattedProbability: String {
        return String(format: "%.1f%%", probability * 100)
    }

    var hasStock: Bool {
        return stock > 0
    }

    var stockText: String {
        return "库存\(stock)个"
    }
}

// 兑换商品模型 - 匹配后端ExchangeItem实体
struct ExchangeItem: Codable, Identifiable {
    let id: Int
    let name: String
    let description: String?
    let requiredPoints: Int
    let stock: Int?
    let category: String?
    let imageUrl: String?
    let isActive: Bool
    let sortOrder: Int?
    let createdTime: String?
    let updatedTime: String?

    var formattedCost: String {
        return "\(requiredPoints)积分"
    }

    var hasStock: Bool {
        return stock == nil || stock == -1 || stock! > 0
    }

    var canExchange: Bool {
        return isActive && hasStock
    }

    var stockText: String? {
        guard let stock = stock, stock != -1 else { return nil }
        return stock > 0 ? "库存\(stock)件" : "缺货"
    }
}

// 抽奖结果模型
struct RewardDrawResult: Codable, Identifiable {
    let id: Int
    let rewardItem: RewardItemNew
    let drawTime: String
    let pointsSpent: Int

    var formattedDrawTime: String {
        guard let date = ISO8601DateFormatter().date(from: drawTime) else {
            return drawTime
        }
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}
