import SwiftUI

// 任务卡片视图
struct TaskCardView: View {
    let task: TaskSummary
    let onTap: () -> Void
    let onStart: (() -> Void)?
    let onComplete: (() -> Void)?
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // 任务头部
                HStack {
                    // 状态指示器
                    StatusIndicator(status: task.status)
                    
                    Spacer()
                    
                    // 积分显示
                    PointsBadge(points: task.basePoints)
                }
                
                // 任务标题
                Text(task.title)
                    .font(.system(size: 18, weight: .semibold, design: .rounded))
                    .foregroundColor(Color(red: 28/255, green: 28/255, blue: 30/255))
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                // 预计时间
                HStack(spacing: 6) {
                    Image(systemName: "clock")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))
                    
                    Text(task.formattedExpectedTime)
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))
                    
                    Spacer()
                }
                
                // 操作按钮
                ActionButtons(
                    task: task,
                    onStart: onStart,
                    onComplete: onComplete
                )
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .shadow(
                color: Color.black.opacity(0.08),
                radius: 8,
                x: 0,
                y: 2
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {
            // 长按操作
        }
    }
}

// 状态指示器
struct StatusIndicator: View {
    let status: TaskStatus
    
    var body: some View {
        HStack(spacing: 6) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            
            Text(status.displayName)
                .font(.system(size: 12, weight: .semibold, design: .rounded))
                .foregroundColor(statusColor)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 4)
        .background(statusColor.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var statusColor: Color {
        switch status {
        case .pending:
            return Color(red: 255/255, green: 149/255, blue: 0/255) // 橙色
        case .inProgress:
            return Color(red: 0/255, green: 122/255, blue: 255/255) // 蓝色
        case .completed, .approved:
            return Color(red: 52/255, green: 199/255, blue: 89/255) // 绿色
        case .overdue:
            return Color(red: 255/255, green: 59/255, blue: 48/255) // 红色
        case .cancelled:
            return Color(red: 142/255, green: 142/255, blue: 147/255) // 灰色
        case .rejected:
            return Color(red: 255/255, green: 59/255, blue: 48/255) // 红色
        }
    }
}

// 积分徽章
struct PointsBadge: View {
    let points: Int
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "star.fill")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.white)
            
            Text("\(points)")
                .font(.system(size: 14, weight: .bold, design: .rounded))
                .foregroundColor(.white)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(
            LinearGradient(
                colors: [
                    Color(red: 255/255, green: 204/255, blue: 0/255),
                    Color(red: 255/255, green: 179/255, blue: 64/255)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(12)
        .shadow(
            color: Color(red: 255/255, green: 204/255, blue: 0/255).opacity(0.3),
            radius: 4,
            x: 0,
            y: 2
        )
    }
}

// 操作按钮
struct ActionButtons: View {
    let task: TaskSummary
    let onStart: (() -> Void)?
    let onComplete: (() -> Void)?

    @StateObject private var screenDimmingManager = ScreenDimmingManager.shared
    
    var body: some View {
        HStack(spacing: 8) {
            if task.status == .pending, let onStart = onStart {
                ActionButton(
                    title: "开始",
                    icon: "play.fill",
                    color: Color(red: 0/255, green: 122/255, blue: 255/255),
                    action: {
                        // 开始任务时启动息屏倒计时
                        screenDimmingManager.startDimmingForTask()
                        onStart()
                    }
                )
            }
            
            if task.status == .inProgress, let onComplete = onComplete {
                ActionButton(
                    title: "完成",
                    icon: "checkmark",
                    color: Color(red: 52/255, green: 199/255, blue: 89/255),
                    action: onComplete
                )
            }
            
            Spacer()
            
            // 查看详情按钮
            Button(action: {
                // 查看详情逻辑在父组件处理
            }) {
                HStack(spacing: 4) {
                    Text("详情")
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .semibold))
                }
                .foregroundColor(Color(red: 0/255, green: 122/255, blue: 255/255))
            }
        }
    }
}

// 通用操作按钮
struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .semibold))
                Text(title)
                    .font(.system(size: 14, weight: .semibold, design: .rounded))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(color)
            .cornerRadius(20)
            .shadow(
                color: color.opacity(0.3),
                radius: 4,
                x: 0,
                y: 2
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 预览 - 使用真实API数据结构，不使用mock数据
struct TaskCardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            TaskCardView(
                task: TaskSummary(
                    id: 1,
                    title: "识字",
                    basePoints: 1,
                    expectedMinutes: 5,
                    status: .pending,
                    dueDate: nil
                ),
                onTap: {},
                onStart: {},
                onComplete: nil
            )

            TaskCardView(
                task: TaskSummary(
                    id: 2,
                    title: "口算",
                    basePoints: 2,
                    expectedMinutes: 10,
                    status: .inProgress,
                    dueDate: nil
                ),
                onTap: {},
                onStart: nil,
                onComplete: {}
            )

            TaskCardView(
                task: TaskSummary(
                    id: 3,
                    title: "写字",
                    basePoints: 3,
                    expectedMinutes: 15,
                    status: .completed,
                    dueDate: nil
                ),
                onTap: {},
                onStart: nil,
                onComplete: nil
            )
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .previewDevice("iPad Pro (12.9-inch) (6th generation)")
    }
}
