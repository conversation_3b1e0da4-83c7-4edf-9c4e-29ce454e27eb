import React from 'react';
import styled from 'styled-components';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { childTheme } from '../../utils/themes';

const NavigationMenu = () => {
  const location = useLocation();
  
  const navItems = [
    {
      path: '/child',
      icon: '📝',
      label: '任务',
      exact: true
    },
    {
      path: '/child/rewards',
      icon: '🎁',
      label: '奖励',
      exact: false
    },
    {
      path: '/child/exchange',
      icon: '🛒',
      label: '兑换',
      exact: false
    },
    {
      path: '/child/points',
      icon: '⭐',
      label: '积分',
      exact: false
    }
  ];
  
  return (
    <NavContainer>
      <NavBackground />
      {navItems.map((item) => {
        const isActive = item.exact 
          ? location.pathname === item.path
          : location.pathname.startsWith(item.path);
          
        return (
          <NavItem 
            key={item.path}
            to={item.path}
            $isActive={isActive}
          >
            <IconWrapper $isActive={isActive}>
              <NavIcon>{item.icon}</NavIcon>
            </IconWrapper>
            <NavLabel>{item.label}</NavLabel>
            {isActive && (
              <ActiveIndicator
                layoutId="activeIndicator"
                initial={false}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              />
            )}
          </NavItem>
        );
      })}
    </NavContainer>
  );
};

const NavContainer = styled.nav`
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 0;
  padding: 0.6rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  position: relative;
  z-index: 10;
  margin: 0;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
`;

const NavBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: -1;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 60%);
    opacity: 0.5;
  }
`;

const NavItem = styled(NavLink)`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 2rem;
  margin: 0 0.5rem;
  border-radius: 1.5rem;
  text-decoration: none;
  color: ${props => props.$isActive ? childTheme.primaryColor : '#666'};
  font-weight: ${props => props.$isActive ? '700' : '500'};
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(1px);
  }
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: ${props => props.$isActive ? 
    `linear-gradient(135deg, ${childTheme.primaryColor}, ${childTheme.secondaryColor})` : 
    'rgba(255, 255, 255, 0.7)'};
  border-radius: 50%;
  margin-bottom: 0.5rem;
  box-shadow: ${props => props.$isActive ? 
    '0 6px 16px rgba(0, 122, 255, 0.25)' : 
    '0 4px 12px rgba(0, 0, 0, 0.05)'};
  transition: all 0.3s ease;
`;

const NavIcon = styled.span`
  font-size: 1.8rem;
  filter: saturate(1.5);
`;

const NavLabel = styled.span`
  font-size: 1rem;
  margin-top: 0.2rem;
`;

const ActiveIndicator = styled(motion.div)`
  position: absolute;
  bottom: 0;
  left: 25%;
  right: 25%;
  width: 50%;
  height: 3px;
  background: ${childTheme.gradients.primary};
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0, 122, 255, 0.3);
`;

export default NavigationMenu; 