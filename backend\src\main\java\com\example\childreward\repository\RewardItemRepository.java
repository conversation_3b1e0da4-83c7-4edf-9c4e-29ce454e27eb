package com.example.childreward.repository;

import com.example.childreward.entity.RewardItem;
import com.example.childreward.entity.RewardPool;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RewardItemRepository extends JpaRepository<RewardItem, Long> {
    
    List<RewardItem> findByRewardPool(RewardPool rewardPool);
    
    List<RewardItem> findByRewardPoolAndStockGreaterThan(RewardPool rewardPool, Integer stock);
} 