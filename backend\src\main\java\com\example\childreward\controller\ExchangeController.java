package com.example.childreward.controller;

import com.example.childreward.entity.ExchangeItem;
import com.example.childreward.entity.ExchangeRecord;
import com.example.childreward.service.ExchangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 兑换控制器
 */
@RestController
@RequestMapping("/api/exchange")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class ExchangeController {
    
    private final ExchangeService exchangeService;
    
    // ========== 兑换商品管理（家长端） ==========
    
    /**
     * 获取所有兑换商品（管理端）
     */
    @GetMapping("/items/admin")
    public ResponseEntity<List<ExchangeItem>> getAllExchangeItems() {
        List<ExchangeItem> items = exchangeService.getAllExchangeItems();
        return ResponseEntity.ok(items);
    }
    
    /**
     * 获取可用的兑换商品（用户端）
     */
    @GetMapping("/items")
    public ResponseEntity<List<ExchangeItem>> getAvailableExchangeItems(
            @RequestParam(required = false) String category) {
        List<ExchangeItem> items;
        if (category != null && !category.trim().isEmpty()) {
            items = exchangeService.getAvailableExchangeItemsByCategory(category);
        } else {
            items = exchangeService.getAvailableExchangeItems();
        }
        return ResponseEntity.ok(items);
    }
    
    /**
     * 获取所有商品分类
     */
    @GetMapping("/categories")
    public ResponseEntity<List<String>> getAllCategories() {
        List<String> categories = exchangeService.getAllCategories();
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 根据ID获取兑换商品
     */
    @GetMapping("/items/{id}")
    public ResponseEntity<ExchangeItem> getExchangeItemById(@PathVariable Long id) {
        try {
            ExchangeItem item = exchangeService.getExchangeItemById(id);
            return ResponseEntity.ok(item);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 创建兑换商品
     */
    @PostMapping("/items")
    public ResponseEntity<ExchangeItem> createExchangeItem(@RequestBody ExchangeItem exchangeItem) {
        try {
            ExchangeItem createdItem = exchangeService.createExchangeItem(exchangeItem);
            return ResponseEntity.ok(createdItem);
        } catch (Exception e) {
            log.error("创建兑换商品失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 更新兑换商品
     */
    @PutMapping("/items/{id}")
    public ResponseEntity<ExchangeItem> updateExchangeItem(
            @PathVariable Long id, 
            @RequestBody ExchangeItem exchangeItem) {
        try {
            ExchangeItem updatedItem = exchangeService.updateExchangeItem(id, exchangeItem);
            return ResponseEntity.ok(updatedItem);
        } catch (RuntimeException e) {
            log.error("更新兑换商品失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 删除兑换商品
     */
    @DeleteMapping("/items/{id}")
    public ResponseEntity<Void> deleteExchangeItem(@PathVariable Long id) {
        try {
            exchangeService.deleteExchangeItem(id);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            log.error("删除兑换商品失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 启用/禁用兑换商品
     */
    @PatchMapping("/items/{id}/toggle")
    public ResponseEntity<ExchangeItem> toggleExchangeItemStatus(@PathVariable Long id) {
        try {
            ExchangeItem item = exchangeService.toggleExchangeItemStatus(id);
            return ResponseEntity.ok(item);
        } catch (RuntimeException e) {
            log.error("切换兑换商品状态失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    // ========== 兑换操作（儿童端） ==========
    
    /**
     * 执行兑换操作
     */
    @PostMapping("/exchange/{itemId}")
    public ResponseEntity<?> exchangeItem(@PathVariable Long itemId) {
        try {
            ExchangeRecord record = exchangeService.exchangeItem(itemId);
            return ResponseEntity.ok(record);
        } catch (RuntimeException e) {
            log.error("兑换失败", e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 使用兑换记录
     */
    @PatchMapping("/records/{recordId}/use")
    public ResponseEntity<?> useExchangeRecord(
            @PathVariable Long recordId,
            @RequestBody(required = false) Map<String, String> request) {
        try {
            String notes = request != null ? request.get("notes") : null;
            ExchangeRecord record = exchangeService.useExchangeRecord(recordId, notes);
            return ResponseEntity.ok(record);
        } catch (RuntimeException e) {
            log.error("使用兑换记录失败", e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    // ========== 兑换记录查询 ==========
    
    /**
     * 获取所有兑换记录
     */
    @GetMapping("/records")
    public ResponseEntity<List<ExchangeRecord>> getAllExchangeRecords() {
        List<ExchangeRecord> records = exchangeService.getAllExchangeRecords();
        return ResponseEntity.ok(records);
    }
    
    /**
     * 获取未使用的兑换记录
     */
    @GetMapping("/records/unused")
    public ResponseEntity<List<ExchangeRecord>> getUnusedExchangeRecords() {
        List<ExchangeRecord> records = exchangeService.getUnusedExchangeRecords();
        return ResponseEntity.ok(records);
    }
    
    /**
     * 根据ID获取兑换记录
     */
    @GetMapping("/records/{id}")
    public ResponseEntity<ExchangeRecord> getExchangeRecordById(@PathVariable Long id) {
        try {
            ExchangeRecord record = exchangeService.getExchangeRecordById(id);
            return ResponseEntity.ok(record);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
