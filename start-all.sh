#!/bin/bash
set -e

echo "=========================================="
echo "  儿童奖励系统 - 一体化容器启动"
echo "=========================================="
echo "启动时间: $(date)"
echo "Java版本: $(java -version 2>&1 | head -1)"
echo "数据库: ${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}"
echo "JVM参数: ${JAVA_OPTS}"
echo "=========================================="

# 创建必要的目录
mkdir -p /app/logs /var/log/nginx /var/log/supervisor

# 检查前端文件
if [ ! -f "/var/www/html/index.html" ]; then
    echo "ERROR: 前端文件未找到，请检查构建过程"
    exit 1
fi

# 检查后端JAR文件
if [ ! -f "/app/backend.jar" ]; then
    echo "ERROR: 后端JAR文件未找到，请检查构建过程"
    exit 1
fi

# 测试Nginx配置
echo "检查Nginx配置..."
nginx -t
if [ $? -ne 0 ]; then
    echo "ERROR: Nginx配置错误"
    exit 1
fi

# 显示启动信息
echo "启动服务..."
echo "- Nginx: 端口80 (前端 + API代理)"
echo "- Spring Boot: 端口18080 (后端API)"
echo ""
echo "访问地址:"
echo "- 家长端: http://容器IP/"
echo "- 儿童端: http://容器IP/child"
echo "- 后端API: http://容器IP/actuator/health"
echo ""

# 启动supervisor管理所有服务
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf