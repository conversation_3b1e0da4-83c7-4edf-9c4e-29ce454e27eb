#!/bin/bash

echo "🚀 测试头像加载"
echo "================"

echo "1. 检查头像文件配置："
cat ChildRewardApp/Assets.xcassets/avatar.imageset/Contents.json
echo ""

echo "2. 启动模拟器（请在Xcode中手动运行项目）"
echo "   - 打开 ChildRewardApp.xcodeproj"
echo "   - 选择 iPad Pro 模拟器"
echo "   - 点击运行按钮"
echo "   - 查看Xcode控制台的调试输出"
echo ""

echo "3. 预期的调试输出："
echo "   🔍 开始加载头像..."
echo "   ✅ 成功从Assets加载头像，尺寸: (160.0, 114.0)"
echo ""

echo "4. 如果头像仍然不显示，请检查："
echo "   - Xcode控制台的错误信息"
echo "   - 头像是否为圆形并带有蓝紫渐变边框"
echo "   - 点击头像10次是否能切换环境"
echo ""

echo "测试准备完成！请在Xcode中运行项目。"
