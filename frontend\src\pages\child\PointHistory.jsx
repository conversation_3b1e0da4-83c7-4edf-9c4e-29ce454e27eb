import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { childApi, pointApi } from '../../api/apiService';
import { childTheme } from '../../utils/themes';
import { format } from 'date-fns';

const PointHistory = () => {
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState(7);
  const [filter, setFilter] = useState('ALL');
  const [stats, setStats] = useState({
    totalEarned: 0,
    totalSpent: 0,
    net: 0,
  });
  const [currentPoints, setCurrentPoints] = useState(0);
  const [dailyPointsChange, setDailyPointsChange] = useState(0);

  const fetchPointsData = async () => {
    try {
      const pointsResponse = await childApi.getPointBalance();
      setCurrentPoints(pointsResponse.data.totalPoints);

      const todayResponse = await childApi.getTodayPointChanges();
      const todayPoints = todayResponse.data.reduce(
        (total, record) => total + record.pointChange,
        0
      );
      setDailyPointsChange(todayPoints);
    } catch (error) {
      console.error('获取积分数据失败:', error);
    }
  };

  const fetchPointRecords = async () => {
    setLoading(true);
    try {
      const response = await pointApi.getRecentPointRecords(timeRange);
      if (response && response.data) {
        setRecords(response.data);
        calculateStats(response.data);
      } else {
        setRecords([]);
        calculateStats([]);
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      setRecords([]);
      calculateStats([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPointsData();
    fetchPointRecords();
  }, [timeRange]);

  const calculateStats = (data) => {
    const totalEarned = data
      .filter((record) => record.pointChange > 0)
      .reduce((sum, record) => sum + record.pointChange, 0);

    const totalSpent = data
      .filter((record) => record.pointChange < 0)
      .reduce((sum, record) => sum + Math.abs(record.pointChange), 0);

    setStats({
      totalEarned,
      totalSpent,
      net: totalEarned - totalSpent,
    });
  };

  const handleTimeRangeChange = (days) => {
    setTimeRange(days);
  };

  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
  };

  const filteredRecords = records.filter((record) => {
    if (filter === 'EARNED') {
      return record.pointChange > 0;
    } else if (filter === 'SPENT') {
      return record.pointChange < 0;
    }
    return true;
  });

  const getChangeTypeInfo = (changeType) => {
    switch (changeType) {
      case 'TASK_COMPLETION':
        return { label: '完成任务', icon: '✅' };
      case 'TASK_PENALTY':
        return { label: '任务惩罚', icon: '❌' };
      case 'REWARD_EXCHANGE':
        return { label: '兑换奖励', icon: '🎁' };
      case 'MANUAL_ADJUST':
        return { label: '积分调整', icon: '⚙️' };
      default:
        return { label: changeType, icon: '📝' };
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return `今天 ${format(date, 'HH:mm')}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `昨天 ${format(date, 'HH:mm')}`;
    } else {
      return format(date, 'MM-dd HH:mm');
    }
  };

  return (
    <PageContainer>
      <StatsContainer>
        <StatCard>
          <StatLabel>当前积分</StatLabel>
          <StatValue color={childTheme.accentColor}>{currentPoints}</StatValue>
        </StatCard>
        <StatCard>
          <StatLabel>今日变化</StatLabel>
          <StatValue color={dailyPointsChange >= 0 ? childTheme.statusColors.IN_PROGRESS : childTheme.statusColors.REJECTED}>
            {dailyPointsChange >= 0 ? '+' : ''}{dailyPointsChange}
          </StatValue>
        </StatCard>
        <StatCard>
          <StatLabel>总获得</StatLabel>
          <StatValue color={childTheme.statusColors.COMPLETED}>
            {stats.totalEarned}
          </StatValue>
        </StatCard>
        <StatCard>
          <StatLabel>总消费</StatLabel>
          <StatValue color={childTheme.statusColors.REJECTED}>
            {stats.totalSpent}
          </StatValue>
        </StatCard>
      </StatsContainer>
      
      <FilterBar>
        <TimeFilters>
          <TimeButton active={timeRange === 7} onClick={() => handleTimeRangeChange(7)}>最近7天</TimeButton>
          <TimeButton active={timeRange === 30} onClick={() => handleTimeRangeChange(30)}>最近30天</TimeButton>
          <TimeButton active={timeRange === 90} onClick={() => handleTimeRangeChange(90)}>最近90天</TimeButton>
        </TimeFilters>
        
        <TypeFilters>
          <TypeButton active={filter === 'ALL'} onClick={() => handleFilterChange('ALL')}>全部</TypeButton>
          <TypeButton active={filter === 'EARNED'} onClick={() => handleFilterChange('EARNED')} positive>获得</TypeButton>
          <TypeButton active={filter === 'SPENT'} onClick={() => handleFilterChange('SPENT')} negative>消费</TypeButton>
        </TypeFilters>
      </FilterBar>

      {loading ? (
        <LoadingContainer>
          <LoadingDots>
            <Dot delay={0} />
            <Dot delay={0.2} />
            <Dot delay={0.4} />
          </LoadingDots>
          <LoadingText>加载中...</LoadingText>
        </LoadingContainer>
      ) : (
        <RecordsContainer>
          {filteredRecords.length > 0 ? (
            <AnimatePresence>
              {filteredRecords.map(record => {
                const typeInfo = getChangeTypeInfo(record.changeType);
                return (
                  <RecordCard
                    key={record.id}
                    positive={record.pointChange > 0}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                  >
                    <RecordIconContainer>
                      <RecordIcon>{typeInfo.icon}</RecordIcon>
                    </RecordIconContainer>
                    <RecordContent>
                      <RecordTitle>{record.description}</RecordTitle>
                      <RecordSubtitle>{typeInfo.label} • {formatDate(record.recordTime)}</RecordSubtitle>
                    </RecordContent>
                    <RecordPoints positive={record.pointChange > 0}>
                      {record.pointChange > 0 ? '+' : ''}{record.pointChange}
                    </RecordPoints>
                  </RecordCard>
                );
              })}
            </AnimatePresence>
          ) : (
            <EmptyState>
              <EmptyIcon>🔍</EmptyIcon>
              <EmptyText>没有找到积分记录</EmptyText>
              <EmptySubtext>选择不同的时间范围试试看</EmptySubtext>
            </EmptyState>
          )}
        </RecordsContainer>
      )}
    </PageContainer>
  );
};

const PageContainer = styled.div`
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #ebf0f6 100%);
  border-radius: ${childTheme.borderRadius};
  position: relative;
  overflow: hidden;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const StatCard = styled.div`
  padding: 1rem;
  border-radius: ${childTheme.borderRadius};
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const StatValue = styled.div`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${props => props.color};
`;

const StatLabel = styled.div`
  font-size: 1rem;
  color: #666;
`;

const FilterBar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const TimeFilters = styled.div`
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
`;

const TimeButton = styled.button`
  padding: 0.6rem 1rem;
  border: none;
  border-radius: 2rem;
  background: ${props => props.active ? 
    'linear-gradient(135deg, #42a5f5, #1976d2)' :
    'white'};
  color: ${props => props.active ? 'white' : '#666'};
  font-weight: ${props => props.active ? '600' : '400'};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  white-space: nowrap;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  transition: all 0.2s ease;
`;

const TypeFilters = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const TypeButton = styled.button`
  flex: 1;
  padding: 0.6rem;
  border: none;
  border-radius: ${childTheme.borderRadius};
  background: ${props => {
    if (!props.active) return 'white';
    if (props.positive) return 'linear-gradient(135deg, #4CAF50, #8BC34A)';
    if (props.negative) return 'linear-gradient(135deg, #f44336, #e57373)';
    return 'linear-gradient(135deg, #9C27B0, #673AB7)';
  }};
  color: ${props => props.active ? 'white' : '#666'};
  font-weight: ${props => props.active ? '600' : '400'};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  transition: all 0.2s ease;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
`;

const LoadingDots = styled.div`
  display: flex;
  margin-bottom: 1rem;
`;

const Dot = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${childTheme.primaryColor};
  margin: 0 5px;
  animation: bounce 1.4s infinite ease-in-out;
  animation-delay: ${props => props.delay}s;
  
  @keyframes bounce {
    0%, 100% {
      transform: scale(0);
    }
    50% {
      transform: scale(1);
    }
  }
`;

const LoadingText = styled.div`
  font-size: 1rem;
  color: #666;
`;

const RecordsContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 0;
`;

const RecordCard = styled(motion.div)`
  display: flex;
  align-items: center;
  background: white;
  border-radius: ${childTheme.borderRadius};
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-left: 5px solid ${props => props.positive ? 
    childTheme.statusColors.COMPLETED : 
    childTheme.statusColors.REJECTED};
`;

const RecordIconContainer = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
`;

const RecordIcon = styled.div`
  font-size: 1.2rem;
`;

const RecordContent = styled.div`
  flex: 1;
`;

const RecordTitle = styled.div`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
  color: ${childTheme.textColor};
`;

const RecordSubtitle = styled.div`
  font-size: 0.8rem;
  color: #777;
`;

const RecordPoints = styled.div`
  font-size: 1.2rem;
  font-weight: 700;
  color: ${props => props.positive ? 
    childTheme.statusColors.COMPLETED : 
    childTheme.statusColors.REJECTED};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1.5rem;
  animation: float 3s ease-in-out infinite;
  
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
`;

const EmptyText = styled.div`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${childTheme.textColor};
  margin-bottom: 0.5rem;
`;

const EmptySubtext = styled.div`
  font-size: 1rem;
  color: #777;
`;

export default PointHistory; 