#!/bin/bash

# 头像添加脚本
# 使用方法: ./add_avatar.sh /path/to/your/avatar/image.jpg

echo "🖼️  头像添加脚本"
echo "=================="

# 检查参数
if [ $# -eq 0 ]; then
    echo "❌ 请提供头像图片路径"
    echo "使用方法: ./add_avatar.sh /path/to/your/avatar/image.jpg"
    exit 1
fi

AVATAR_PATH="$1"
TARGET_DIR="ChildRewardApp/Assets.xcassets/avatar.imageset"
TARGET_FILE="$TARGET_DIR/avatar.png"

# 检查源文件是否存在
if [ ! -f "$AVATAR_PATH" ]; then
    echo "❌ 头像文件不存在: $AVATAR_PATH"
    exit 1
fi

# 检查目标目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo "❌ 目标目录不存在: $TARGET_DIR"
    echo "请确保在ios-app目录下运行此脚本"
    exit 1
fi

echo "📁 源文件: $AVATAR_PATH"
echo "📁 目标文件: $TARGET_FILE"

# 使用sips命令处理图片（macOS内置工具）
echo "🔄 处理图片..."

# 获取图片尺寸
WIDTH=$(sips -g pixelWidth "$AVATAR_PATH" | tail -1 | awk '{print $2}')
HEIGHT=$(sips -g pixelHeight "$AVATAR_PATH" | tail -1 | awk '{print $2}')

echo "📏 原始尺寸: ${WIDTH}x${HEIGHT}"

# 裁剪为正方形并调整大小
if [ "$WIDTH" -gt "$HEIGHT" ]; then
    SIZE=$HEIGHT
else
    SIZE=$WIDTH
fi

echo "✂️  裁剪为正方形: ${SIZE}x${SIZE}"
echo "📐 调整大小为: 160x160"

# 复制并处理图片
sips -s format png \
     --cropToHeightWidth $SIZE $SIZE \
     --resampleHeightWidth 160 160 \
     "$AVATAR_PATH" \
     --out "$TARGET_FILE"

if [ $? -eq 0 ]; then
    echo "✅ 头像添加成功!"
    echo "📱 请重新编译项目以查看效果"
    echo ""
    echo "💡 提示:"
    echo "   1. 在Xcode中重新编译项目"
    echo "   2. 头像会显示为圆形，带有白色边框"
    echo "   3. 如果看不到效果，请清理项目缓存"
else
    echo "❌ 头像处理失败"
    exit 1
fi
