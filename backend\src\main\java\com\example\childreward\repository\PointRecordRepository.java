package com.example.childreward.repository;

import com.example.childreward.entity.PointRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PointRecordRepository extends JpaRepository<PointRecord, Long> {
    
    List<PointRecord> findByRecordTimeBetweenOrderByRecordTimeDesc(LocalDateTime start, LocalDateTime end);
    
    @Query("SELECT SUM(p.pointChange) FROM PointRecord p")
    Integer getTotalPoints();
    
    List<PointRecord> findByRelatedTaskId(Long taskId);
    
    List<PointRecord> findByRelatedRewardId(Long rewardId);
    
    @Query("SELECT p FROM PointRecord p WHERE DATE(p.recordTime) = CURRENT_DATE ORDER BY p.recordTime DESC")
    List<PointRecord> findTodayRecords();

    @Query("SELECT COALESCE(SUM(p.pointChange), 0) FROM PointRecord p WHERE DATE(p.recordTime) = CURRENT_DATE")
    Integer getTodayPointsChange();

    @Query("SELECT COALESCE(SUM(p.pointChange), 0) FROM PointRecord p WHERE DATE(p.recordTime) = :date")
    Integer getPointsChangeByDate(@Param("date") LocalDate date);
} 