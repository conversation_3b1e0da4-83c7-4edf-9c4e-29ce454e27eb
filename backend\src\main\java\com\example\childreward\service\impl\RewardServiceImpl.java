package com.example.childreward.service.impl;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.RewardItem;
import com.example.childreward.entity.RewardPool;
import com.example.childreward.entity.RewardPool.PoolType;
import com.example.childreward.repository.RewardItemRepository;
import com.example.childreward.repository.RewardPoolRepository;
import com.example.childreward.service.PointService;
import com.example.childreward.service.RewardService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RewardServiceImpl implements RewardService {

    private final RewardPoolRepository rewardPoolRepository;
    private final RewardItemRepository rewardItemRepository;
    private final PointService pointService;
    private final Random random = new Random();

    @Override
    @Transactional
    public RewardPool createRewardPool(RewardPool rewardPool) {
        return rewardPoolRepository.save(rewardPool);
    }

    @Override
    @Transactional
    public RewardPool updateRewardPool(Long id, RewardPool rewardPool) {
        RewardPool existingPool = getRewardPoolById(id);
        
        existingPool.setName(rewardPool.getName());
        existingPool.setCostPoints(rewardPool.getCostPoints());
        existingPool.setIsEnabled(rewardPool.getIsEnabled());
        existingPool.setPoolType(rewardPool.getPoolType());
        
        return rewardPoolRepository.save(existingPool);
    }

    @Override
    @Transactional
    public void deleteRewardPool(Long id) {
        RewardPool pool = getRewardPoolById(id);
        rewardPoolRepository.delete(pool);
    }

    @Override
    public List<RewardPool> getAllEnabledPools() {
        return rewardPoolRepository.findByIsEnabledTrue();
    }
    
    @Override
    public List<RewardPool> getAllEnabledRewardPools() {
        return rewardPoolRepository.findByIsEnabledTrueAndPoolType(PoolType.REWARD);
    }
    
    @Override
    public List<RewardPool> getAllEnabledPenaltyPools() {
        return rewardPoolRepository.findByIsEnabledTrueAndPoolType(PoolType.PENALTY);
    }

    @Override
    public RewardPool getRewardPoolById(Long id) {
        return rewardPoolRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("奖品池不存在: " + id));
    }

    @Override
    @Transactional
    public RewardItem addRewardItem(Long poolId, RewardItem rewardItem) {
        RewardPool pool = getRewardPoolById(poolId);
        rewardItem.setRewardPool(pool);
        return rewardItemRepository.save(rewardItem);
    }

    @Override
    @Transactional
    public RewardItem updateRewardItem(Long id, RewardItem rewardItem) {
        RewardItem existingItem = getRewardItemById(id);
        
        existingItem.setName(rewardItem.getName());
        existingItem.setProbability(rewardItem.getProbability());
        existingItem.setStock(rewardItem.getStock());
        existingItem.setImageUrl(rewardItem.getImageUrl());
        
        return rewardItemRepository.save(existingItem);
    }

    @Override
    @Transactional
    public void deleteRewardItem(Long id) {
        RewardItem item = getRewardItemById(id);
        rewardItemRepository.delete(item);
    }

    @Override
    public RewardItem getRewardItemById(Long id) {
        return rewardItemRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("奖品不存在: " + id));
    }

    @Override
    @Transactional
    public RewardItem drawReward(Long poolId) {
        RewardPool pool = getRewardPoolById(poolId);
        
        // 检查是否为奖励池
        if (pool.getPoolType() != PoolType.REWARD) {
            throw new IllegalStateException("该池不是奖励池，不能进行奖励抽奖");
        }
        
        // 检查积分是否足够
        Integer totalPoints = pointService.getTotalPoints();
        if (totalPoints < pool.getCostPoints()) {
            throw new IllegalStateException("积分不足，无法抽奖");
        }
        
        // 获取有库存的奖品
        List<RewardItem> availableItems = rewardItemRepository.findByRewardPoolAndStockGreaterThan(pool, 0);
        if (availableItems.isEmpty()) {
            throw new IllegalStateException("该奖品池没有可用奖品");
        }
        
        // 计算总概率
        float totalProbability = (float) availableItems.stream()
                .mapToDouble(RewardItem::getProbability)
                .sum();
        
        // 归一化概率
        List<RewardItem> normalizedItems = availableItems.stream()
                .peek(item -> item.setProbability(item.getProbability() / totalProbability))
                .collect(Collectors.toList());
        
        // 抽奖
        float randomValue = random.nextFloat();
        float cumulativeProbability = 0;
        RewardItem selectedItem = null;
        
        for (RewardItem item : normalizedItems) {
            cumulativeProbability += item.getProbability();
            if (randomValue <= cumulativeProbability) {
                selectedItem = item;
                break;
            }
        }
        
        // 如果没有选中（理论上不会发生，但以防万一）
        if (selectedItem == null) {
            selectedItem = normalizedItems.get(normalizedItems.size() - 1);
        }
        
        // 减少库存
        selectedItem.setStock(selectedItem.getStock() - 1);
        rewardItemRepository.save(selectedItem);
        
        // 扣减积分
        pointService.deductPoints(
                pool.getCostPoints(),
                PointRecord.ChangeType.REWARD_EXCHANGE,
                "抽奖获得: " + selectedItem.getName(),
                selectedItem.getId()
        );
        
        return selectedItem;
    }
    
    @Override
    @Transactional
    public RewardItem drawPenalty(Long poolId) {
        RewardPool pool = getRewardPoolById(poolId);
        
        // 检查是否为惩罚池
        if (pool.getPoolType() != PoolType.PENALTY) {
            throw new IllegalStateException("该池不是惩罚池，不能进行惩罚抽奖");
        }
        
        // 获取有库存的惩罚项
        List<RewardItem> availableItems = rewardItemRepository.findByRewardPoolAndStockGreaterThan(pool, 0);
        if (availableItems.isEmpty()) {
            throw new IllegalStateException("该惩罚池没有可用惩罚项");
        }
        
        // 计算总概率
        float totalProbability = (float) availableItems.stream()
                .mapToDouble(RewardItem::getProbability)
                .sum();
        
        // 归一化概率
        List<RewardItem> normalizedItems = availableItems.stream()
                .peek(item -> item.setProbability(item.getProbability() / totalProbability))
                .collect(Collectors.toList());
        
        // 抽取惩罚
        float randomValue = random.nextFloat();
        float cumulativeProbability = 0;
        RewardItem selectedItem = null;
        
        for (RewardItem item : normalizedItems) {
            cumulativeProbability += item.getProbability();
            if (randomValue <= cumulativeProbability) {
                selectedItem = item;
                break;
            }
        }
        
        // 如果没有选中（理论上不会发生，但以防万一）
        if (selectedItem == null) {
            selectedItem = normalizedItems.get(normalizedItems.size() - 1);
        }
        
        // 减少库存
        selectedItem.setStock(selectedItem.getStock() - 1);
        rewardItemRepository.save(selectedItem);
        
        // 增加积分（执行惩罚任务后获得积分）
        pointService.addPoints(
                pool.getCostPoints(),
                PointRecord.ChangeType.TASK_PENALTY, // 使用任务惩罚类型
                "执行惩罚任务: " + selectedItem.getName(),
                selectedItem.getId()
        );
        
        return selectedItem;
    }
} 