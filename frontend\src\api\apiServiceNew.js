import { getApiClient, getApiBaseUrl } from './apiConfig.js';

// 任务相关API
export const taskApi = {
  // 获取任务列表
  getTasks: (date) => {
    const client = getApiClient();
    return client.get(date ? `/tasks/date/${date}` : '/tasks/today');
  },
  
  // 获取所有任务
  getAllTasks: () => {
    const client = getApiClient();
    return client.get('/tasks/all');
  },
  
  // 获取待审批任务
  getPendingTasks: (startDate, endDate) => {
    let url = '/tasks/pending';
    const params = [];

    if (startDate) params.push(`startDate=${startDate}`);
    if (endDate) params.push(`endDate=${endDate}`);

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    const client = getApiClient();
    return client.get(url);
  },
  
  // 获取已完成任务
  getCompletedTasks: () => {
    // 临时方案：获取所有任务，前端过滤已完成状态（兼容大小写和不同格式）
    const client = getApiClient();
    return client.get('/tasks/today').then(response => {
      return {
        ...response,
        data: response.data.filter(task => {
          const status = task.status ? task.status.toString().toUpperCase() : '';
          return status === 'COMPLETED' || status === 'APPROVED' || status === 'COMPLETE';
        })
      };
    });
  },
  
  // 获取已拒绝任务
  getRejectedTasks: () => {
    // 临时方案：获取所有任务，前端过滤已拒绝状态（兼容大小写和不同格式）
    const client = getApiClient();
    return client.get('/tasks/today').then(response => {
      return {
        ...response,
        data: response.data.filter(task => {
          const status = task.status ? task.status.toString().toUpperCase() : '';
          return status === 'REJECTED' || status === 'REJECT';
        })
      };
    });
  },

  // 获取指定日期范围和状态的任务
  getTasksByDateRangeAndStatus: (startDate, endDate, status) => {
    let url = '/tasks/range';
    const params = [];

    if (startDate) params.push(`startDate=${startDate}`);
    if (endDate) params.push(`endDate=${endDate}`);
    if (status) params.push(`status=${status}`);

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    const client = getApiClient();
    return client.get(url);
  },
  
  // 创建任务
  createTask: (taskData) => {
    const client = getApiClient();
    return client.post('/tasks', taskData);
  },
  
  // 更新任务
  updateTask: (taskId, taskData) => {
    const client = getApiClient();
    return client.put(`/tasks/${taskId}`, taskData);
  },
  
  // 删除任务
  deleteTask: (taskId) => {
    const client = getApiClient();
    return client.delete(`/tasks/${taskId}`);
  },
  
  // 开始任务
  startTask: (taskId) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/start`);
  },
  
  // 完成任务
  completeTask: (taskId) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/complete`);
  },
  
  // 审批任务
  approveTask: (taskId, actualPoints) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/approve?actualPoints=${actualPoints}`);
  },
  
  // 拒绝任务
  rejectTask: (taskId, reason) => {
    const client = getApiClient();
    return client.post(`/tasks/${taskId}/reject${reason ? `?reason=${reason}` : ''}`);
  }
};

// 积分相关API
export const pointApi = {
  // 获取总积分
  getTotalPoints: () => {
    const client = getApiClient();
    return client.get('/points/total');
  },
  
  // 获取今日积分变化
  getTodayPoints: () => {
    const client = getApiClient();
    return client.get('/points/today');
  },
  
  // 获取指定日期的积分变化
  getPointsByDate: (date) => {
    const client = getApiClient();
    return client.get(`/points/date/${date}`);
  },
  
  // 获取积分记录
  getPointRecords: (startTime, endTime) => {
    let url = '/points/records';
    const params = [];
    
    if (startTime) params.push(`startTime=${encodeURIComponent(startTime.toISOString())}`);
    if (endTime) params.push(`endTime=${encodeURIComponent(endTime.toISOString())}`);
    
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }
    
    const API_BASE_URL = getApiBaseUrl();
    console.log('🔍 发送积分记录请求:', `${API_BASE_URL}${url}`);
    console.log('请求参数:', { startTime, endTime });
    
    const client = getApiClient();
    return client.get(url)
      .then(response => {
        console.log('✅ 积分记录响应:', response);
        
        // 检查响应是否为空
        if (!response) {
          console.warn('⚠️ 响应对象为空');
          return { data: [] };
        }
        
        // 检查data是否存在
        if (!response.data && !Array.isArray(response.data)) {
          console.warn('⚠️ 响应数据为空或非数组:', response);
          return { ...response, data: [] };
        }
        
        return response;
      })
      .catch(error => {
        console.error('❌ 获取积分记录失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        return { data: [] };
      });
  },
  
  // 获取最近几天的积分记录（简化版）
  getRecentPointRecords: (days = 7) => {
    console.log('🔍 发送获取最近积分记录请求, days=' + days);
    
    const client = getApiClient();
    return client.get(`/points/recent?days=${days}`)
      .then(response => {
        console.log('✅ 最近积分记录响应:', response);
        
        // 检查响应是否为空
        if (!response) {
          console.warn('⚠️ 响应对象为空');
          return { data: [] };
        }
        
        // 检查data是否存在且是数组
        if (!response.data) {
          console.warn('⚠️ 响应数据为空');
          return { ...response, data: [] };
        }
        
        // 打印数据条数
        if (Array.isArray(response.data)) {
          console.log(`📊 获取到 ${response.data.length} 条积分记录`);
          if (response.data.length > 0) {
            console.log('📝 第一条记录:', response.data[0]);
          }
        }
        
        return response;
      })
      .catch(error => {
        console.error('❌ 获取最近积分记录失败:', error);
        if (error.response) {
          console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请检查网络或服务器状态');
        } else {
          console.error('请求配置错误:', error.message);
        }
        return { data: [] };
      });
  },
  
  // 添加积分
  addPoints: (points, description) => {
    const client = getApiClient();
    return client.post('/points/add', null, { 
      params: { points, description } 
    });
  },
  
  // 扣减积分
  deductPoints: (points, description) => {
    const client = getApiClient();
    return client.post('/points/deduct', null, { 
      params: { points, description } 
    });
  },
  
  // 扣减惩罚积分
  deductPunishmentPoints: (points, description) => {
    const client = getApiClient();
    return client.post('/points/deduct/punishment', null, {
      params: { points, description }
    });
  },

  // 手动调整积分（正数为加分，负数为扣分）
  manualAdjustPoints: (points, description) => {
    const client = getApiClient();
    if (points > 0) {
      return client.post('/points/add', null, {
        params: { points, description }
      });
    } else if (points < 0) {
      return client.post('/points/deduct', null, {
        params: { points: Math.abs(points), description }
      });
    } else {
      return Promise.reject(new Error('积分变化不能为0'));
    }
  }
};
