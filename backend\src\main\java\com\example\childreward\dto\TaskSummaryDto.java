package com.example.childreward.dto;

import com.example.childreward.entity.TaskStatus;
import com.example.childreward.entity.TaskType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 任务摘要DTO - 用于列表查询，不包含详情字段
 * 优化性能，减少数据传输量
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskSummaryDto {
    
    private Long id;
    
    /**
     * 关联的计划任务模板ID
     */
    private Long sourceTemplateId;
    
    /**
     * 任务标题
     */
    private String title;
    
    /**
     * 预计耗时（分钟）
     */
    private Integer expectedMinutes;
    
    /**
     * 基础积分
     */
    private Integer basePoints;
    
    /**
     * 截止时间
     */
    private LocalTime dueTime;
    
    /**
     * 截止日期
     */
    private LocalDate dueDate;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 任务类型
     */
    private TaskType taskType;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 实际获得积分
     */
    private Integer actualPoints;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 计划日期
     */
    private LocalDate scheduledDate;
    
    /**
     * 实际耗时（分钟）- 计算字段
     */
    public Integer getActualMinutes() {
        if (startTime != null && endTime != null) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
        return null;
    }
}