package com.example.childreward.controller;

import com.example.childreward.entity.RewardItem;
import com.example.childreward.entity.RewardPool;
import com.example.childreward.service.RewardService;
import com.example.childreward.service.PointService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*") // 允许所有来源跨域请求
@RequestMapping("/api/rewards")
@RequiredArgsConstructor
public class RewardController {

    private final RewardService rewardService;
    private final PointService pointService;

    // 奖品池管理接口
    @PostMapping("/pools")
    public ResponseEntity<RewardPool> createRewardPool(@RequestBody RewardPool rewardPool) {
        return new ResponseEntity<>(rewardService.createRewardPool(rewardPool), HttpStatus.CREATED);
    }

    @PutMapping("/pools/{id}")
    public ResponseEntity<RewardPool> updateRewardPool(@PathVariable Long id, @RequestBody RewardPool rewardPool) {
        return ResponseEntity.ok(rewardService.updateRewardPool(id, rewardPool));
    }

    @DeleteMapping("/pools/{id}")
    public ResponseEntity<Void> deleteRewardPool(@PathVariable Long id) {
        rewardService.deleteRewardPool(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/pools")
    public ResponseEntity<List<RewardPool>> getAllEnabledPools() {
        return ResponseEntity.ok(rewardService.getAllEnabledPools());
    }
    
    // 获取所有启用的奖励池（供家长端使用）
    @GetMapping("/pools/rewards")
    public ResponseEntity<List<RewardPool>> getRewardPools() {
        return ResponseEntity.ok(rewardService.getAllEnabledRewardPools());
    }
    
    // 获取所有启用的惩罚池（供家长端使用）
    @GetMapping("/pools/penalties")
    public ResponseEntity<List<RewardPool>> getPenaltyPools() {
        return ResponseEntity.ok(rewardService.getAllEnabledPenaltyPools());
    }
    
    // 获取可用奖励池，供小孩端使用
    @GetMapping("/pools/available")
    public ResponseEntity<List<RewardPool>> getAvailableRewardPools() {
        // 获取当前积分
        Integer totalPoints = pointService.getTotalPoints();
        
        // 如果积分为负，返回惩罚池；否则返回奖励池
        if (totalPoints < 0) {
            return ResponseEntity.ok(rewardService.getAllEnabledPenaltyPools());
        } else {
            return ResponseEntity.ok(rewardService.getAllEnabledRewardPools());
        }
    }

    @GetMapping("/pools/{id}")
    public ResponseEntity<RewardPool> getRewardPoolById(@PathVariable Long id) {
        return ResponseEntity.ok(rewardService.getRewardPoolById(id));
    }

    // 奖品项管理接口
    @PostMapping("/pools/{poolId}/items")
    public ResponseEntity<RewardItem> addRewardItem(@PathVariable Long poolId, @RequestBody RewardItem rewardItem) {
        return new ResponseEntity<>(rewardService.addRewardItem(poolId, rewardItem), HttpStatus.CREATED);
    }

    @PutMapping("/items/{id}")
    public ResponseEntity<RewardItem> updateRewardItem(@PathVariable Long id, @RequestBody RewardItem rewardItem) {
        return ResponseEntity.ok(rewardService.updateRewardItem(id, rewardItem));
    }

    @DeleteMapping("/items/{id}")
    public ResponseEntity<Void> deleteRewardItem(@PathVariable Long id) {
        rewardService.deleteRewardItem(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/items/{id}")
    public ResponseEntity<RewardItem> getRewardItemById(@PathVariable Long id) {
        return ResponseEntity.ok(rewardService.getRewardItemById(id));
    }

    // 抽奖接口
    @PostMapping("/pools/{poolId}/draw")
    public ResponseEntity<RewardItem> drawReward(@PathVariable Long poolId) {
        return ResponseEntity.ok(rewardService.drawReward(poolId));
    }
    
    // 惩罚抽奖接口
    @PostMapping("/pools/{poolId}/draw-penalty")
    public ResponseEntity<RewardItem> drawPenalty(@PathVariable Long poolId) {
        return ResponseEntity.ok(rewardService.drawPenalty(poolId));
    }
} 