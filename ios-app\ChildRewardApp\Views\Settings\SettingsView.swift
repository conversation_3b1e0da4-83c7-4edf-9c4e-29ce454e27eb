import SwiftUI

// 设置页面视图
struct SettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @StateObject private var screenDimmingManager = ScreenDimmingManager.shared
    @StateObject private var environmentManager = EnvironmentManager.shared

    @State private var showingResetAlert = false
    @State private var showingEnvironmentSelector = false

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 息屏设置卡片
                screenDimmingCard

                // 应用设置卡片
                appSettingsCard

                // 环境设置卡片
                environmentCard

                // 系统操作卡片
                systemActionsCard

                Spacer(minLength: 32)
            }
            .padding(32)
        }
        .background(Color(.systemGroupedBackground))
        .alert("重置设置", isPresented: $showingResetAlert) {
            Button("取消", role: .cancel) { }
            Button("重置", role: .destructive) {
                settingsManager.resetToDefaults()
            }
        } message: {
            Text("确定要重置所有设置为默认值吗？此操作不可撤销。")
        }
        .sheet(isPresented: $showingEnvironmentSelector) {
            EnvironmentSelectorView()
        }
    }
    
    // 息屏设置卡片
    private var screenDimmingCard: some View {
        SettingsCard(
            title: "息屏设置",
            icon: "moon.fill",
            color: .indigo
        ) {
            VStack(spacing: 16) {
                // 息屏功能开关
                SettingsToggle(
                    title: "启用息屏功能",
                    subtitle: "开始任务后自动调暗屏幕",
                    isOn: $settingsManager.isScreenDimmingEnabled
                ) { enabled in
                    settingsManager.toggleScreenDimming(enabled)
                }
                
                if settingsManager.isScreenDimmingEnabled {
                    Divider()
                    
                    // 息屏延迟时间设置
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("息屏延迟时间")
                                .font(.body)
                                .fontWeight(.medium)
                            
                            Spacer()
                            
                            Text("\(Int(settingsManager.screenDimmingDelay))秒")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        
                        Slider(
                            value: $settingsManager.screenDimmingDelay,
                            in: 3...30,
                            step: 1
                        ) { _ in
                            settingsManager.updateScreenDimmingDelay(settingsManager.screenDimmingDelay)
                        }
                        .accentColor(.indigo)
                        
                        Text("开始任务后等待多长时间自动息屏")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Divider()
                    
                    // 当前状态显示
                    HStack {
                        Text("当前状态")
                            .font(.body)
                            .fontWeight(.medium)

                        Spacer()

                        Text(screenDimmingManager.statusDescription)
                            .font(.body)
                            .foregroundColor(screenDimmingManager.isDimmed ? .orange : .green)
                    }

                    Divider()

                    // 测试按钮
                    Button(action: {
                        print("🧪 用户点击测试息屏按钮")
                        screenDimmingManager.startDimmingForTask()
                    }) {
                        HStack {
                            Image(systemName: "play.circle.fill")
                                .foregroundColor(.blue)

                            Text("测试息屏功能")
                                .font(.body)
                                .fontWeight(.medium)

                            Spacer()

                            Text("点击测试")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    // 应用设置卡片
    private var appSettingsCard: some View {
        SettingsCard(
            title: "应用设置",
            icon: "gear",
            color: .blue
        ) {
            VStack(spacing: 16) {
                SettingsToggle(
                    title: "声音效果",
                    subtitle: "启用按钮点击和操作音效",
                    isOn: $settingsManager.soundEnabled
                ) { enabled in
                    settingsManager.updateSoundEnabled(enabled)
                }
                
                Divider()
                
                SettingsToggle(
                    title: "震动反馈",
                    subtitle: "启用触觉反馈",
                    isOn: $settingsManager.vibrationEnabled
                ) { enabled in
                    settingsManager.updateVibrationEnabled(enabled)
                }
                
                Divider()
                
                SettingsToggle(
                    title: "自动刷新",
                    subtitle: "定期自动刷新数据",
                    isOn: $settingsManager.autoRefreshEnabled
                ) { enabled in
                    settingsManager.updateAutoRefreshEnabled(enabled)
                }
                
                if settingsManager.autoRefreshEnabled {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("刷新间隔")
                                .font(.body)
                                .fontWeight(.medium)

                            Spacer()

                            Text("\(Int(settingsManager.autoRefreshInterval))秒")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }

                        Slider(
                            value: $settingsManager.autoRefreshInterval,
                            in: 10...120,
                            step: 10
                        ) { _ in
                            settingsManager.updateAutoRefreshInterval(settingsManager.autoRefreshInterval)
                        }
                        .accentColor(.blue)
                    }
                }

                Divider()

                SettingsToggle(
                    title: "信息面板",
                    subtitle: "显示右侧积分统计信息面板",
                    isOn: $settingsManager.infoPanelEnabled
                ) { enabled in
                    settingsManager.updateInfoPanelEnabled(enabled)
                }

                Divider()

                SettingsToggle(
                    title: "奖励页面",
                    subtitle: "显示奖励中心页面入口",
                    isOn: $settingsManager.rewardTabEnabled
                ) { enabled in
                    settingsManager.updateRewardTabEnabled(enabled)
                }
            }
        }
    }
    
    // 环境设置卡片
    private var environmentCard: some View {
        SettingsCard(
            title: "环境设置",
            icon: "network",
            color: .green
        ) {
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("当前环境")
                            .font(.body)
                            .fontWeight(.medium)
                        
                        Text(environmentManager.currentEnvironmentName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("切换环境") {
                        showingEnvironmentSelector = true
                    }
                    .font(.body)
                    .foregroundColor(.green)
                }
                
                Divider()
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("API地址")
                        .font(.body)
                        .fontWeight(.medium)
                    
                    Text(environmentManager.currentBaseURL)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(nil)
                }
            }
        }
    }
    
    // 系统操作卡片
    private var systemActionsCard: some View {
        SettingsCard(
            title: "系统操作",
            icon: "wrench.and.screwdriver",
            color: .orange
        ) {
            VStack(spacing: 12) {
                Button(action: {
                    showingResetAlert = true
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                            .foregroundColor(.orange)
                        
                        Text("重置所有设置")
                            .font(.body)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// 设置卡片组件
struct SettingsCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: Content

    init(
        title: String,
        icon: String,
        color: Color,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.icon = icon
        self.color = color
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片标题
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 28, height: 28)

                Text(title)
                    .font(.title3)
                    .fontWeight(.semibold)

                Spacer()
            }

            // 卡片内容
            content
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
    }
}

// 设置开关组件
struct SettingsToggle: View {
    let title: String
    let subtitle: String?
    @Binding var isOn: Bool
    let onToggle: (Bool) -> Void

    init(
        title: String,
        subtitle: String? = nil,
        isOn: Binding<Bool>,
        onToggle: @escaping (Bool) -> Void
    ) {
        self.title = title
        self.subtitle = subtitle
        self._isOn = isOn
        self.onToggle = onToggle
    }

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .onChange(of: isOn) { newValue in
                    onToggle(newValue)
                }
        }
    }
}

// 环境选择器视图
struct EnvironmentSelectorView: View {
    @StateObject private var environmentManager = EnvironmentManager.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                Text("选择环境")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 20)

                VStack(spacing: 16) {
                    ForEach(EnvironmentManager.Environment.allCases, id: \.self) { environment in
                        Button(action: {
                            environmentManager.switchEnvironment(to: environment)
                            dismiss()
                        }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(environment.rawValue)
                                        .font(.headline)
                                        .fontWeight(.semibold)

                                    Text(environment.baseURL)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                if environmentManager.currentEnvironment == environment {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.title2)
                                }
                            }
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(
                                environmentManager.currentEnvironment == environment
                                    ? Color.green.opacity(0.1)
                                    : Color(.systemGray6)
                            )
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 32)

                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// 预览
struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView()
            .previewDevice("iPad Pro (12.9-inch) (6th generation)")
    }
}
