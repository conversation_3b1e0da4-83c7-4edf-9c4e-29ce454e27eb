# 🔋 系统级息屏功能测试指南

## 功能说明

这个功能专门为您的需求设计：**小孩完成任务需要花费很多时间，期间不会看iPad，如果一直亮屏会掉电很快**。

## 实现原理

1. **点击"开始任务"后**：
   - 立即启用系统自动锁屏功能
   - 启动自定义倒计时（默认5秒，可在设置中调整）

2. **倒计时结束后**：
   - 模拟器：显示可视化息屏覆盖层
   - 真机：降低屏幕亮度至10%
   - 系统会在无操作时自动锁屏（真正节电）

3. **节电效果**：
   - 屏幕亮度降低 → 立即节电
   - 系统自动锁屏 → 最大化节电
   - 适合长时间任务场景

## 测试步骤

### 1. 编译和运行
```bash
# 在Xcode中重新编译项目
# 运行到iPad模拟器或真机
```

### 2. 测试息屏功能
1. 打开应用
2. 进入任务详情页面
3. 点击"开始任务"按钮
4. **立即查看Xcode控制台**，应该看到：
   ```
   🔘 用户点击了开始任务按钮
   🔘 即将调用startDimmingForTask()
   🌟 任务息屏：开始任务后启动息屏倒计时
   🌟 任务息屏：当前设置 - 延迟时间: 5.0秒
   🌟 启用系统自动锁屏功能，为任务期间节电做准备
   🌟 开始息屏倒计时: 5.0秒，当前时间: [时间戳]
   ```

5. **等待5秒**，应该看到：
   ```
   🌙 息屏倒计时到达，开始息屏，当前时间: [时间戳]
   🌙 开始系统级息屏，当前时间: [时间戳]
   🌙 已启用系统自动锁屏功能
   ```

6. **验证息屏效果**：
   - **模拟器**：屏幕上出现半透明黑色覆盖层，显示月亮图标
   - **真机**：屏幕亮度明显降低

### 3. 测试恢复功能
- 点击屏幕任意位置
- 应该恢复正常显示
- 控制台显示恢复日志

## 故障排除

### 如果5秒后没有息屏效果：

1. **检查设置**：
   - 进入应用设置页面
   - 确认"启用息屏功能"已开启
   - 检查"息屏延迟时间"设置

2. **检查控制台日志**：
   - 如果没有看到任何日志 → 按钮点击可能没有触发
   - 如果看到"功能已禁用" → 需要在设置中启用
   - 如果看到倒计时开始但没有结束 → 定时器可能被取消

3. **重新测试**：
   - 重启应用
   - 清理Xcode缓存：Product → Clean Build Folder
   - 重新编译运行

## 预期效果

### ✅ 成功的表现：
- 控制台显示完整的息屏流程日志
- 模拟器显示可视化息屏覆盖层
- 真机屏幕亮度明显降低
- 系统在无操作时会自动锁屏
- 点击可以恢复正常状态

### ❌ 需要检查的情况：
- 控制台没有任何日志输出
- 有开始日志但没有结束日志
- 息屏功能被禁用的提示

## 节电效果

这个功能将显著延长iPad的电池使用时间：
- **立即效果**：屏幕亮度降低至10%
- **长期效果**：系统自动锁屏，屏幕完全关闭
- **适用场景**：小孩长时间完成作业，不需要看屏幕

## 自定义设置

可以在设置页面调整：
- 息屏延迟时间：3-30秒
- 启用/禁用息屏功能
