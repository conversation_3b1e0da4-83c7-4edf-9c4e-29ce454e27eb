# CRS 前端部署说明

## 📦 构建和部署

### 🏠 本地开发环境
```bash
# 开发模式启动
npm run dev

# 本地构建（API地址: http://127.0.0.1:18080）
npm run build:local
# 或使用批处理文件
build-for-local.bat
```

### 🌐 生产环境部署
```bash
# 生产构建（API地址: http://*************:18080）
npm run build:prod
# 或使用批处理文件
build-for-deployment.bat
```

## 🔧 环境配置

### 开发环境 (.env.development)
```
VITE_API_BASE_URL=http://127.0.0.1:18080/api
```

### 生产环境 (.env.production)
```
VITE_API_BASE_URL=http://*************:18080/api
```

## 🚀 部署步骤

### 方式一：使用压缩包部署
1. 运行 `build-for-deployment.bat` 生成 `crs-frontend-deploy.zip`
2. 将压缩包上传到服务器
3. 解压到 nginx 网站根目录
4. 确保后端服务运行在 `*************:18080`

### 方式二：直接复制文件
1. 运行 `npm run build:prod`
2. 将 `dist/` 目录下的所有文件复制到服务器
3. 配置 nginx 指向这些文件

## 📋 Nginx 配置示例

### 简单配置（仅静态文件）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/crs-frontend;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 完整配置（包含API代理）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/crs-frontend;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理（可选，如果需要统一域名）
    location /api/ {
        proxy_pass http://*************:18080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🔍 验证部署

1. **检查前端访问**: 打开浏览器访问前端地址
2. **检查API连接**: 打开浏览器开发者工具，查看网络请求
3. **检查后端服务**: 确保 `http://*************:18080/api/` 可访问

## 📁 文件结构

```
frontend/
├── dist/                    # 构建输出目录
├── src/                     # 源代码
├── .env.development         # 开发环境配置
├── .env.production          # 生产环境配置
├── build-for-local.bat      # 本地构建脚本
├── build-for-deployment.bat # 部署构建脚本
├── crs-frontend-deploy.zip  # 部署压缩包
└── DEPLOYMENT.md           # 本文档
```

## 🛠️ 故障排除

### 问题：API请求失败
- 检查后端服务是否运行在正确端口
- 检查防火墙设置
- 检查网络连接

### 问题：页面空白
- 检查 nginx 配置
- 检查文件路径
- 查看浏览器控制台错误

### 问题：路由不工作
- 确保 nginx 配置了 `try_files $uri $uri/ /index.html;`
- 检查是否为单页应用配置