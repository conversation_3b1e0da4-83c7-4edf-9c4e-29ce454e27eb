import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { childApi } from '../../api/apiService';

// 子页面组件
import TaskBoard from './TaskBoard';
import TaskDetail from './TaskDetail';
import RewardCenter from './RewardCenter';
import SpinWheel from './SpinWheel';
import PointHistory from './PointHistory';
import ExchangeStore from './ExchangeStore';

// 组件
import StatusBar from '../../components/child/StatusBar';
import NavigationMenu from '../../components/child/NavigationMenu';
import ChildBackground from '../../components/child/ChildBackground';

// 主题和状态
import { childTheme } from '../../utils/themes';

// 页面切换动画
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20,
      duration: 0.5
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.3
    }
  }
};

const PointsChangeValue = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== 'isPositive',
})`
  font-size: 1.1rem;
  font-weight: 700;
  color: ${props => props.isPositive ? childTheme.statusColors.COMPLETED : childTheme.statusColors.NOT_STARTED};
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
`;

const ChildHome = () => {
  const [currentPoints, setCurrentPoints] = useState(0);
  const [dailyPointsChange, setDailyPointsChange] = useState(0);
  const [activeTab, setActiveTab] = useState('today'); // 'today' 或 'past'
  const [pastTasksCount, setPastTasksCount] = useState(0);
  const [shouldRefresh, setShouldRefresh] = useState(false); // 用于触发刷新
  const navigate = useNavigate();
  const location = useLocation();
  
  useEffect(() => {
    // 当路径变化时，设置刷新标志
    setShouldRefresh(true);
  }, [location.pathname]);

  useEffect(() => {
    // 从API获取积分信息和过期任务数量
    const fetchInitialData = async () => {
      try {
        console.log('ChildHome: 开始获取初始数据...');
        
        // 获取总积分
        const pointsResponse = await childApi.getPointBalance();
        const points = pointsResponse.data.totalPoints;
        console.log('ChildHome: 获取到总积分:', points);
        setCurrentPoints(points);
        
        // 获取今日积分变化（使用新的专门API）
        const todayPointsResponse = await childApi.getTodayPointChanges(); 
        console.log('ChildHome: 今日积分变化数据:', todayPointsResponse.data);
        
        const todayPoints = todayPointsResponse.data.reduce((total, record) => {
          console.log(`积分记录: ${record.description}, 变化: ${record.pointChange}`);
          return total + record.pointChange;
        }, 0);
        console.log('ChildHome: 计算得到今日积分变化:', todayPoints);
        setDailyPointsChange(todayPoints);
        
        // 获取过期任务数量 - 从today接口筛选OVERDUE状态的任务
        const overdueTasksResponse = await childApi.getOverdueTasks();
        const overdueTasks = overdueTasksResponse.data.filter(task => 
          task.status === 'OVERDUE'
        );
        console.log('ChildHome: 过期任务数量:', overdueTasks.length);
        setPastTasksCount(overdueTasks.length);
      } catch (error) {
        console.error('ChildHome: 获取初始数据失败:', error);
        // 如果获取积分失败，再尝试单独获取积分
        try {
          const pointsResponse = await childApi.getPointBalance();
          const points = pointsResponse.data.totalPoints;
          console.log('ChildHome: 重试获取积分成功:', points);
          setCurrentPoints(points);
        } catch (retryError) {
          console.error('ChildHome: 重试获取积分也失败:', retryError);
          // 使用默认值
          setCurrentPoints(0);
          setDailyPointsChange(0);
          setPastTasksCount(0);
        }
      }
    };
    
    fetchInitialData();
  }, []);

  // 积分变化效果
  const handlePointsChange = (points) => {
    setCurrentPoints(prev => prev + points);
    setDailyPointsChange(prev => prev + points);
  };

  // 不再需要在任何页面显示积分状态栏
  const shouldShowStatusBar = false;

  // 检查是否在抽奖页面
  const isSpinWheelPage = location.pathname.includes('/spin/');

  return (
    <ChildContainer>
      <ChildBackground />
      <PageGlow />
      
      <ContentArea>
        {shouldShowStatusBar && (
          <Header>
            <StatusBar 
              currentPoints={currentPoints} 
              dailyPointsChange={dailyPointsChange}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              pastTasksCount={pastTasksCount}
            />
          </Header>
        )}
        
        <MainContent 
          withStatusBar={shouldShowStatusBar}
          variants={pageVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          key={location.pathname}
          isSpinWheelPage={isSpinWheelPage}
        >
          <ContentWrapper>
            <Routes>
              <Route
                path="/"
                element={
                  <TaskBoard
                    onPointsChange={handlePointsChange}
                    activeTab={activeTab}
                    setActiveTab={setActiveTab}
                    dailyPointsChange={dailyPointsChange}
                    currentPoints={currentPoints}
                    shouldRefresh={shouldRefresh}
                    onRefreshed={() => setShouldRefresh(false)}
                  />
                }
              />
              <Route
                path="/task/:taskId"
                element={<TaskDetail />}
              />
              <Route
                path="/rewards"
                element={<RewardCenter currentPoints={currentPoints} onPointsChange={handlePointsChange} dailyPointsChange={dailyPointsChange} />}
              />
              <Route
                path="/spin/:poolId"
                element={<SpinWheel onPointsChange={handlePointsChange} />}
              />
              <Route
                path="/points"
                element={<PointHistory currentPoints={currentPoints} onPointsChange={handlePointsChange} dailyPointsChange={dailyPointsChange} />}
              />
              <Route
                path="/exchange"
                element={<ExchangeStore onPointsChange={handlePointsChange} />}
              />
            </Routes>
          </ContentWrapper>
        </MainContent>
        
        {!isSpinWheelPage && (
          <Footer>
            <NavigationMenu />
          </Footer>
        )}
      </ContentArea>
    </ChildContainer>
  );
};

const ChildContainer = styled.div`
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: ${childTheme.backgroundColor};
  color: ${childTheme.textColor};
  font-family: 'Baloo 2', 'Noto Sans SC', sans-serif;
`;

const PageGlow = styled.div`
  position: absolute;
  top: -20%;
  right: -10%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(0, 122, 255, 0.15) 0%, rgba(0, 122, 255, 0) 70%);
  border-radius: 50%;
  filter: blur(50px);
  animation: float 20s ease-in-out infinite alternate;
  
  @keyframes float {
    0% {
      transform: translateY(0) translateX(0) scale(1);
    }
    50% {
      transform: translateY(-5%) translateX(5%) scale(1.1);
    }
    100% {
      transform: translateY(5%) translateX(-5%) scale(0.9);
    }
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -150%;
    left: -50%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(255, 204, 0, 0.15) 0%, rgba(255, 204, 0, 0) 70%);
    border-radius: 50%;
    filter: blur(50px);
    animation: float2 15s ease-in-out infinite alternate;
    
    @keyframes float2 {
      0% {
        transform: translateY(0) translateX(0) scale(1);
      }
      50% {
        transform: translateY(8%) translateX(-3%) scale(1.1);
      }
      100% {
        transform: translateY(-8%) translateX(3%) scale(0.9);
      }
    }
  }
`;

const ContentArea = styled.div`
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  flex: 0 0 auto;
  padding: 1rem;
`;

const MainContent = styled(motion.main)`
  flex: 1;
  overflow: hidden;
  position: relative;
  padding: 0 ${props => props.isSpinWheelPage ? '0' : '1rem'};
  padding-top: ${props => props.withStatusBar ? '0' : '1rem'};
  padding-bottom: ${props => props.isSpinWheelPage ? '0' : '1rem'};
  margin-left: ${props => props.isSpinWheelPage ? '-1rem' : '0'};
  margin-right: ${props => props.isSpinWheelPage ? '-1rem' : '0'};
  width: ${props => props.isSpinWheelPage ? 'calc(100% + 2rem)' : '100%'};
`;

const ContentWrapper = styled.div`
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
`;

const Footer = styled.footer`
  flex: 0 0 auto;
  padding: 0;
  margin: 0;
  width: 100%;
`;

export default ChildHome; 