package com.example.childreward.config;

import com.example.childreward.entity.SystemConfig;
import com.example.childreward.repository.SystemConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统配置初始化器
 * 在应用启动时检查并初始化必要的系统配置
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SystemConfigInitializer implements CommandLineRunner {

    private final SystemConfigRepository systemConfigRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("开始初始化系统配置...");
        
        // 定义默认配置
        Map<String, ConfigItem> defaultConfigs = new HashMap<>();
        defaultConfigs.put("PARENT_PIN", new ConfigItem("0907", "家长PIN码"));
        defaultConfigs.put("CHILD_PIN", new ConfigItem("0000", "儿童PIN码"));
        defaultConfigs.put("TASK_EXPIRED_PENALTY", new ConfigItem("10", "任务过期惩罚分值"));
        defaultConfigs.put("REQUIRED_TASK_MULTIPLIER", new ConfigItem("1.5", "必做任务乘数"));
        defaultConfigs.put("BONUS_REWARD_PROBABILITY", new ConfigItem("0.1", "奖励概率加成"));
        defaultConfigs.put("DAILY_MAX_POINTS", new ConfigItem("100", "每日最大积分"));
        defaultConfigs.put("OVERDRAFT_ALLOWED", new ConfigItem("true", "允许透支积分"));
        defaultConfigs.put("OVERDRAFT_LIMIT", new ConfigItem("50", "积分透支限额"));
        defaultConfigs.put("NOTIFICATION_TASK_REMINDER", new ConfigItem("true", "任务提醒通知"));
        defaultConfigs.put("NOTIFICATION_APPROVAL_NEEDED", new ConfigItem("true", "审批提醒通知"));
        defaultConfigs.put("NOTIFICATION_POINTS_EARNED", new ConfigItem("true", "积分获得通知"));
        defaultConfigs.put("NOTIFICATION_OVERDRAFT_ALERT", new ConfigItem("true", "透支提醒通知"));
        
        // 检查并创建默认配置
        int createdCount = 0;
        for (Map.Entry<String, ConfigItem> entry : defaultConfigs.entrySet()) {
            String key = entry.getKey();
            ConfigItem item = entry.getValue();
            
            if (!systemConfigRepository.existsByConfigKey(key)) {
                SystemConfig config = SystemConfig.builder()
                        .configKey(key)
                        .configValue(item.value)
                        .description(item.description)
                        .build();
                
                systemConfigRepository.save(config);
                createdCount++;
                log.info("已创建配置项: {} = {}", key, item.value);
            }
        }
        
        log.info("系统配置初始化完成，共创建 {} 个配置项", createdCount);
    }
    
    /**
     * 配置项数据结构
     */
    private static class ConfigItem {
        String value;
        String description;
        
        ConfigItem(String value, String description) {
            this.value = value;
            this.description = description;
        }
    }
} 