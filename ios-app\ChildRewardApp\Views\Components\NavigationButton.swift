import SwiftUI

// MARK: - 导航按钮组件
struct NavigationButton: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .frame(width: 24)

                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                Spacer()
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                isSelected ?
                LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing) :
                LinearGradient(colors: [.clear], startPoint: .leading, endPoint: .trailing)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
struct NavigationButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            NavigationButton(
                icon: "list.bullet",
                title: "任务",
                isSelected: true,
                action: {}
            )
            
            NavigationButton(
                icon: "gift",
                title: "奖励",
                isSelected: false,
                action: {}
            )
            
            NavigationButton(
                icon: "cart",
                title: "兑换",
                isSelected: false,
                action: {}
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}

// MARK: - 导航按钮样式变体
extension NavigationButton {
    /// 紧凑样式的导航按钮
    func compactStyle() -> some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.body)
                    .frame(width: 20)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .secondary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                isSelected ?
                LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing) :
                Color.clear
            )
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 图标样式的导航按钮
    func iconOnlyStyle() -> some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(isSelected ? .white : .primary)
                .frame(width: 44, height: 44)
                .background(
                    isSelected ?
                    LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing) :
                    Color.clear
                )
                .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 导航按钮配置
struct NavigationButtonConfig {
    let icon: String
    let title: String
    let badge: Int?
    let isEnabled: Bool
    
    init(icon: String, title: String, badge: Int? = nil, isEnabled: Bool = true) {
        self.icon = icon
        self.title = title
        self.badge = badge
        self.isEnabled = isEnabled
    }
}

// MARK: - 带徽章的导航按钮
struct BadgedNavigationButton: View {
    let config: NavigationButtonConfig
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                ZStack {
                    Image(systemName: config.icon)
                        .font(.title3)
                        .frame(width: 24)
                    
                    if let badge = config.badge, badge > 0 {
                        Text("\(badge)")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.red)
                            .cornerRadius(8)
                            .offset(x: 12, y: -8)
                    }
                }

                Text(config.title)
                    .font(.body)
                    .fontWeight(.medium)

                Spacer()
            }
            .foregroundColor(isSelected ? .white : (config.isEnabled ? .primary : .secondary))
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                isSelected ?
                LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing) :
                LinearGradient(colors: [.clear], startPoint: .leading, endPoint: .trailing)
            )
            .cornerRadius(12)
            .opacity(config.isEnabled ? 1.0 : 0.6)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!config.isEnabled)
    }
}

// MARK: - 导航按钮组
struct NavigationButtonGroup: View {
    let buttons: [NavigationButtonConfig]
    let selectedIndex: Int
    let onSelectionChanged: (Int) -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(Array(buttons.enumerated()), id: \.offset) { index, config in
                BadgedNavigationButton(
                    config: config,
                    isSelected: index == selectedIndex,
                    action: {
                        onSelectionChanged(index)
                    }
                )
            }
        }
    }
}
