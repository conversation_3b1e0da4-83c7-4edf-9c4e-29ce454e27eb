#!/bin/bash

echo "🔨 测试项目编译"
echo "================"

# 检查Xcode是否可用
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ xcodebuild 不可用，请确保安装了完整的Xcode"
    echo "💡 如果只安装了Command Line Tools，请安装完整的Xcode应用"
    exit 1
fi

# 检查项目文件
if [ ! -f "ChildRewardApp.xcodeproj/project.pbxproj" ]; then
    echo "❌ 找不到Xcode项目文件"
    exit 1
fi

echo "📁 项目文件: ChildRewardApp.xcodeproj"
echo "🎯 目标设备: iPad Pro (12.9-inch) (6th generation)"
echo ""

# 清理项目
echo "🧹 清理项目缓存..."
xcodebuild clean -project ChildRewardApp.xcodeproj -scheme ChildRewardApp

echo ""
echo "🔨 开始编译..."

# 编译项目
xcodebuild build \
    -project ChildRewardApp.xcodeproj \
    -scheme ChildRewardApp \
    -destination 'platform=iOS Simulator,name=iPad Pro (12.9-inch) (6th generation)' \
    -quiet

BUILD_RESULT=$?

echo ""
if [ $BUILD_RESULT -eq 0 ]; then
    echo "✅ 编译成功！"
    echo "📱 可以在Xcode中运行项目了"
    echo ""
    echo "💡 下一步："
    echo "   1. 在Xcode中打开项目"
    echo "   2. 选择iPad模拟器"
    echo "   3. 点击运行按钮"
    echo "   4. 查看左侧导航栏的头像效果"
else
    echo "❌ 编译失败"
    echo "请检查Xcode中的错误信息"
fi

exit $BUILD_RESULT
