import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';

// 紧凑型进度条容器
const CompactContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
`;

// 主进度条
const ProgressTrack = styled.div`
  position: relative;
  height: 6px;
  background: ${AppleDesignSystem.colors.semantic.quaternarySystemFill};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled(motion.div)`
  height: 100%;
  border-radius: 4px;
  background: ${props => props.gradient};
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.4), transparent);
    border-radius: 4px 4px 0 0;
  }
`;

// 分段指示器
const SegmentIndicator = styled.div`
  display: flex;
  justify-content: center;
  gap: 3px;
  align-items: center;
`;

const SegmentDot = styled(motion.div)`
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: ${props => props.completed ? props.color : AppleDesignSystem.colors.semantic.quaternarySystemFill};
`;

const CompactProgressBar = ({ totalTasks, completedTasks, showSegments = true }) => {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  
  const progress = totalTasks > 0 ? completedTasks / totalTasks : 0;
  
  // 计算进度颜色
  const getProgressColor = () => {
    if (progress >= 1.0) {
      return AppleDesignSystem.colors.system.green;
    } else if (progress >= 0.5) {
      return AppleDesignSystem.colors.system.blue;
    } else {
      return AppleDesignSystem.colors.system.orange;
    }
  };
  
  // 计算渐变
  const getProgressGradient = () => {
    if (progress >= 1.0) {
      return `linear-gradient(90deg, ${AppleDesignSystem.colors.system.green}, ${AppleDesignSystem.colors.system.green}CC)`;
    } else if (progress >= 0.5) {
      return `linear-gradient(90deg, ${AppleDesignSystem.colors.system.blue}, ${AppleDesignSystem.colors.system.teal})`;
    } else {
      return `linear-gradient(90deg, ${AppleDesignSystem.colors.system.orange}, ${AppleDesignSystem.colors.system.yellow})`;
    }
  };
  
  // 动画效果
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progress);
    }, 100);
    
    return () => clearTimeout(timer);
  }, [progress]);
  
  return (
    <CompactContainer>
      {/* 主进度条 */}
      <ProgressTrack>
        <ProgressFill
          gradient={getProgressGradient()}
          initial={{ width: 0 }}
          animate={{ width: `${animatedProgress * 100}%` }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        />
      </ProgressTrack>
      
      {/* 分段指示器 */}
      {showSegments && totalTasks > 0 && totalTasks <= 10 && (
        <SegmentIndicator>
          {Array.from({ length: totalTasks }, (_, index) => (
            <SegmentDot
              key={index}
              completed={index < completedTasks}
              color={getProgressColor()}
              initial={{ scale: 0 }}
              animate={{ 
                scale: index < completedTasks ? 1.1 : 1.0 
              }}
              transition={{ 
                duration: 0.2,
                delay: index * 0.05,
                type: 'spring',
                stiffness: 400,
                damping: 25
              }}
            />
          ))}
        </SegmentIndicator>
      )}
    </CompactContainer>
  );
};

export default CompactProgressBar;
