package com.example.childreward.service;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.entity.TaskType;
import com.example.childreward.repository.PointRecordRepository;
import com.example.childreward.repository.TaskRepository;
import com.example.childreward.service.impl.TaskServiceImpl;

import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskServiceTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private PointRecordRepository pointRecordRepository;

    @Mock
    private PointService pointService;

    @InjectMocks
    private TaskServiceImpl taskService;

    private Task testTask;

    @BeforeEach
    void setUp() {
        testTask = Task.builder()
                .id(1L)
                .title("测试任务")
                .description("这是一个测试任务")
                .basePoints(10)
                .expectedMinutes(30)
                .dueDate(LocalDate.now())
                .dueTime(LocalTime.of(18, 0))
                .status(TaskStatus.NOT_STARTED)
                .taskType(TaskType.REWARD)
                .build();
    }

    @Test
    void createTask_shouldSetDefaultStatusAndSave() {
        // Given
        Task inputTask = Task.builder()
                .title("新任务")
                .basePoints(5)
                .build();
        
        when(taskRepository.save(any(Task.class))).thenReturn(inputTask);
        
        // When
        Task result = taskService.createTask(inputTask);
        
        // Then
        assertEquals(TaskStatus.NOT_STARTED, result.getStatus());
        verify(taskRepository).save(inputTask);
    }

    @Test
    void updateTask_shouldUpdateFieldsAndSave() {
        // Given
        Task updatedTask = Task.builder()
                .title("更新后的标题")
                .description("更新后的描述")
                .basePoints(20)
                .expectedMinutes(45)
                .dueDate(LocalDate.now().plusDays(1))
                .dueTime(LocalTime.of(20, 0))
                .taskType(TaskType.REWARD)
                .build();
        
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.updateTask(1L, updatedTask);
        
        // Then
        assertEquals("更新后的标题", result.getTitle());
        assertEquals("更新后的描述", result.getDescription());
        assertEquals(20, result.getBasePoints());
        assertEquals(45, result.getExpectedMinutes());
        assertEquals(LocalDate.now().plusDays(1), result.getDueDate());
        assertEquals(LocalTime.of(20, 0), result.getDueTime());
        assertEquals(TaskType.REWARD, result.getTaskType());
        verify(taskRepository).save(testTask);
    }

    @Test
    void updateTask_shouldUpdateScheduledDateAndSave() {
        // Given
        LocalDate futureDate = LocalDate.now().plusDays(5);
        Task updatedTask = Task.builder()
                .title("计划任务")
                .description("这是一个计划任务")
                .basePoints(15)
                .expectedMinutes(30)
                .dueDate(futureDate)
                .scheduledDate(futureDate)
                .dueTime(LocalTime.of(18, 0))
                .taskType(TaskType.REWARD)
                .build();
        
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.updateTask(1L, updatedTask);
        
        // Then
        assertEquals(futureDate, result.getScheduledDate());
        assertEquals(futureDate, result.getDueDate());
        verify(taskRepository).save(testTask);
    }

    @Test
    void createTask_shouldSetScheduledDateWhenProvided() {
        // Given
        LocalDate futureDate = LocalDate.now().plusDays(7);
        Task inputTask = Task.builder()
                .title("未来任务")
                .basePoints(5)
                .dueDate(futureDate)
                .scheduledDate(futureDate)
                .build();
        
        when(taskRepository.save(any(Task.class))).thenReturn(inputTask);
        
        // When
        Task result = taskService.createTask(inputTask);
        
        // Then
        assertEquals(futureDate, result.getScheduledDate());
        verify(taskRepository).save(inputTask);
    }

    @Test
    void deleteTask_shouldDeleteExistingTask() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When
        taskService.deleteTask(1L);
        
        // Then
        verify(taskRepository).delete(testTask);
    }

    @Test
    void getTaskById_shouldReturnTaskWhenExists() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When
        Task result = taskService.getTaskById(1L);
        
        // Then
        assertEquals(testTask, result);
    }

    @Test
    void getTaskById_shouldThrowExceptionWhenNotExists() {
        // Given
        when(taskRepository.findById(99L)).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(EntityNotFoundException.class, () -> taskService.getTaskById(99L));
    }

    @Test
    void getTasksByDate_shouldReturnTasksForDate() {
        // Given
        LocalDate today = LocalDate.now();
        List<Task> expectedTasks = Arrays.asList(testTask);
        when(taskRepository.findTasksByDateOrderByStatusPriority(today)).thenReturn(expectedTasks);
        
        // When
        List<Task> result = taskService.getTasksByDate(today);
        
        // Then
        assertEquals(expectedTasks, result);
    }

    @Test
    void startTask_shouldUpdateStatusAndStartTime() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.startTask(1L);
        
        // Then
        assertEquals(TaskStatus.IN_PROGRESS, result.getStatus());
        assertNotNull(result.getStartTime());
        verify(taskRepository).save(testTask);
    }

    @Test
    void startTask_shouldThrowExceptionWhenTaskNotInStartableState() {
        // Given
        testTask.setStatus(TaskStatus.IN_PROGRESS);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When & Then
        assertThrows(IllegalStateException.class, () -> taskService.startTask(1L));
    }

    @Test
    void completeTask_shouldUpdateStatusAndEndTime() {
        // Given
        testTask.setStatus(TaskStatus.IN_PROGRESS);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.completeTask(1L);
        
        // Then
        assertEquals(TaskStatus.PENDING, result.getStatus());
        assertNotNull(result.getEndTime());
        verify(taskRepository).save(testTask);
    }

    @Test
    void completeTask_shouldThrowExceptionWhenTaskNotInProgress() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When & Then
        assertThrows(IllegalStateException.class, () -> taskService.completeTask(1L));
    }

    @Test
    void approveTask_shouldUpdateStatusAndActualPoints() {
        // Given
        testTask.setStatus(TaskStatus.PENDING);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.approveTask(1L, 15);
        
        // Then
        assertEquals(TaskStatus.COMPLETED, result.getStatus());
        assertEquals(15, result.getActualPoints());
        verify(taskRepository).save(testTask);
    }

    @Test
    void approveTask_shouldAddPointsForRewardTask() {
        // Given
        testTask.setStatus(TaskStatus.PENDING);
        testTask.setTaskType(TaskType.REWARD);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        when(pointService.addPoints(anyInt(), any(), anyString(), anyLong())).thenReturn(new PointRecord());
        
        // When
        Task result = taskService.approveTask(1L, 15);
        
        // Then
        assertEquals(TaskStatus.COMPLETED, result.getStatus());
        assertEquals(15, result.getActualPoints());
        verify(pointService).addPoints(
            eq(15), 
            eq(PointRecord.ChangeType.TASK_COMPLETION), 
            anyString(), 
            eq(1L)
        );
    }

    @Test
    void approveTask_shouldNotAddPointsForNonRewardTask() {
        // Given
        testTask.setStatus(TaskStatus.PENDING);
        testTask.setTaskType(TaskType.PENALTY_APPROVAL);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.approveTask(1L, 15);
        
        // Then
        assertEquals(TaskStatus.COMPLETED, result.getStatus());
        assertEquals(15, result.getActualPoints());
        verify(pointService, never()).addPoints(anyInt(), any(), anyString(), anyLong());
    }

    @Test
    void approveTask_shouldThrowExceptionWhenTaskNotPending() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When & Then
        assertThrows(IllegalStateException.class, () -> taskService.approveTask(1L, 15));
    }

    @Test
    void rejectTask_shouldUpdateStatusRegardlessOfCurrentStatus() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.rejectTask(1L, "拒绝原因");
        
        // Then
        assertEquals(TaskStatus.REJECTED, result.getStatus());
        verify(taskRepository).save(testTask);
    }

    @Test
    void getPendingTasks_shouldReturnTasksWithPendingStatus() {
        // Given
        List<Task> pendingTasks = Arrays.asList(testTask);
        when(taskRepository.findByStatus(TaskStatus.PENDING)).thenReturn(pendingTasks);
        
        // When
        List<Task> result = taskService.getPendingTasks();
        
        // Then
        assertEquals(pendingTasks, result);
    }

    @Test
    void findOverdueTasks_shouldReturnOverdueTasks() {
        // Given
        List<Task> overdueTasks = Arrays.asList(testTask);
        when(taskRepository.findByStatusAndDueDateBefore(TaskStatus.NOT_STARTED, LocalDate.now())).thenReturn(overdueTasks);
        
        // When
        List<Task> result = taskService.findOverdueTasks();
        
        // Then
        assertEquals(overdueTasks, result);
    }

    @Test
    void processOverdueTasks_shouldMarkTasksAsOverdue() {
        // Given
        List<Task> overdueTasks = Arrays.asList(testTask);
        when(taskRepository.findOverdueTasks(any(LocalDate.class))).thenReturn(overdueTasks);
        
        // When
        taskService.processOverdueTasks();
        
        // Then
        ArgumentCaptor<Task> taskCaptor = ArgumentCaptor.forClass(Task.class);
        verify(taskRepository).save(taskCaptor.capture());
        assertEquals(TaskStatus.OVERDUE, taskCaptor.getValue().getStatus());
    }

    @Test
    void approvePenalty_shouldUpdateStatusAndSetNegativePoints() {
        // Given
        testTask.setStatus(TaskStatus.PENDING);
        testTask.setBasePoints(10);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.approvePenalty(1L);
        
        // Then
        assertEquals(TaskStatus.COMPLETED, result.getStatus());
        assertEquals(-10, result.getActualPoints());
        assertNotNull(result.getEndTime());
        verify(taskRepository).save(testTask);
    }

    @Test
    void approvePenalty_shouldDeductPointsForPenaltyTask() {
        // Given
        testTask.setStatus(TaskStatus.PENDING);
        testTask.setTaskType(TaskType.PENALTY_APPROVAL);
        testTask.setBasePoints(10);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        when(pointService.deductPoints(anyInt(), any(), anyString(), anyLong())).thenReturn(new PointRecord());
        
        // When
        Task result = taskService.approvePenalty(1L);
        
        // Then
        assertEquals(TaskStatus.COMPLETED, result.getStatus());
        assertEquals(-10, result.getActualPoints());
        verify(pointService).deductPoints(
            eq(10), 
            eq(PointRecord.ChangeType.TASK_PENALTY), 
            anyString(), 
            eq(1L)
        );
    }

    @Test
    void approvePenalty_shouldThrowExceptionWhenTaskNotPending() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When & Then
        assertThrows(IllegalStateException.class, () -> taskService.approvePenalty(1L));
    }

    @Test
    void rejectPenalty_shouldUpdateStatus() {
        // Given
        testTask.setStatus(TaskStatus.PENDING);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(Task.class))).thenAnswer(i -> i.getArguments()[0]);
        
        // When
        Task result = taskService.rejectPenalty(1L);
        
        // Then
        assertEquals(TaskStatus.REJECTED, result.getStatus());
        verify(taskRepository).save(testTask);
    }

    @Test
    void rejectPenalty_shouldThrowExceptionWhenTaskNotPending() {
        // Given
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        
        // When & Then
        assertThrows(IllegalStateException.class, () -> taskService.rejectPenalty(1L));
    }
} 