/**
 * 苹果标准设计系统
 * 基于 iOS Human Interface Guidelines 和 macOS Human Interface Guidelines
 * 完全遵循苹果的设计原则和规范
 */

export const AppleDesignSystem = {
  // ==================== 颜色系统 ====================
  colors: {
    // 系统颜色 - iOS 17 标准
    system: {
      blue: '#007AFF',
      green: '#34C759', 
      indigo: '#5856D6',
      orange: '#FF9500',
      pink: '#FF2D92',
      purple: '#AF52DE',
      red: '#FF3B30',
      teal: '#5AC8FA',
      yellow: '#FFCC00',
      gray: '#8E8E93',
      gray2: '#AEAEB2',
      gray3: '#C7C7CC',
      gray4: '#D1D1D6',
      gray5: '#E5E5EA',
      gray6: '#F2F2F7'
    },

    // 语义化颜色
    semantic: {
      // 文本颜色
      label: '#000000',
      secondaryLabel: '#3C3C43',
      tertiaryLabel: '#3C3C4399',
      quaternaryLabel: '#3C3C432E',
      
      // 背景颜色
      systemBackground: '#FFFFFF',
      secondarySystemBackground: '#F2F2F7',
      tertiarySystemBackground: '#FFFFFF',
      
      // 分组背景
      systemGroupedBackground: '#F2F2F7',
      secondarySystemGroupedBackground: '#FFFFFF',
      tertiarySystemGroupedBackground: '#F2F2F7',
      
      // 填充颜色
      systemFill: '#78788033',
      secondarySystemFill: '#78788028',
      tertiarySystemFill: '#7676801E',
      quaternarySystemFill: '#74748014',
      
      // 分隔线
      separator: '#3C3C4349',
      opaqueSeparator: '#C6C6C8',
      
      // 链接颜色
      link: '#007AFF',
      
      // 状态颜色
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#007AFF'
    },

    // 儿童端专用颜色
    child: {
      primary: '#007AFF',
      secondary: '#5856D6', 
      accent: '#FF2D92',
      success: '#34C759',
      warning: '#FF9500',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      playful: '#FF2D92',
      friendly: '#5AC8FA'
    },

    // 家长端专用颜色  
    parent: {
      primary: '#007AFF',
      secondary: '#5856D6',
      accent: '#AF52DE',
      success: '#34C759', 
      warning: '#FF9500',
      error: '#FF3B30',
      background: '#FFFFFF',
      surface: '#F2F2F7',
      professional: '#8E8E93',
      management: '#5856D6'
    }
  },

  // ==================== 字体系统 ====================
  typography: {
    // 字体族 - SF Pro 系列
    fontFamily: {
      system: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif',
      display: '"SF Pro Display", -apple-system, BlinkMacSystemFont, system-ui, sans-serif',
      text: '"SF Pro Text", -apple-system, BlinkMacSystemFont, system-ui, sans-serif',
      mono: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, monospace',
      rounded: '"SF Pro Rounded", -apple-system, BlinkMacSystemFont, system-ui, sans-serif'
    },

    // iOS 文本样式
    textStyles: {
      largeTitle: {
        fontSize: '34px',
        lineHeight: '41px',
        fontWeight: 400,
        letterSpacing: '0.37px'
      },
      title1: {
        fontSize: '28px', 
        lineHeight: '34px',
        fontWeight: 400,
        letterSpacing: '0.36px'
      },
      title2: {
        fontSize: '22px',
        lineHeight: '28px', 
        fontWeight: 400,
        letterSpacing: '0.35px'
      },
      title3: {
        fontSize: '20px',
        lineHeight: '25px',
        fontWeight: 400,
        letterSpacing: '0.38px'
      },
      headline: {
        fontSize: '17px',
        lineHeight: '22px',
        fontWeight: 600,
        letterSpacing: '-0.43px'
      },
      body: {
        fontSize: '17px',
        lineHeight: '22px', 
        fontWeight: 400,
        letterSpacing: '-0.43px'
      },
      callout: {
        fontSize: '16px',
        lineHeight: '21px',
        fontWeight: 400,
        letterSpacing: '-0.32px'
      },
      subheadline: {
        fontSize: '15px',
        lineHeight: '20px',
        fontWeight: 400,
        letterSpacing: '-0.24px'
      },
      footnote: {
        fontSize: '13px',
        lineHeight: '18px',
        fontWeight: 400,
        letterSpacing: '-0.08px'
      },
      caption1: {
        fontSize: '12px',
        lineHeight: '16px',
        fontWeight: 400,
        letterSpacing: '0px'
      },
      caption2: {
        fontSize: '11px',
        lineHeight: '13px',
        fontWeight: 400,
        letterSpacing: '0.07px'
      }
    },

    // 字重
    fontWeight: {
      ultraLight: 100,
      thin: 200,
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      heavy: 800,
      black: 900
    }
  },

  // ==================== 间距系统 ====================
  spacing: {
    // 基于 8pt 网格系统
    xs: '4px',    // 0.5 * 8
    sm: '8px',    // 1 * 8  
    md: '16px',   // 2 * 8
    lg: '24px',   // 3 * 8
    xl: '32px',   // 4 * 8
    xxl: '48px',  // 6 * 8
    xxxl: '64px', // 8 * 8

    // 特殊间距
    hairline: '1px',
    thin: '2px',
    thick: '4px',

    // 组件间距
    component: {
      padding: '16px',
      margin: '16px',
      gap: '12px'
    }
  },

  // ==================== 圆角系统 ====================
  borderRadius: {
    none: '0px',
    xs: '4px',
    sm: '6px', 
    md: '8px',
    lg: '12px',
    xl: '16px',
    xxl: '20px',
    xxxl: '24px',
    full: '9999px',

    // iOS 标准圆角
    ios: {
      small: '8px',    // 小组件
      medium: '12px',  // 中等组件
      large: '16px',   // 大组件
      card: '12px',    // 卡片
      button: '8px',   // 按钮
      input: '10px'    // 输入框
    }
  },

  // ==================== 阴影系统 ====================
  shadows: {
    // iOS 标准阴影
    none: 'none',
    xs: '0 1px 2px rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04)',
    
    // 特殊阴影
    card: '0 2px 10px rgba(0, 0, 0, 0.08)',
    modal: '0 25px 50px rgba(0, 0, 0, 0.25)',
    button: '0 1px 3px rgba(0, 0, 0, 0.12)',
    floating: '0 8px 30px rgba(0, 0, 0, 0.12)',
    
    // 内阴影
    inset: 'inset 0 2px 4px rgba(0, 0, 0, 0.06)'
  },

  // ==================== 动画系统 ====================
  animation: {
    // iOS 标准缓动函数 - Framer Motion格式
    easing: {
      // 标准缓动
      standard: [0.4, 0.0, 0.2, 1],
      // 减速
      decelerate: [0.0, 0.0, 0.2, 1],
      // 加速
      accelerate: [0.4, 0.0, 1, 1],
      // 尖锐
      sharp: [0.4, 0.0, 0.6, 1],
      // iOS 弹簧动画 - 使用spring类型
      spring: { type: 'spring', stiffness: 260, damping: 20 }
    },

    // CSS缓动函数（用于styled-components）
    cssEasing: {
      standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
      decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
      accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0.0, 0.6, 1)',
      spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    },

    // 持续时间
    duration: {
      instant: '0ms',
      fast: '150ms',
      normal: '250ms', 
      slow: '350ms',
      slower: '500ms'
    },

    // 预设动画
    presets: {
      fadeIn: 'opacity 250ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      slideUp: 'transform 350ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      scaleIn: 'transform 200ms cubic-bezier(0.175, 0.885, 0.32, 1.275)',
      bounce: 'transform 500ms cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    }
  },

  // ==================== 断点系统 ====================
  breakpoints: {
    // iPhone 尺寸
    iphone: {
      se: '375px',      // iPhone SE
      standard: '390px', // iPhone 14
      plus: '428px',    // iPhone 14 Plus
      pro: '393px',     // iPhone 14 Pro
      proMax: '430px'   // iPhone 14 Pro Max
    },

    // iPad 尺寸
    ipad: {
      mini: '744px',    // iPad mini
      standard: '820px', // iPad 10.9"
      air: '834px',     // iPad Air
      pro11: '834px',   // iPad Pro 11"
      pro129: '1024px'  // iPad Pro 12.9"
    },

    // Mac 尺寸
    mac: {
      small: '1280px',  // MacBook Air 13"
      medium: '1440px', // MacBook Pro 14"
      large: '1728px',  // MacBook Pro 16"
      xl: '1920px'      // 外接显示器
    },

    // 通用断点
    xs: '375px',
    sm: '768px', 
    md: '1024px',
    lg: '1280px',
    xl: '1440px',
    xxl: '1920px'
  },

  // ==================== 组件规格 ====================
  components: {
    // 按钮
    button: {
      height: {
        small: '32px',
        medium: '44px',   // iOS 标准触摸目标
        large: '56px'
      },
      padding: {
        small: '8px 16px',
        medium: '12px 24px', 
        large: '16px 32px'
      }
    },

    // 输入框
    input: {
      height: {
        small: '36px',
        medium: '44px',
        large: '52px'
      }
    },

    // 导航栏
    navigation: {
      height: {
        compact: '44px',
        regular: '56px',
        large: '96px'
      }
    },

    // 标签栏
    tabBar: {
      height: '83px',  // iOS 标准
      iconSize: '24px'
    },

    // 卡片
    card: {
      padding: '16px',
      borderRadius: '12px'
    }
  }
};

// ==================== 媒体查询助手 ====================
export const mediaQueries = {
  // 设备类型
  iphone: `@media (max-width: ${AppleDesignSystem.breakpoints.ipad.mini})`,
  ipad: `@media (min-width: ${AppleDesignSystem.breakpoints.ipad.mini}) and (max-width: ${AppleDesignSystem.breakpoints.mac.small})`,
  mac: `@media (min-width: ${AppleDesignSystem.breakpoints.mac.small})`,

  // 方向
  portrait: '@media (orientation: portrait)',
  landscape: '@media (orientation: landscape)',

  // 交互能力
  touch: '@media (hover: none) and (pointer: coarse)',
  mouse: '@media (hover: hover) and (pointer: fine)',

  // 显示密度
  retina: '@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',

  // 暗色模式
  dark: '@media (prefers-color-scheme: dark)',
  light: '@media (prefers-color-scheme: light)',

  // 减少动画
  reduceMotion: '@media (prefers-reduced-motion: reduce)'
};

export default AppleDesignSystem;
