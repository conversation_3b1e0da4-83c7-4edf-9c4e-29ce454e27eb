//
//  TimeFormatDemoView.swift
//  ChildRewardApp
//
//  Created by AI Assistant on 2024-07-29.
//

import SwiftUI

struct TimeFormatDemoView: View {
    @State private var sampleRecords: [PointRecord] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 16) {
                    Text("积分记录时间格式演示")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding()
                    
                    Text("以下展示了不同时间的积分记录格式化效果：")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                    
                    LazyVStack(spacing: 12) {
                        ForEach(sampleRecords, id: \.id) { record in
                            pointRecordRow(record)
                        }
                    }
                    .padding(.horizontal)
                    
                    Button("刷新示例数据") {
                        generateSampleRecords()
                    }
                    .buttonStyle(.borderedProminent)
                    .padding()
                }
            }
            .navigationTitle("时间格式演示")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            generateSampleRecords()
        }
    }
    
    private func pointRecordRow(_ record: PointRecord) -> some View {
        HStack(spacing: 16) {
            // 记录类型图标
            Image(systemName: record.typeIcon)
                .font(.title2)
                .foregroundColor(record.typeColor)
                .frame(width: 32, height: 32)
                .background(record.typeColor.opacity(0.1))
                .cornerRadius(8)

            // 记录信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.description)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(record.formattedTime)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.1))
                    .cornerRadius(4)
            }

            Spacer()

            // 积分变化
            Text(record.formattedPointChange)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(record.pointChange > 0 ? .green : .red)
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
    
    private func generateSampleRecords() {
        let now = Date()
        let calendar = Calendar.current
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        sampleRecords = [
            // 今天的记录
            PointRecord(
                id: 1,
                pointChange: 10,
                description: "完成任务：语文《每日一练》",
                changeType: "TASK_COMPLETION",
                recordTime: formatter.string(from: now),
                relatedTaskId: 1,
                relatedRewardId: nil
            ),
            
            // 今天早些时候的记录
            PointRecord(
                id: 2,
                pointChange: 5,
                description: "完成任务：数学练习",
                changeType: "TASK_COMPLETION",
                recordTime: formatter.string(from: calendar.date(byAdding: .hour, value: -3, to: now) ?? now),
                relatedTaskId: 2,
                relatedRewardId: nil
            ),
            
            // 昨天的记录
            PointRecord(
                id: 3,
                pointChange: -10,
                description: "兑换奖励：看电视30分钟",
                changeType: "REWARD_EXCHANGE",
                recordTime: formatter.string(from: calendar.date(byAdding: .day, value: -1, to: now) ?? now),
                relatedTaskId: nil,
                relatedRewardId: 1
            ),
            
            // 3天前的记录
            PointRecord(
                id: 4,
                pointChange: 15,
                description: "完成任务：英语《练字帖》",
                changeType: "TASK_COMPLETION",
                recordTime: formatter.string(from: calendar.date(byAdding: .day, value: -3, to: now) ?? now),
                relatedTaskId: 3,
                relatedRewardId: nil
            ),
            
            // 一周前的记录
            PointRecord(
                id: 5,
                pointChange: -5,
                description: "任务未完成惩罚",
                changeType: "TASK_PENALTY",
                recordTime: formatter.string(from: calendar.date(byAdding: .day, value: -7, to: now) ?? now),
                relatedTaskId: 4,
                relatedRewardId: nil
            ),
            
            // 一个月前的记录
            PointRecord(
                id: 6,
                pointChange: 20,
                description: "手动调整积分",
                changeType: "MANUAL_ADJUST",
                recordTime: formatter.string(from: calendar.date(byAdding: .month, value: -1, to: now) ?? now),
                relatedTaskId: nil,
                relatedRewardId: nil
            )
        ]
    }
}

#Preview {
    TimeFormatDemoView()
}
