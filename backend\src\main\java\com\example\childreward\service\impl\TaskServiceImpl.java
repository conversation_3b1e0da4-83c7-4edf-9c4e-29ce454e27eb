package com.example.childreward.service.impl;

import com.example.childreward.entity.PointRecord;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.entity.TaskType;
import com.example.childreward.repository.PointRecordRepository;
import com.example.childreward.repository.TaskRepository;
import com.example.childreward.service.PointService;
import com.example.childreward.service.TaskService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final PointRecordRepository pointRecordRepository;
    private final PointService pointService;

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task createTask(Task task) {
        // 设置默认状态
        if (task.getStatus() == null) {
            task.setStatus(TaskStatus.NOT_STARTED);
        }
        // 确保scheduledDate字段被设置
        if (task.getScheduledDate() == null) {
            task.setScheduledDate(task.getDueDate() != null ? task.getDueDate() : LocalDate.now());
        }
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task updateTask(Long id, Task task) {
        Task existingTask = getTaskById(id);
        
        // 更新基本信息
        existingTask.setTitle(task.getTitle());
        existingTask.setDescription(task.getDescription());
        existingTask.setExpectedMinutes(task.getExpectedMinutes());
        existingTask.setBasePoints(task.getBasePoints());
        existingTask.setDueTime(task.getDueTime());
        existingTask.setDueDate(task.getDueDate());
        existingTask.setTaskType(task.getTaskType());
        
        // 更新计划日期
        if (task.getScheduledDate() != null) {
            existingTask.setScheduledDate(task.getScheduledDate());
        } else if (task.getDueDate() != null) {
            // 如果没有提供计划日期，但提供了截止日期，则使用截止日期作为计划日期
            existingTask.setScheduledDate(task.getDueDate());
        }
        
        return taskRepository.save(existingTask);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public void deleteTask(Long id) {
        Task task = getTaskById(id);
        taskRepository.delete(task);
    }

    @Override
    public Task getTaskById(Long id) {
        return taskRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("任务不存在: " + id));
    }

    @Override
    @Cacheable(value = "tasksByDate", key = "#date.toString()")
    public List<Task> getTasksByDate(LocalDate date) {
        String serviceId = java.util.UUID.randomUUID().toString().substring(0, 8);
        long startTime = System.currentTimeMillis();

        log.info("🔍 [服务{}] TaskService.getTasksByDate() 开始", serviceId);
        log.info("📅 [服务{}] 查询日期: {}", serviceId, date);

        try {
            // 使用高性能查询方法，只查询列表需要的字段，排除description字段
            List<Task> tasks = taskRepository.findTaskSummaryByScheduledDate(date);
            long queryTime = System.currentTimeMillis();
            log.info("📊 [服务{}] 高性能数据库查询耗时: {}ms, 结果: {} 个任务",
                serviceId, queryTime - startTime, tasks.size());

            // 在应用层进行状态排序（更快）
            tasks.sort((t1, t2) -> {
                int priority1 = getStatusPriority(t1.getStatus());
                int priority2 = getStatusPriority(t2.getStatus());
                if (priority1 != priority2) {
                    return Integer.compare(priority1, priority2);
                }
                // 相同状态按截止时间排序
                return t1.getDueTime().compareTo(t2.getDueTime());
            });

            long sortTime = System.currentTimeMillis();
            log.info("🔄 [服务{}] 应用层排序耗时: {}ms", serviceId, sortTime - queryTime);

            // 打印每个任务的详细信息
            for (int i = 0; i < tasks.size(); i++) {
                Task task = tasks.get(i);
                log.info("📋 [服务{}] 任务{}: ID={}, 标题={}, 状态={}, 计划日期={}",
                    serviceId, i+1, task.getId(), task.getTitle(), task.getStatus(), task.getScheduledDate());
            }

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("✅ [服务{}] TaskService.getTasksByDate() 完成，总耗时: {}ms", serviceId, totalTime);
            return tasks;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("❌ [服务{}] TaskService.getTasksByDate() 失败，耗时: {}ms, 错误: {}",
                serviceId, totalTime, e.getMessage(), e);
            throw e;
        }
    }

    // 状态优先级辅助方法
    private int getStatusPriority(TaskStatus status) {
        switch (status) {
            case IN_PROGRESS: return 1;
            case NOT_STARTED: return 2;
            case PENDING: return 3;
            case COMPLETED: return 4;
            case OVERDUE: return 5;
            default: return 6;
        }
    }

    @Override
    public List<Task> getAllTasks() {
        return taskRepository.findAll();
    }

    @Override
    public List<Task> getAllTasks(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return taskRepository.findAll(pageable).getContent();
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task startTask(Long id) {
        Task task = getTaskById(id);
        if (task.getStatus() != TaskStatus.NOT_STARTED) {
            throw new IllegalStateException("Task is not in a startable state.");
        }
        task.setStatus(TaskStatus.IN_PROGRESS);
        task.setStartTime(LocalDateTime.now());
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task completeTask(Long id) {
        Task task = getTaskById(id);
        if (task.getStatus() != TaskStatus.IN_PROGRESS) {
            throw new IllegalStateException("Task cannot be completed.");
        }
        
        // 奖励类型任务完成后进入审批流程，而不是直接标记为完成
        task.setStatus(TaskStatus.PENDING); // 修改为PENDING，等待家长审批
        task.setEndTime(LocalDateTime.now());
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task approveTask(Long id, Integer actualPoints) {
        Task task = getTaskById(id);
        if (task.getStatus() != TaskStatus.PENDING) {
            throw new IllegalStateException("只有处于待审批状态的任务才能被批准。");
        }
        task.setStatus(TaskStatus.COMPLETED);
        task.setActualPoints(actualPoints);

        // 添加积分逻辑 - 所有类型的任务完成后都应该获得积分
        if (actualPoints > 0) {
            pointService.addPoints(
                actualPoints,
                PointRecord.ChangeType.TASK_COMPLETION,
                "完成任务：" + task.getTitle(),
                task.getId()
            );
        }

        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task rejectTask(Long id, String reason) {
        Task task = getTaskById(id);
        task.setStatus(TaskStatus.REJECTED);
        // 可以在这里添加作废原因的逻辑
        return taskRepository.save(task);
    }

    @Override
    @Cacheable(value = "pendingTasks", key = "'all'")
    public List<Task> getPendingTasks() {
        return taskRepository.findByStatus(TaskStatus.PENDING);
    }

    @Override
    public List<Task> getPendingTasksByDateRange(LocalDate startDate, LocalDate endDate) {
        // 如果没有指定日期范围，默认查询当天
        if (startDate == null && endDate == null) {
            LocalDate today = LocalDate.now();
            return taskRepository.findByStatusAndScheduledDateBetween(TaskStatus.PENDING, today, today);
        }

        // 如果只指定了开始日期，结束日期设为开始日期
        if (endDate == null) {
            endDate = startDate;
        }

        // 如果只指定了结束日期，开始日期设为结束日期
        if (startDate == null) {
            startDate = endDate;
        }

        return taskRepository.findByStatusAndScheduledDateBetween(TaskStatus.PENDING, startDate, endDate);
    }

    @Override
    public List<Task> getTasksByDateRangeAndStatus(LocalDate startDate, LocalDate endDate, String status) {
        log.info("🔍 审批中心查询 - 原始参数: startDate={}, endDate={}, status={}", startDate, endDate, status);

        // 如果没有指定日期范围，默认查询当天
        if (startDate == null && endDate == null) {
            LocalDate today = LocalDate.now();
            startDate = today;
            endDate = today;
            log.info("📅 使用默认日期范围: {} 至 {}", startDate, endDate);
        } else if (endDate == null) {
            endDate = startDate;
            log.info("📅 结束日期为空，设置为开始日期: {}", endDate);
        } else if (startDate == null) {
            startDate = endDate;
            log.info("📅 开始日期为空，设置为结束日期: {}", startDate);
        }

        log.info("📅 最终查询日期范围: {} 至 {}", startDate, endDate);

        // 如果没有指定状态，返回所有状态的任务
        if (status == null || status.trim().isEmpty()) {
            log.info("📋 查询所有状态的任务");
            List<Task> tasks = taskRepository.findByScheduledDateBetween(startDate, endDate);
            log.info("📊 查询结果: {} 个任务", tasks.size());
            return tasks;
        }

        // 根据状态查询
        try {
            TaskStatus taskStatus = TaskStatus.valueOf(status.toUpperCase());
            log.info("📋 查询状态为 {} 的任务", taskStatus);
            List<Task> tasks = taskRepository.findByStatusAndScheduledDateBetween(taskStatus, startDate, endDate);
            log.info("📊 查询结果: {} 个任务", tasks.size());
            return tasks;
        } catch (IllegalArgumentException e) {
            log.warn("⚠️ 无效的任务状态: {}", status);
            // 如果状态无效，返回空列表
            return new ArrayList<>();
        }
    }

    @Override
    public List<Task> findOverdueTasks() {
        return taskRepository.findByStatusAndDueDateBefore(TaskStatus.NOT_STARTED, LocalDate.now());
    }

    @Override
    public List<Task> getPastTasks() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate sevenDaysAgo = yesterday.minusDays(6); // 最近7天，不包括今天

        return taskRepository.findPastTasksInDateRange(today, sevenDaysAgo);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public void processOverdueTasks() {
        // 获取过期的未开始或进行中的任务（不包括已完成等待审批的PENDING任务）
        List<Task> overdueTasks = taskRepository.findOverdueTasks(LocalDate.now());
        
        for (Task task : overdueTasks) {
            // 标记为逾期
            task.setStatus(TaskStatus.OVERDUE);
            taskRepository.save(task);
            
            // 注意：TaskType的逻辑已改变，旧的判断逻辑已不再适用，暂时注释。
            // if (task.getTaskType() == Task.TaskType.REQUIRED) {
            //     // 对于必做任务，逾期可能会有惩罚，此处可以添加逻辑
            // }
        }
    }

    @Override
    public List<Task> getTasksByScheduledDateLessThanEqual(LocalDate date) {
        return taskRepository.findTasksByScheduledDateLessThanEqualOrderByStatusPriority(date);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task approvePenalty(Long taskId) {
        Task task = getTaskById(taskId);
        if (task.getStatus() != TaskStatus.PENDING) {
            throw new IllegalStateException("只有处于待审批状态的惩罚才能被批准。");
        }
        // 扣分，所以记录为负值
        task.setActualPoints(-task.getBasePoints());
        task.setStatus(TaskStatus.COMPLETED);
        task.setEndTime(LocalDateTime.now());

        // 执行扣分操作
        if (task.getTaskType() == TaskType.PENALTY_APPROVAL && task.getBasePoints() > 0) {
            pointService.deductPoints(
                task.getBasePoints(),
                PointRecord.ChangeType.TASK_PENALTY,
                "惩罚任务：" + task.getTitle(),
                task.getId()
            );
        }

        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tasksByDate", "pendingTasks"}, allEntries = true)
    public Task rejectPenalty(Long taskId) {
        Task task = getTaskById(taskId);
        if (task.getStatus() != TaskStatus.PENDING) {
            throw new IllegalStateException("只有处于待审批状态的惩罚才能被拒绝。");
        }
        task.setStatus(TaskStatus.REJECTED);
        return taskRepository.save(task);
    }
} 