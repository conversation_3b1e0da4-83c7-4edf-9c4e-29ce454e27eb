import SwiftUI
import Combine

// 状态栏视图
struct StatusBarView: View {
    @StateObject private var apiService = ChildAPIService.shared
    @State private var currentPoints: Int = 0
    @State private var dailyPointsChange: Int = 0
    @State private var isLoading = false
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.lg) {
            // 积分显示
            PointsDisplayView(
                currentPoints: currentPoints,
                dailyChange: dailyPointsChange
            )
            
            Spacer()
            
            // 今日进度
            TodayProgressView()
            
            Spacer()
            
            // 快捷操作
            QuickActionsView()
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .glassBackground()
        .onAppear {
            loadStatusData()
        }
    }
    
    private func loadStatusData() {
        isLoading = true
        
        // 获取积分余额
        apiService.getPointBalance()
            .sink(
                receiveCompletion: { completion in
                    isLoading = false
                    if case .failure(let error) = completion {
                        print("获取积分余额失败: \(error)")
                    }
                },
                receiveValue: { balance in
                    currentPoints = balance.totalPoints
                }
            )
            .store(in: &cancellables)
        
        // 获取今日积分变化
        apiService.getTodayPointChange()
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("获取今日积分变化失败: \(error)")
                    }
                },
                receiveValue: { records in
                    dailyPointsChange = records.reduce(0) { $0 + $1.pointChange }
                }
            )
            .store(in: &cancellables)
    }
}

// 积分显示组件
struct PointsDisplayView: View {
    let currentPoints: Int
    let dailyChange: Int
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            // 积分图标
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [DesignSystem.Colors.warning, DesignSystem.Colors.warningLight],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 44, height: 44)
                
                Image(systemName: "star.fill")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                // 当前积分
                Text("\(currentPoints)")
                    .font(DesignSystem.Typography.title3)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                // 今日变化
                HStack(spacing: 4) {
                    Image(systemName: dailyChange >= 0 ? "arrow.up" : "arrow.down")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(dailyChange >= 0 ? DesignSystem.Colors.success : DesignSystem.Colors.error)
                    
                    Text("今日\(dailyChange >= 0 ? "+" : "")\(dailyChange)")
                        .font(DesignSystem.Typography.caption1)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
        }
    }
}

// 今日进度组件
struct TodayProgressView: View {
    @StateObject private var apiService = ChildAPIService.shared
    @State private var completedTasks = 0
    @State private var totalTasks = 0
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xs) {
            // 进度环
            ZStack {
                Circle()
                    .stroke(DesignSystem.Colors.separator.opacity(0.3), lineWidth: 6)
                    .frame(width: 40, height: 40)
                
                Circle()
                    .trim(from: 0, to: progress)
                    .stroke(
                        LinearGradient(
                            colors: [DesignSystem.Colors.success, DesignSystem.Colors.successLight],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 6, lineCap: .round)
                    )
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(-90))
                    .animation(DesignSystem.Animation.smooth, value: progress)
                
                Text("\(completedTasks)")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            
            // 进度文字
            Text("今日任务")
                .font(DesignSystem.Typography.caption2)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .onAppear {
            loadTodayProgress()
        }
    }
    
    private var progress: Double {
        guard totalTasks > 0 else { return 0 }
        return Double(completedTasks) / Double(totalTasks)
    }
    
    private func loadTodayProgress() {
        apiService.getTodayTasks()
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("获取今日任务失败: \(error)")
                    }
                },
                receiveValue: { tasks in
                    totalTasks = tasks.count
                    completedTasks = tasks.filter { 
                        $0.status == .completed || $0.status == .approved 
                    }.count
                }
            )
            .store(in: &cancellables)
    }
}

// 快捷操作组件
struct QuickActionsView: View {
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 刷新按钮
            Button(action: {
                // 刷新数据
                NotificationCenter.default.post(name: .refreshData, object: nil)
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.primary)
                    .frame(width: 32, height: 32)
                    .background(DesignSystem.Colors.surface)
                    .clipShape(Circle())
                    .shadow(
                        color: DesignSystem.Shadow.small.color,
                        radius: DesignSystem.Shadow.small.radius,
                        x: DesignSystem.Shadow.small.x,
                        y: DesignSystem.Shadow.small.y
                    )
            }
            
            // 设置按钮
            Button(action: {
                // 打开设置
            }) {
                Image(systemName: "gearshape")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(width: 32, height: 32)
                    .background(DesignSystem.Colors.surface)
                    .clipShape(Circle())
                    .shadow(
                        color: DesignSystem.Shadow.small.color,
                        radius: DesignSystem.Shadow.small.radius,
                        x: DesignSystem.Shadow.small.x,
                        y: DesignSystem.Shadow.small.y
                    )
            }
        }
    }
}

// 通知扩展
extension Notification.Name {
    static let refreshData = Notification.Name("refreshData")
}

// 预览
struct StatusBarView_Previews: PreviewProvider {
    static var previews: some View {
        StatusBarView()
            .padding()
            .background(DesignSystem.Colors.background)
            .previewDevice("iPad Pro (12.9-inch) (6th generation)")
    }
}
