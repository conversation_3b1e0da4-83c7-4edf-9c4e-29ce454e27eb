import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { pointApi } from '../../api/apiService';
import { parentTheme } from '../../utils/themes';

const BonusPointCard = ({ onPointsUpdated }) => {
  const [points, setPoints] = useState(10);
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (points <= 0) {
      setError('奖励的积分必须大于0');
      return;
    }
    
    if (!description) {
      setError('请输入加分原因');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // 使用通用的手动调整积分API
      await pointApi.addPoints(points, description);
      setSuccess(true);
      setPoints(10);
      setDescription('');
      
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
      
      if (onPointsUpdated) {
        onPointsUpdated();
      }
    } catch (error) {
      console.error('加分失败:', error);
      setError('加分失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <CardContainer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <CardHeader>
        <CardIcon>🌟</CardIcon>
        <CardTitle>表现优异加分</CardTitle>
      </CardHeader>
      
      <CardContent>
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="bonus-points">奖励积分</Label>
            <Input
              type="number"
              id="bonus-points"
              min="1"
              max="100"
              value={points}
              onChange={(e) => setPoints(parseInt(e.target.value) || 0)}
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="bonus-description">加分原因</Label>
            <Textarea
              id="bonus-description"
              placeholder="例如：主动帮助做家务、考试取得好成绩等"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              required
            />
          </FormGroup>
          
          {error && <ErrorMessage>{error}</ErrorMessage>}
          {success && <SuccessMessage>加分成功！</SuccessMessage>}
          
          <SubmitButton
            type="submit"
            disabled={loading}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {loading ? '处理中...' : '确认加分'}
          </SubmitButton>
        </Form>
      </CardContent>
    </CardContainer>
  );
};

// 样式组件
const CardContainer = styled(motion.div)`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 100%;
`;

const CardHeader = styled.div`
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  padding: 1rem;
  display: flex;
  align-items: center;
  color: white;
`;

const CardIcon = styled.div`
  font-size: 1.5rem;
  margin-right: 0.8rem;
`;

const CardTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const CardContent = styled.div`
  padding: 1.5rem;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.3rem;
`;

const Input = styled.input`
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const Textarea = styled.textarea`
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  resize: vertical;
  min-height: 80px;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const ErrorMessage = styled.div`
  color: #f44336;
  font-size: 0.9rem;
  margin-top: 0.5rem;
`;

const SuccessMessage = styled.div`
  color: #4caf50;
  font-size: 0.9rem;
  margin-top: 0.5rem;
`;

const SubmitButton = styled(motion.button)`
  padding: 1rem;
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

export default BonusPointCard; 