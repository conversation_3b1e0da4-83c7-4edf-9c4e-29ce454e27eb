-- 修复数据库表结构
-- 添加缺失的字段

-- 检查并添加 scheduled_date 字段到 task 表
ALTER TABLE task ADD COLUMN IF NOT EXISTS scheduled_date DATE;

-- 检查并添加 description 字段到 task 表（如果不存在）
ALTER TABLE task ADD COLUMN IF NOT EXISTS description VARCHAR(255);

-- 检查并添加 task_type 字段到 task 表（如果不存在）
ALTER TABLE task ADD COLUMN IF NOT EXISTS task_type VARCHAR(20);

-- 检查并添加 created_time 字段到 task 表（如果不存在）
ALTER TABLE task ADD COLUMN IF NOT EXISTS created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 更新现有任务的 scheduled_date 字段（如果为空，设置为 due_date）
UPDATE task SET scheduled_date = due_date WHERE scheduled_date IS NULL;

-- 显示更新后的表结构
DESCRIBE task;
