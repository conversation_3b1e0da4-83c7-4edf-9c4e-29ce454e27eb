import SwiftUI

// MARK: - 环境选择器视图
struct EnvironmentSelectorView: View {
    let currentEnvironment: EnvironmentManager.Environment
    let onEnvironmentSelected: (EnvironmentManager.Environment) -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                // 标题和说明
                VStack(spacing: 16) {
                    Image(systemName: "gear.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("环境配置")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("选择API服务器环境")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 40)
                
                // 当前环境显示
                VStack(spacing: 12) {
                    Text("当前环境")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 12) {
                        Circle()
                            .fill(environmentColor(currentEnvironment))
                            .frame(width: 12, height: 12)
                        
                        Text(currentEnvironment.displayName)
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                    .cornerRadius(16)
                    
                    Text(currentEnvironment.baseURL)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 20)
                }
                
                // 环境选择按钮
                VStack(spacing: 16) {
                    Text("切换到")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    ForEach(EnvironmentManager.Environment.allCases, id: \.self) { environment in
                        EnvironmentButton(
                            environment: environment,
                            isSelected: environment == currentEnvironment,
                            action: {
                                onEnvironmentSelected(environment)
                            }
                        )
                    }
                }
                
                Spacer()
                
                // 警告信息
                VStack(spacing: 8) {
                    HStack(spacing: 8) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("注意")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    
                    Text("切换环境将重新加载所有数据，请确保目标服务器可访问。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding(.horizontal, 32)
            .navigationTitle("环境配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 环境按钮组件
struct EnvironmentButton: View {
    let environment: EnvironmentManager.Environment
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 环境图标
                ZStack {
                    Circle()
                        .fill(environmentColor(environment))
                        .frame(width: 40, height: 40)

                    Image(systemName: environmentIcon(environment))
                        .font(.title3)
                        .foregroundColor(.white)
                }
                
                // 环境信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(environment.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(environment.baseURL)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 选中状态
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                } else {
                    Image(systemName: "circle")
                        .font(.title2)
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                isSelected ? 
                Color.blue.opacity(0.1) : 
                Color(.systemGray6)
            )
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isSelected ? Color.blue : Color.clear,
                        lineWidth: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isSelected)
    }
}

// MARK: - 辅助函数
private func environmentColor(_ environment: EnvironmentManager.Environment) -> Color {
    switch environment {
    case .production:
        return .green
    case .development:
        return .orange
    case .developmentBackup:
        return .blue
    }
}

private func environmentIcon(_ environment: EnvironmentManager.Environment) -> String {
    switch environment {
    case .production:
        return "checkmark.shield.fill"
    case .development:
        return "hammer.fill"
    case .developmentBackup:
        return "wrench.and.screwdriver.fill"
    }
}

// MARK: - 预览
struct EnvironmentSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        EnvironmentSelectorView(
            currentEnvironment: .production,
            onEnvironmentSelected: { _ in }
        )
    }
}
