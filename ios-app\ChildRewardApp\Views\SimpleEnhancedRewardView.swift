import SwiftUI

// MARK: - 简化版增强奖励页面
struct SimpleEnhancedRewardView: View {
    @State private var currentPoints: Int = 85
    @State private var selectedTab: Int = 0
    @State private var showingSpinWheel = false
    @State private var showingResult = false
    @State private var resultMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部积分卡片
            pointsCard
            
            // 标签页选择器
            Picker("选择页面", selection: $selectedTab) {
                Text("🎁 奖励区").tag(0)
                Text("⚡ 惩罚区").tag(1)
                Text("📋 记录").tag(2)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            
            // 内容区域
            ScrollView {
                VStack(spacing: 24) {
                    if selectedTab == 0 {
                        rewardsContent
                    } else if selectedTab == 1 {
                        penaltiesContent
                    } else {
                        historyContent
                    }
                    
                    Spacer(minLength: 32)
                }
                .padding(.horizontal, 20)
            }
        }
        .background(Color(.systemGroupedBackground))
        .sheet(isPresented: $showingSpinWheel) {
            SimpleSpinWheelView { result in
                handleSpinResult(result)
                showingSpinWheel = false
            }
        }
        .alert("结果", isPresented: $showingResult) {
            Button("确定") {
                showingResult = false
            }
        } message: {
            Text(resultMessage)
        }
    }
    
    // MARK: - 积分卡片
    private var pointsCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("我的积分")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("\(currentPoints)")
                        .font(.system(size: 42, weight: .bold, design: .rounded))
                        .foregroundColor(currentPoints >= 0 ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    HStack(spacing: 6) {
                        Circle()
                            .fill(currentPoints >= 0 ? .green : .red)
                            .frame(width: 8, height: 8)
                        
                        Text(currentPoints >= 0 ? "可获奖励" : "需要惩罚")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(currentPoints >= 0 ? .green : .red)
                    }
                    
                    Text("状态指示")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 积分进度条
            ProgressView(value: max(0, Double(currentPoints)), total: 100)
                .progressViewStyle(LinearProgressViewStyle(tint: currentPoints >= 0 ? .green : .red))
                .scaleEffect(y: 2)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - 奖励内容
    private var rewardsContent: some View {
        VStack(spacing: 24) {
            // 转盘抽奖卡片
            VStack(spacing: 20) {
                VStack(spacing: 8) {
                    Text("🎰 幸运转盘")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("转动转盘，获得惊喜奖励！")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // 转盘预览图
                ZStack {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 120, height: 120)
                        .shadow(color: .black.opacity(0.1), radius: 8)
                    
                    // 简化的转盘扇形
                    ForEach(0..<6, id: \.self) { index in
                        Path { path in
                            let center = CGPoint(x: 60, y: 60)
                            let angle = 60.0
                            let startAngle = Double(index) * angle
                            path.move(to: center)
                            path.addArc(
                                center: center,
                                radius: 55,
                                startAngle: .degrees(startAngle - 90),
                                endAngle: .degrees(startAngle + angle - 90),
                                clockwise: false
                            )
                            path.closeSubpath()
                        }
                        .fill([Color.red, .blue, .green, .orange, .purple, .pink][index].opacity(0.7))
                        .stroke(.white, lineWidth: 1)
                    }
                    .frame(width: 120, height: 120)
                    
                    Circle()
                        .fill(.white)
                        .frame(width: 20, height: 20)
                        .shadow(color: .black.opacity(0.2), radius: 2)
                }
                
                Text("消耗 50 积分")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Button(action: { showingSpinWheel = true }) {
                    Text("开始抽奖")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(currentPoints >= 50 ? .blue : .gray)
                        )
                }
                .disabled(currentPoints < 50)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.08), radius: 12, x: 0, y: 6)
            )
            
            // 直接兑换区域
            VStack(alignment: .leading, spacing: 16) {
                Text("🛍️ 直接兑换")
                    .font(.title3)
                    .fontWeight(.bold)
                
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    QuickRewardCard(name: "看动画片", icon: "tv", cost: 20, color: .blue, canAfford: currentPoints >= 20) {
                        if currentPoints >= 20 {
                            currentPoints -= 20
                            showResult("成功兑换「看动画片」！")
                        }
                    }
                    
                    QuickRewardCard(name: "冰淇淋", icon: "snowflake", cost: 30, color: .cyan, canAfford: currentPoints >= 30) {
                        if currentPoints >= 30 {
                            currentPoints -= 30
                            showResult("成功兑换「冰淇淋」！")
                        }
                    }
                    
                    QuickRewardCard(name: "小零食", icon: "bag", cost: 15, color: .orange, canAfford: currentPoints >= 15) {
                        if currentPoints >= 15 {
                            currentPoints -= 15
                            showResult("成功兑换「小零食」！")
                        }
                    }
                    
                    QuickRewardCard(name: "晚睡30分钟", icon: "moon.stars", cost: 40, color: .purple, canAfford: currentPoints >= 40) {
                        if currentPoints >= 40 {
                            currentPoints -= 40
                            showResult("成功兑换「晚睡30分钟」！")
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - 惩罚内容
    private var penaltiesContent: some View {
        VStack(spacing: 24) {
            if currentPoints < 0 {
                // 惩罚说明
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.orange)
                    
                    Text("需要接受惩罚")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("你的积分为负，需要通过完成惩罚任务来重新获得积分")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button("接受惩罚") {
                        showResult("随机惩罚：整理书桌，完成后可获得10积分")
                    }
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.orange)
                    )
                }
                .padding(40)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.orange.opacity(0.1))
                )
            } else {
                // 积分为正时的提示
                VStack(spacing: 16) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.green)
                    
                    Text("表现良好！")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("你的积分为正，暂时不需要接受惩罚")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(40)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.green.opacity(0.1))
                )
            }
            
            // 惩罚说明
            VStack(alignment: .leading, spacing: 16) {
                Text("💡 惩罚说明")
                    .font(.title3)
                    .fontWeight(.bold)
                
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.title3)
                            .foregroundColor(.orange)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("轻度惩罚")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            
                            Text("简单的家务或学习任务，完成后获得5-15积分")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    HStack(spacing: 12) {
                        Image(systemName: "exclamationmark.octagon.fill")
                            .font(.title3)
                            .foregroundColor(.red)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("中度惩罚")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            
                            Text("较复杂的任务或短期限制，完成后获得15-30积分")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    HStack(spacing: 12) {
                        Image(systemName: "exclamationmark.shield.fill")
                            .font(.title3)
                            .foregroundColor(.purple)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("重度惩罚")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            
                            Text("困难的任务或长期限制，完成后获得30-50积分")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.ultraThinMaterial)
                )
            }
        }
    }
    
    // MARK: - 历史记录内容
    private var historyContent: some View {
        VStack(spacing: 16) {
            Text("功能开发中...")
                .font(.headline)
                .foregroundColor(.secondary)
                .padding(40)
        }
    }
    
    // MARK: - 辅助方法
    private func handleSpinResult(_ result: String) {
        currentPoints -= 50 // 抽奖消耗积分
        showResult("恭喜获得「\(result)」！")
    }
    
    private func showResult(_ message: String) {
        resultMessage = message
        showingResult = true
    }
}

// MARK: - 快速奖励卡片
struct QuickRewardCard: View {
    let name: String
    let icon: String
    let cost: Int
    let color: Color
    let canAfford: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(color.opacity(0.2))
                    )
                
                Text(name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                
                Text("\(cost) 积分")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(canAfford ? .ultraThinMaterial : .gray.opacity(0.1))
                    .stroke(canAfford ? color.opacity(0.3) : .clear, lineWidth: 1)
            )
        }
        .disabled(!canAfford)
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 简化转盘视图
struct SimpleSpinWheelView: View {
    let onComplete: (String) -> Void
    @State private var rotation: Double = 0
    @State private var isSpinning = false
    
    private let rewards = ["看动画片", "冰淇淋", "10积分", "小玩具", "晚睡30分钟", "选择晚餐"]
    
    var body: some View {
        VStack(spacing: 30) {
            Text("🎰 幸运转盘")
                .font(.title)
                .fontWeight(.bold)
            
            // 转盘
            ZStack {
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 280, height: 280)
                    .shadow(color: .black.opacity(0.2), radius: 15, x: 0, y: 8)
                
                // 转盘扇形区域
                ForEach(Array(rewards.enumerated()), id: \.offset) { index, reward in
                    Path { path in
                        let center = CGPoint(x: 140, y: 140)
                        let angle = 60.0
                        let startAngle = Double(index) * angle
                        path.move(to: center)
                        path.addArc(
                            center: center,
                            radius: 135,
                            startAngle: .degrees(startAngle - 90),
                            endAngle: .degrees(startAngle + angle - 90),
                            clockwise: false
                        )
                        path.closeSubpath()
                    }
                    .fill([Color.red, .blue, .green, .orange, .purple, .pink][index].opacity(0.8))
                    .stroke(.white, lineWidth: 2)
                    
                    Text(reward)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .offset(
                            x: cos(.degrees(Double(index) * 60 + 30 - 90)) * 80,
                            y: sin(.degrees(Double(index) * 60 + 30 - 90)) * 80
                        )
                }
                .frame(width: 280, height: 280)
                
                // 中心圆
                Circle()
                    .fill(.white)
                    .frame(width: 60, height: 60)
                    .shadow(color: .black.opacity(0.1), radius: 5)
                
                // 中心图标
                Image(systemName: "star.fill")
                    .font(.title2)
                    .foregroundColor(.orange)
            }
            .rotationEffect(.degrees(rotation))
            .animation(.easeOut(duration: isSpinning ? 3.0 : 0.5), value: rotation)
            
            // 指针
            VStack {
                Triangle()
                    .fill(.red)
                    .frame(width: 20, height: 20)
                    .shadow(color: .black.opacity(0.3), radius: 3)
                
                Spacer()
            }
            .offset(y: -140 - 10)
            
            // 抽奖按钮
            Button(action: spinWheel) {
                HStack(spacing: 12) {
                    Image(systemName: isSpinning ? "arrow.clockwise" : "play.fill")
                        .font(.title3)
                    
                    Text(isSpinning ? "转盘中..." : "开始抽奖")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(isSpinning ? .gray : .blue)
                        .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                )
            }
            .disabled(isSpinning)
            .scaleEffect(isSpinning ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSpinning)
        }
        .padding(40)
    }
    
    private func spinWheel() {
        guard !isSpinning else { return }
        
        isSpinning = true
        
        // 随机选择一个奖品
        let randomIndex = Int.random(in: 0..<rewards.count)
        let selectedReward = rewards[randomIndex]
        
        // 计算目标角度
        let targetAngle = Double(randomIndex) * 60
        let spins = Double.random(in: 5...8) * 360 // 5-8圈
        let finalRotation = rotation + spins + (360 - targetAngle)
        
        // 开始旋转
        withAnimation(.easeOut(duration: 3.0)) {
            rotation = finalRotation
        }
        
        // 延迟显示结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.2) {
            isSpinning = false
            onComplete(selectedReward)
        }
    }
}

// MARK: - 三角形指针
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.closeSubpath()
        return path
    }
}

// MARK: - 预览
struct SimpleEnhancedRewardView_Previews: PreviewProvider {
    static var previews: some View {
        SimpleEnhancedRewardView()
            .preferredColorScheme(.light)
    }
}
