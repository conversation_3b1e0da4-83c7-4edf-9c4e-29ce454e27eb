import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HashRouter } from 'react-router-dom';
import App from './App';

// 根据环境选择路由模式
// 生产环境如果nginx没有配置try_files，使用HashRouter
const Router = import.meta.env.PROD && !import.meta.env.VITE_USE_BROWSER_ROUTER
  ? HashRouter
  : BrowserRouter;

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Router>
      <App />
    </Router>
  </React.StrictMode>
);