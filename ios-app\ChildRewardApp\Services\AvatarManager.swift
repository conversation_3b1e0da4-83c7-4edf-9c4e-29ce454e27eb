import UIKit
import Foundation

class AvatarManager: ObservableObject {
    static let shared = AvatarManager()
    
    @Published var avatarImage: UIImage?
    
    private let userDefaults = UserDefaults.standard
    private let avatarKey = "user_avatar_image"
    
    private init() {
        loadAvatarImage()
    }
    
    // 保存头像图片
    func saveAvatarImage(_ image: UIImage) {
        // 缩放图片到合适的尺寸
        let resizedImage = resizeImage(image, to: CGSize(width: 160, height: 160))
        
        // 转换为JPEG数据并压缩
        if let imageData = resizedImage.jpegData(compressionQuality: 0.8) {
            userDefaults.set(imageData, forKey: avatarKey)
            
            DispatchQueue.main.async {
                self.avatarImage = resizedImage
            }
            
            print("✅ 头像已保存，大小: \(imageData.count) bytes")
        } else {
            print("❌ 头像保存失败：无法转换为JPEG数据")
        }
    }
    
    // 加载头像图片
    func loadAvatarImage() {
        guard let imageData = userDefaults.data(forKey: avatarKey),
              let image = UIImage(data: imageData) else {
            print("📷 未找到保存的头像，使用默认头像")
            avatarImage = nil
            return
        }
        
        DispatchQueue.main.async {
            self.avatarImage = image
        }
        
        print("✅ 头像加载成功")
    }
    
    // 删除头像图片
    func deleteAvatarImage() {
        userDefaults.removeObject(forKey: avatarKey)
        
        DispatchQueue.main.async {
            self.avatarImage = nil
        }
        
        print("🗑️ 头像已删除")
    }
    
    // 缩放图片到指定尺寸
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }
    
    // 检查是否有自定义头像
    var hasCustomAvatar: Bool {
        return avatarImage != nil
    }
}

// UIImage扩展，用于图片处理
extension UIImage {
    // 裁剪为正方形
    func cropToSquare() -> UIImage {
        let originalWidth = size.width
        let originalHeight = size.height
        let cropSize = min(originalWidth, originalHeight)
        
        let cropRect = CGRect(
            x: (originalWidth - cropSize) / 2,
            y: (originalHeight - cropSize) / 2,
            width: cropSize,
            height: cropSize
        )
        
        guard let cgImage = cgImage?.cropping(to: cropRect) else {
            return self
        }
        
        return UIImage(cgImage: cgImage, scale: scale, orientation: imageOrientation)
    }
    
    // 调整图片大小并保持质量
    func resized(to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: size))
        }
    }
}
