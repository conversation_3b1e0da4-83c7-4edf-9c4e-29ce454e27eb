import React from 'react';
import RichTextRenderer from '../components/common/RichTextRenderer';
import { AppleDesignSystem } from '../design/AppleDesignSystem';

const TestRichText = () => {
  const testContent = `这是一个测试任务的描述。

任务要求：
1. 完成数学作业第15-20页
2. 背诵古诗《静夜思》
3. 练习英语单词20个

作业图片：
https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=数学作业示例

参考链接：
https://www.example.com/homework-help

注意事项：
- 字迹要工整
- 按时完成
- 有问题及时询问

更多图片：
https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=英语作业
https://via.placeholder.com/350x250/45B7D1/FFFFFF?text=古诗背诵`;

  return (
    <div style={{
      padding: AppleDesignSystem.spacing.lg,
      maxWidth: '600px',
      margin: '0 auto',
      background: 'white',
      minHeight: '100vh'
    }}>
      <h1 style={{
        fontSize: '24px',
        marginBottom: AppleDesignSystem.spacing.lg,
        color: AppleDesignSystem.colors.semantic.label
      }}>
        富文本渲染测试
      </h1>
      
      <div style={{
        background: AppleDesignSystem.colors.semantic.systemFill,
        borderRadius: AppleDesignSystem.borderRadius.medium,
        padding: AppleDesignSystem.spacing.md
      }}>
        <RichTextRenderer content={testContent} />
      </div>
    </div>
  );
};

export default TestRichText;