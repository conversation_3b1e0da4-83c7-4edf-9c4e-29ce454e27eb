package com.example.childreward.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 防重复提交注解，用于接口级别的防重复提交
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PreventDuplicateSubmit {
    
    /**
     * 锁定时间，单位毫秒，默认5秒
     */
    long value() default 5000;
    
    /**
     * 错误提示消息
     */
    String message() default "操作过于频繁，请稍后再试";
} 