package com.example.childreward.service;

import com.example.childreward.entity.ExchangeItem;
import com.example.childreward.entity.ExchangeRecord;

import java.util.List;

/**
 * 兑换服务接口
 */
public interface ExchangeService {
    
    // ========== 兑换商品管理 ==========
    
    /**
     * 获取所有兑换商品（管理端）
     */
    List<ExchangeItem> getAllExchangeItems();
    
    /**
     * 获取可用的兑换商品（用户端）
     */
    List<ExchangeItem> getAvailableExchangeItems();
    
    /**
     * 按分类获取可用的兑换商品
     */
    List<ExchangeItem> getAvailableExchangeItemsByCategory(String category);
    
    /**
     * 获取所有商品分类
     */
    List<String> getAllCategories();
    
    /**
     * 根据ID获取兑换商品
     */
    ExchangeItem getExchangeItemById(Long id);
    
    /**
     * 创建兑换商品
     */
    ExchangeItem createExchangeItem(ExchangeItem exchangeItem);
    
    /**
     * 更新兑换商品
     */
    ExchangeItem updateExchangeItem(Long id, ExchangeItem exchangeItem);
    
    /**
     * 删除兑换商品
     */
    void deleteExchangeItem(Long id);
    
    /**
     * 启用/禁用兑换商品
     */
    ExchangeItem toggleExchangeItemStatus(Long id);
    
    // ========== 兑换操作 ==========
    
    /**
     * 执行兑换操作
     */
    ExchangeRecord exchangeItem(Long exchangeItemId);
    
    /**
     * 使用兑换记录
     */
    ExchangeRecord useExchangeRecord(Long recordId, String notes);
    
    // ========== 兑换记录查询 ==========
    
    /**
     * 获取所有兑换记录
     */
    List<ExchangeRecord> getAllExchangeRecords();
    
    /**
     * 获取未使用的兑换记录
     */
    List<ExchangeRecord> getUnusedExchangeRecords();
    
    /**
     * 根据ID获取兑换记录
     */
    ExchangeRecord getExchangeRecordById(Long id);
    
    /**
     * 处理过期的兑换记录
     */
    void processExpiredRecords();
}
