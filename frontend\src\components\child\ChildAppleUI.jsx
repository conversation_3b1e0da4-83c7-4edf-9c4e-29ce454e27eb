/**
 * 儿童端苹果风格UI组件
 * 专为iPad和儿童使用优化
 */

import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import { AppleDesignSystem, mediaQueries } from '../../design/AppleDesignSystem';

// ==================== 儿童端主容器 ====================
export const ChildContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, 
    ${AppleDesignSystem.colors.child.background} 0%, 
    ${AppleDesignSystem.colors.semantic.systemGroupedBackground} 100%);
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  padding: ${AppleDesignSystem.spacing.md};
  
  /* iPad 优化 */
  ${mediaQueries.ipad} {
    padding: ${AppleDesignSystem.spacing.xl};
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: ${AppleDesignSystem.spacing.lg};
  }
  
  /* iPhone 适配 */
  ${mediaQueries.iphone} {
    padding: ${AppleDesignSystem.spacing.md};
  }
`;

// ==================== 儿童端头部 ====================
export const ChildHeader = styled(motion.header)`
  background: ${AppleDesignSystem.colors.child.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xxl};
  padding: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: ${AppleDesignSystem.spacing.md};
  
  ${mediaQueries.iphone} {
    flex-direction: column;
    text-align: center;
  }
  
  ${mediaQueries.ipad} {
    padding: ${AppleDesignSystem.spacing.xl};
  }
`;

// ==================== 儿童端标题 ====================
export const ChildTitle = styled.h1`
  font-family: ${AppleDesignSystem.typography.fontFamily.display};
  font-size: ${AppleDesignSystem.typography.textStyles.largeTitle.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.bold};
  color: ${AppleDesignSystem.colors.child.primary};
  margin: 0;
  line-height: ${AppleDesignSystem.typography.textStyles.largeTitle.lineHeight};
  
  /* iPad 更大字体 */
  ${mediaQueries.ipad} {
    font-size: 48px;
    line-height: 1.1;
  }
  
  /* 添加文字阴影增强可读性 */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

export const ChildSubtitle = styled.p`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.subheadline.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.regular};
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  margin: ${AppleDesignSystem.spacing.xs} 0 0 0;
  line-height: 1.3;

  ${mediaQueries.ipad} {
    font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
  }
`;

// ==================== 积分卡片 ====================
export const PointsCard = styled(motion.div)`
  background: linear-gradient(135deg, 
    ${AppleDesignSystem.colors.child.primary} 0%, 
    ${AppleDesignSystem.colors.child.secondary} 100%);
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  color: white;
  text-align: center;
  box-shadow: ${AppleDesignSystem.shadows.lg};
  min-width: 120px;
  
  ${mediaQueries.ipad} {
    min-width: 160px;
    padding: ${AppleDesignSystem.spacing.xl};
  }
`;

export const PointsNumber = styled.div`
  font-family: ${AppleDesignSystem.typography.fontFamily.display};
  font-size: 48px;
  font-weight: ${AppleDesignSystem.typography.fontWeight.bold};
  line-height: 1;
  margin-bottom: ${AppleDesignSystem.spacing.xs};
  
  ${mediaQueries.ipad} {
    font-size: 64px;
  }
  
  /* 数字动画效果 */
  background: linear-gradient(45deg, white, rgba(255,255,255,0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

export const PointsLabel = styled.div`
  font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.medium};
  opacity: 0.9;
`;

// ==================== 儿童端大按钮 ====================
export const ChildBigButton = styled(motion.button)`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.title3.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  
  min-height: 120px;
  width: 100%;
  padding: ${AppleDesignSystem.spacing.lg};
  border: none;
  border-radius: ${AppleDesignSystem.borderRadius.xxl};
  cursor: pointer;
  
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: ${AppleDesignSystem.spacing.md};
  
  /* 渐变背景 */
  ${props => {
    const colors = {
      primary: `linear-gradient(135deg, ${AppleDesignSystem.colors.child.primary} 0%, ${AppleDesignSystem.colors.system.blue} 100%)`,
      success: `linear-gradient(135deg, ${AppleDesignSystem.colors.child.success} 0%, ${AppleDesignSystem.colors.system.green} 100%)`,
      warning: `linear-gradient(135deg, ${AppleDesignSystem.colors.child.warning} 0%, ${AppleDesignSystem.colors.system.orange} 100%)`,
      playful: `linear-gradient(135deg, ${AppleDesignSystem.colors.child.playful} 0%, ${AppleDesignSystem.colors.system.pink} 100%)`,
      friendly: `linear-gradient(135deg, ${AppleDesignSystem.colors.child.friendly} 0%, ${AppleDesignSystem.colors.system.teal} 100%)`
    };
    return css`
      background: ${colors[props.variant] || colors.primary};
    `;
  }}
  
  color: white;
  box-shadow: ${AppleDesignSystem.shadows.lg};
  transition: all ${AppleDesignSystem.animation.duration.normal} ${AppleDesignSystem.animation.easing.standard};
  
  /* iPad 更大尺寸 */
  ${mediaQueries.ipad} {
    min-height: 160px;
    font-size: ${AppleDesignSystem.typography.textStyles.title1.fontSize};
  }
  
  /* 悬停效果 */
  ${mediaQueries.mouse} {
    &:hover:not(:disabled) {
      transform: translateY(-4px);
      box-shadow: ${AppleDesignSystem.shadows.xl};
    }
  }
  
  /* 触摸反馈 */
  ${mediaQueries.touch} {
    &:active:not(:disabled) {
      transform: scale(0.96);
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
`;

// ==================== 按钮图标 ====================
export const ChildButtonIcon = styled.div`
  font-size: 48px;
  line-height: 1;
  
  ${mediaQueries.ipad} {
    font-size: 64px;
  }
  
  /* 图标动画 */
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
`;

// ==================== 儿童端网格 ====================
export const ChildGrid = styled.div`
  display: grid;
  gap: ${AppleDesignSystem.spacing.lg};
  grid-template-columns: 1fr;
  
  ${mediaQueries.ipad} {
    grid-template-columns: repeat(2, 1fr);
    gap: ${AppleDesignSystem.spacing.xl};
  }
  
  /* 大屏iPad Pro */
  @media (min-width: ${AppleDesignSystem.breakpoints.ipad.pro129}) {
    grid-template-columns: repeat(3, 1fr);
  }
`;

// ==================== 任务卡片 ====================
export const ChildTaskCard = styled(motion.div)`
  background: ${AppleDesignSystem.colors.child.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};
  transition: all ${AppleDesignSystem.animation.duration.normal} ${AppleDesignSystem.animation.easing.standard};
  
  ${mediaQueries.mouse} {
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${AppleDesignSystem.shadows.lg};
    }
  }
`;

export const ChildTaskTitle = styled.h3`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.title3.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  color: ${AppleDesignSystem.colors.semantic.label};
  margin: 0 0 ${AppleDesignSystem.spacing.sm} 0;
  line-height: ${AppleDesignSystem.typography.textStyles.title3.lineHeight};
  
  ${mediaQueries.ipad} {
    font-size: ${AppleDesignSystem.typography.textStyles.title2.fontSize};
  }
`;

export const ChildTaskDescription = styled.p`
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  margin: 0 0 ${AppleDesignSystem.spacing.md} 0;
  line-height: ${AppleDesignSystem.typography.textStyles.body.lineHeight};
  
  ${mediaQueries.ipad} {
    font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
  }
`;

// ==================== 徽章组件 ====================
export const ChildBadge = styled.span`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.caption2.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.bold};
  padding: 4px 8px;
  border-radius: ${AppleDesignSystem.borderRadius.full};
  background: ${AppleDesignSystem.colors.child.primary};
  color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  line-height: 1;
`;

// ==================== 状态徽章 ====================
export const ChildStatusBadge = styled.span`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.footnote.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  padding: ${AppleDesignSystem.spacing.xs} ${AppleDesignSystem.spacing.sm};
  border-radius: ${AppleDesignSystem.borderRadius.full};
  display: inline-flex;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.xs};
  
  ${props => {
    const colors = {
      completed: AppleDesignSystem.colors.child.success,
      inProgress: AppleDesignSystem.colors.child.warning,
      pending: AppleDesignSystem.colors.child.secondary
    };
    return css`
      background: ${colors[props.status] || colors.pending};
      color: white;
    `;
  }}
`;

// ==================== 底部标签栏 ====================
export const ChildTabBar = styled.nav`
  background: ${AppleDesignSystem.colors.child.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.md};
  box-shadow: ${AppleDesignSystem.shadows.card};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  
  display: flex;
  justify-content: space-around;
  align-items: center;
  
  ${mediaQueries.ipad} {
    padding: ${AppleDesignSystem.spacing.lg};
    margin: 0 ${AppleDesignSystem.spacing.xl};
  }
`;

export const ChildTabItem = styled(motion.button).withConfig({
  shouldForwardProp: (prop) => !['active'].includes(prop)
})`
  background: none;
  border: none;
  cursor: pointer;
  padding: ${AppleDesignSystem.spacing.sm};
  border-radius: ${AppleDesignSystem.borderRadius.lg};
  
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.xs};
  min-width: 60px;
  
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.caption1.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.medium};
  
  color: ${props => props.active 
    ? AppleDesignSystem.colors.child.primary 
    : AppleDesignSystem.colors.semantic.secondaryLabel};
  
  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};
  
  &:active {
    transform: scale(0.95);
  }
  
  .icon {
    font-size: 28px;
    
    ${mediaQueries.ipad} {
      font-size: 32px;
    }
  }
  
  ${props => props.active && css`
    background: ${AppleDesignSystem.colors.child.primary}1A;
  `}
`;

// ==================== 卡片组件 ====================
export const ChildCard = styled(motion.div).withConfig({
  shouldForwardProp: (prop) => !['fullWidth', 'interactive'].includes(prop)
})`
  background: ${AppleDesignSystem.colors.child.surface};
  border-radius: ${AppleDesignSystem.borderRadius.xl};
  padding: ${AppleDesignSystem.spacing.lg};
  box-shadow: ${AppleDesignSystem.shadows.card};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  ${props => props.fullWidth && css`
    grid-column: 1 / -1;
  `}

  ${props => props.interactive && css`
    cursor: pointer;
    transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};

    &:hover {
      transform: translateY(-2px);
      box-shadow: ${AppleDesignSystem.shadows.elevated};
    }

    &:active {
      transform: translateY(0);
    }
  `}
`;

export const ChildCardTitle = styled.h3`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.headline.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  color: ${AppleDesignSystem.colors.semantic.label};
  margin: 0 0 ${AppleDesignSystem.spacing.sm} 0;
  line-height: 1.2;
`;

export const ChildCardContent = styled.div`
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  line-height: 1.4;
  margin: ${AppleDesignSystem.spacing.sm} 0;
`;

export const ChildButton = styled(motion.button)`
  background: ${AppleDesignSystem.colors.child.primary};
  color: white;
  border: none;
  border-radius: ${AppleDesignSystem.borderRadius.lg};
  padding: ${AppleDesignSystem.spacing.md} ${AppleDesignSystem.spacing.lg};
  font-family: ${AppleDesignSystem.typography.fontFamily.system};
  font-size: ${AppleDesignSystem.typography.textStyles.body.fontSize};
  font-weight: ${AppleDesignSystem.typography.fontWeight.semibold};
  cursor: pointer;

  transition: all ${AppleDesignSystem.animation.duration.fast} ${AppleDesignSystem.animation.easing.standard};

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${AppleDesignSystem.shadows.button};
  }

  &:active {
    transform: translateY(0);
  }
`;

export default {
  ChildContainer,
  ChildHeader,
  ChildTitle,
  ChildSubtitle,
  PointsCard,
  PointsNumber,
  PointsLabel,
  ChildBigButton,
  ChildButtonIcon,
  ChildGrid,
  ChildCard,
  ChildCardTitle,
  ChildCardContent,
  ChildButton,
  ChildTaskCard,
  ChildTaskTitle,
  ChildTaskDescription,
  ChildBadge,
  ChildStatusBadge,
  ChildTabBar,
  ChildTabItem
};
