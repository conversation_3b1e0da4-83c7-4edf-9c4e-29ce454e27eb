import SwiftUI
import UIKit

// MARK: - HTML文本渲染组件
struct HTMLTextView: UIViewRepresentable {
    let htmlString: String
    let font: UIFont
    let textColor: UIColor
    
    init(htmlString: String, font: UIFont = UIFont.systemFont(ofSize: 16), textColor: UIColor = UIColor.label) {
        self.htmlString = htmlString
        self.font = font
        self.textColor = textColor
    }
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.isEditable = false
        textView.isScrollEnabled = false
        textView.backgroundColor = UIColor.clear
        textView.textContainerInset = UIEdgeInsets.zero
        textView.textContainer.lineFragmentPadding = 0

        // 设置容器宽度限制
        textView.textContainer.widthTracksTextView = true
        textView.textContainer.size = CGSize(width: 0, height: CGFloat.greatestFiniteMagnitude)

        // 设置HTML内容
        updateTextView(textView)

        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        updateTextView(uiView)
    }
    
    private func updateTextView(_ textView: UITextView) {
        guard !htmlString.isEmpty else {
            textView.text = ""
            return
        }
        
        // 创建HTML字符串，添加基本样式
        let styledHTML = """
        <html>
        <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: \(font.pointSize)px;
            color: \(textColor.hexString);
            line-height: 1.4;
            margin: 0;
            padding: 0;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        p { margin: 8px 0; }
        ul, ol { margin: 8px 0; padding-left: 20px; }
        li { margin: 4px 0; }
        strong, b { font-weight: 600; }
        em, i { font-style: italic; }
        h1, h2, h3, h4, h5, h6 { margin: 12px 0 8px 0; font-weight: 600; }
        img {
            max-width: 100% !important;
            width: auto !important;
            height: auto !important;
            display: block;
            margin: 8px auto;
            object-fit: contain;
            box-sizing: border-box;
        }
        * {
            max-width: 100%;
            box-sizing: border-box;
        }
        </style>
        </head>
        <body>
        \(htmlString)
        </body>
        </html>
        """
        
        // 转换HTML为NSAttributedString
        if let data = styledHTML.data(using: .utf8) {
            do {
                let attributedString = try NSAttributedString(
                    data: data,
                    options: [
                        .documentType: NSAttributedString.DocumentType.html,
                        .characterEncoding: String.Encoding.utf8.rawValue
                    ],
                    documentAttributes: nil
                )
                
                // 应用自定义字体
                let mutableAttributedString = NSMutableAttributedString(attributedString: attributedString)
                let range = NSRange(location: 0, length: mutableAttributedString.length)
                
                // 设置字体
                mutableAttributedString.addAttribute(.font, value: font, range: range)
                mutableAttributedString.addAttribute(.foregroundColor, value: textColor, range: range)
                
                textView.attributedText = mutableAttributedString
            } catch {
                print("❌ HTML渲染失败: \(error)")
                textView.text = htmlString.stripHTML()
            }
        } else {
            textView.text = htmlString.stripHTML()
        }
    }
}

// MARK: - UIColor扩展
extension UIColor {
    var hexString: String {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        return String(format: "#%02X%02X%02X",
                     Int(red * 255),
                     Int(green * 255),
                     Int(blue * 255))
    }
}

// MARK: - String扩展
extension String {
    func stripHTML() -> String {
        return self.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression, range: nil)
    }
    
    var isHTML: Bool {
        return self.contains("<") && self.contains(">")
    }
}

// MARK: - 简化的富文本组件
struct RichTextView: View {
    let text: String
    let font: Font
    let color: Color
    
    init(_ text: String, font: Font = .body, color: Color = .primary) {
        self.text = text
        self.font = font
        self.color = color
    }
    
    var body: some View {
        if text.isHTML {
            HTMLTextView(
                htmlString: text,
                font: UIFont.systemFont(ofSize: 16),
                textColor: UIColor(color)
            )
            .frame(maxWidth: .infinity, alignment: .leading)
            .clipped() // 确保内容不会超出边界
        } else {
            Text(text)
                .font(font)
                .foregroundColor(color)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}
