---
type: "always_apply"
---

# 目的
将web前端项目复刻成ios-app，从web应用改成ios应用，最终需要安装在ipad设备上使用。

## 要求
1. 原有的功能，复刻后也都需要保留
2. 界面UI需要美观大气，可以适当增加毛玻璃效果，符合苹果规范标准
3. 开发需要考虑性能，交互以及接口访问尽可能的快
4. 涉及思路需要清晰，全局考虑周全，避免出现严重的设计问题，技术栈要求现在较新的前沿技术
5. 最终打包的文件需要是ipa格式，我需要安装到ipad上，用巨魔签名安装测试
6. 家长端(web版)：适配pc和手机端  儿童端(ipad版ios原生应用)：适配ipad
7. 尽量使用第三方开源项目来实现需要的功能，避免重复造轮子
8. 功能开发完成后都需要完整的自测一遍，确保交付给我的是正确的
9. 最终打包的ipa安装包需要保证能在ios16.6系统的运行
10. 如果需要切换jdk，可以使用sdkman完成
11. 每次修改完客户端后，都需要重新编译并安装到模拟器，方便我来测试