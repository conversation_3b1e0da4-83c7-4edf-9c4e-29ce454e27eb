@echo off
echo Building CRS Backend...
echo.

echo Cleaning and building JAR package...
mvn clean package -DskipTests

if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo JAR file location: target/
echo.

REM Show the generated JAR file
for %%f in (target\*.jar) do (
    if not "%%f"=="target\*.jar" (
        if not "%%~nf"=="%%~nf.original" (
            echo Generated JAR: %%f
            echo.
            echo To copy to packages directory as crs-backend.jar:
            echo copy "%%f" "..\packages\crs-backend.jar"
        )
    )
)

echo.
pause
