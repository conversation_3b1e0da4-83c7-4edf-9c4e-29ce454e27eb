import SwiftUI

// MARK: - 任务页面视图
struct TasksView: View {
    @ObservedObject var taskViewModel: TaskViewModel
    @Binding var selectedTask: AppTask?
    @Binding var showTaskDetail: Bool
    
    var body: some View {
        Group {
            if showTaskDetail, let task = selectedTask {
                // 任务详情页
                TaskDetailView(
                    task: task,
                    onBack: {
                        selectedTask = nil
                        showTaskDetail = false
                    },
                    onAction: { action in
                        taskViewModel.handleTaskAction(action, for: task)
                    }
                )
            } else {
                // 任务列表
                taskListView
            }
        }
    }
    
    // MARK: - 任务列表视图
    
    private var taskListView: some View {
        VStack(spacing: 16) {
            // 调试信息区域（开发时使用）
            if taskViewModel.isLoading || !taskViewModel.errorMessage.isNil {
                debugInfoSection
            }
            
            // 任务内容
            if taskViewModel.isLoading {
                loadingView
            } else if taskViewModel.todayTasks.isEmpty {
                emptyStateView
            } else {
                taskGridView
            }
        }
        .padding(.bottom, 32)
    }
    
    // MARK: - 子视图
    
    private var debugInfoSection: some View {
        VStack(spacing: 12) {
            if taskViewModel.isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在加载任务...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(.ultraThinMaterial)
                .cornerRadius(10)
            }
            
            if let errorMessage = taskViewModel.errorMessage {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("加载失败")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    
                    Text(errorMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button("重试") {
                        taskViewModel.refreshTasks()
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding()
                .background(.ultraThinMaterial)
                .cornerRadius(10)
                .padding(.horizontal)
            }
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("正在加载任务...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            VStack(spacing: 8) {
                Text("太棒了！")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("今天没有待完成的任务")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Button("刷新任务") {
                taskViewModel.refreshTasks()
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(Color.blue)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var taskGridView: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 20),
            GridItem(.flexible(), spacing: 20)
        ], spacing: 20) {
            ForEach(taskViewModel.todayTasks, id: \.id) { task in
                TaskCard(
                    title: task.title,
                    description: "预计用时: \(task.formattedExpectedTime)",
                    points: task.basePoints,
                    isCompleted: task.isCompleted,
                    onToggle: {
                        handleTaskToggle(task)
                    },
                    onTap: {
                        selectedTask = task
                        showTaskDetail = true
                    },
                    status: task.status,
                    dueDate: task.formattedDueDate,
                    category: task.taskType
                )
            }
        }
        .padding(.horizontal, 32)
    }
    
    // MARK: - 辅助方法
    
    private func handleTaskToggle(_ task: TaskModel) {
        if task.canStart {
            taskViewModel.startTask(task.id)
        } else if task.canComplete {
            taskViewModel.completeTask(task.id)
        }
    }
}

// MARK: - 任务统计卡片
struct TaskStatsCard: View {
    let stats: TaskStats
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("今日统计")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.blue)
            }
            
            // 统计数据
            HStack(spacing: 20) {
                statItem(
                    title: "总任务",
                    value: "\(stats.totalTasks)",
                    color: .blue
                )
                
                statItem(
                    title: "已完成",
                    value: "\(stats.completedTasks)",
                    color: .green
                )
                
                statItem(
                    title: "进行中",
                    value: "\(stats.inProgressTasks)",
                    color: .orange
                )
                
                statItem(
                    title: "获得积分",
                    value: "\(stats.totalPoints)",
                    color: .purple
                )
            }
            
            // 完成率进度条
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("完成率")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(Int(stats.completionRate * 100))%")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                ProgressView(value: stats.completionRate)
                    .progressViewStyle(LinearProgressViewStyle(tint: .green))
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .padding(.horizontal, 32)
    }
    
    private func statItem(title: String, value: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 任务过滤器
struct TaskFilter: View {
    @Binding var selectedFilter: TaskFilterType
    
    var body: some View {
        HStack(spacing: 12) {
            ForEach(TaskFilterType.allCases, id: \.self) { filter in
                filterButton(filter)
            }
        }
        .padding(.horizontal, 32)
    }
    
    private func filterButton(_ filter: TaskFilterType) -> some View {
        Button(action: {
            selectedFilter = filter
        }) {
            Text(filter.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(selectedFilter == filter ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    selectedFilter == filter ?
                    Color.blue : Color.clear
                )
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 任务过滤类型
enum TaskFilterType: CaseIterable {
    case all
    case pending
    case inProgress
    case completed
    
    var displayName: String {
        switch self {
        case .all: return "全部"
        case .pending: return "待开始"
        case .inProgress: return "进行中"
        case .completed: return "已完成"
        }
    }
}

// MARK: - 扩展方法
extension Optional where Wrapped == String {
    var isNil: Bool {
        return self == nil
    }
}

// MARK: - 预览
struct TasksView_Previews: PreviewProvider {
    static var previews: some View {
        TasksView(
            taskViewModel: TaskViewModel(),
            selectedTask: .constant(nil),
            showTaskDetail: .constant(false)
        )
        .background(Color(.systemGroupedBackground))
    }
}
