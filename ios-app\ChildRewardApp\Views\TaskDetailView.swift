import SwiftUI
import WebKit

// 避免与Swift内置Task冲突
typealias AppTask = TaskModel

// 任务操作枚举
enum TaskAction {
    case start
    case complete
    case viewDetail
}

struct TaskDetailView: View {
    let task: AppTask
    let onBack: () -> Void
    let onAction: (TaskAction) -> Void

    @StateObject private var taskViewModel = TaskViewModel()
    @StateObject private var screenDimmingManager = ScreenDimmingManager.shared
    @State private var detailedTask: AppTask?
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 任务标题和状态
                    VStack(alignment: .leading, spacing: 12) {
                        Text(currentTask.title)
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        HStack {
                            Label(currentTask.status.displayName, systemImage: statusIcon)
                                .foregroundColor(statusColor)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(statusColor.opacity(0.1))
                                .cornerRadius(8)

                            Spacer()

                            Text("\(currentTask.basePoints) 积分")
                                .font(.headline)
                                .foregroundColor(.orange)
                        }
                    }
                    
                    // 任务描述
                    VStack(alignment: .leading, spacing: 8) {
                        Text("任务描述")
                            .font(.headline)

                        if let description = currentTask.description, !description.isEmpty {
                            // 检查是否包含HTML标签
                            if description.contains("<") && description.contains(">") {
                                HTMLView(htmlContent: description)
                                    .frame(minHeight: 100)
                            } else {
                                Text(description)
                                    .font(.body)
                                    .foregroundColor(.secondary)
                            }
                        } else {
                            Text("暂无任务描述 (描述为: \(currentTask.description ?? "nil"))")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .italic()
                        }
                    }
                    
                    // 任务信息
                    VStack(alignment: .leading, spacing: 16) {
                        Text("任务信息")
                            .font(.headline)

                        VStack(spacing: 12) {
                            InfoRow(title: "预计时间", value: currentTask.formattedExpectedTime, icon: "clock")

                            if let dueDate = currentTask.formattedDueDate {
                                InfoRow(title: "截止时间", value: dueDate, icon: "calendar")
                            }

                            if let createdAt = currentTask.createdTime {
                                InfoRow(title: "创建时间", value: formatDate(createdAt), icon: "plus.circle")
                            }
                        }
                    }
                    
                    Spacer()
                    
                    // 操作按钮
                    VStack(spacing: 12) {
                        if currentTask.canStart {
                            Button(action: {
                                print("🔘 TaskDetailView: 用户点击了开始任务按钮")
                                onAction(.start)
                                // 息屏功能现在在ContentView中处理，任务开始成功后自动启动
                                onBack()
                            }) {
                                Text("开始任务")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.blue)
                                    .cornerRadius(12)
                            }
                        } else if currentTask.canComplete {
                            Button(action: {
                                onAction(.complete)
                                onBack()
                            }) {
                                Text("完成任务")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.green)
                                    .cornerRadius(12)
                            }
                        }

                        Button(action: {
                            onBack()
                        }) {
                            Text("关闭")
                                .font(.headline)
                                .foregroundColor(.primary)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(12)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("任务详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        onBack()
                    }
                }
            }
        }
        .onAppear {
            testNetworkConnection()
            loadTaskDetail()
        }
        .onTapGesture {
            // 用户点击时处理息屏状态（但不影响正在进行的倒计时）
            if screenDimmingManager.isDimmed {
                screenDimmingManager.restoreBrightness()
            }
            // 注意：不在这里重新启动倒计时，避免干扰按钮操作
        }
    }

    // 当前显示的任务（优先使用详细任务，否则使用传入的任务）
    private var currentTask: AppTask {
        return detailedTask ?? task
    }

    private var statusIcon: String {
        switch currentTask.status {
        case .pending: return "clock"
        case .inProgress: return "play.circle"
        case .completed: return "checkmark.circle"
        case .approved: return "checkmark.circle.fill"
        case .overdue: return "exclamationmark.triangle"
        case .cancelled: return "xmark.circle"
        case .notStarted: return "circle"
        case .rejected: return "xmark.circle.fill"
        }
    }

    private var statusColor: Color {
        switch currentTask.status {
        case .pending: return .orange
        case .inProgress: return .blue
        case .completed: return .green
        case .approved: return .green
        case .overdue: return .red
        case .cancelled: return .gray
        case .notStarted: return .gray
        case .rejected: return .red
        }
    }

    private func testNetworkConnection() {
        print("🔍 测试网络连接...")
        Task {
            do {
                let url = URL(string: "http://localhost:18080/api/tasks/today")!
                let (data, response) = try await URLSession.shared.data(from: url)
                if let httpResponse = response as? HTTPURLResponse {
                    print("🔍 网络测试成功: 状态码=\(httpResponse.statusCode), 数据长度=\(data.count)")
                }
            } catch {
                print("🔍 网络测试失败: \(error)")
            }
        }
    }

    private func loadTaskDetail() {
        isLoading = true
        print("🔄 开始加载任务详情: ID=\(task.id), 标题=\(task.title)")

        Task {
            do {
                let networkService = NetworkService.shared
                print("🌐 调用网络服务获取任务详情...")
                let taskDetail = try await networkService.fetchTaskDetail(task.id)

                await MainActor.run {
                    self.detailedTask = taskDetail
                    self.isLoading = false
                    print("✅ 任务详情加载成功: ID=\(taskDetail.id), 标题=\(taskDetail.title)")
                    print("✅ 任务描述: \(taskDetail.description ?? "无描述")")
                    print("✅ 描述长度: \(taskDetail.description?.count ?? 0)")
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    print("❌ 加载任务详情失败: \(error)")
                    print("❌ 错误详情: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日 HH:mm"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return dateString
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(title)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontWeight(.medium)
        }
    }
}

// HTML内容显示组件
struct HTMLView: UIViewRepresentable {
    let htmlContent: String

    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        webView.scrollView.isScrollEnabled = false
        webView.isOpaque = false
        webView.backgroundColor = UIColor.clear
        return webView
    }

    func updateUIView(_ uiView: WKWebView, context: Context) {
        // 创建完整的HTML文档，包含样式
        let htmlString = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 16px;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 16px;
                    background-color: transparent;
                }
                img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 8px;
                    margin: 8px 0;
                }
                p {
                    margin: 8px 0;
                }
                h1, h2, h3, h4, h5, h6 {
                    margin: 16px 0 8px 0;
                    color: #1a1a1a;
                }
            </style>
        </head>
        <body>
            \(htmlContent)
        </body>
        </html>
        """

        uiView.loadHTMLString(htmlString, baseURL: nil)
    }

    func makeCoordinator() -> Coordinator {
        Coordinator()
    }

    class Coordinator: NSObject, WKNavigationDelegate {
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            // 网页加载完成后，调整高度
            webView.evaluateJavaScript("document.readyState") { (complete, error) in
                if complete != nil {
                    webView.evaluateJavaScript("document.body.scrollHeight") { (height, error) in
                        if let height = height as? CGFloat {
                            DispatchQueue.main.async {
                                webView.frame.size.height = height
                            }
                        }
                    }
                }
            }
        }
    }
}

#Preview {
    TaskDetailView(
        task: AppTask(
            id: 1,
            sourceTemplateId: nil,
            title: "完成数学作业",
            description: "<p>完成第3章练习题1-10，包括应用题和计算题</p><p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\"></p>",
            expectedMinutes: 45,
            basePoints: 15,
            dueTime: "18:00:00",
            dueDate: "2024-01-15",
            status: .pending,
            taskType: "REQUIRED",
            startTime: nil,
            endTime: nil,
            actualPoints: nil,
            createdTime: "2024-01-15T10:00:00Z",
            scheduledDate: "2024-01-15",
            actualMinutes: nil
        ),
        onBack: {},
        onAction: { _ in }
    )
}
