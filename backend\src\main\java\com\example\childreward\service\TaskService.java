package com.example.childreward.service;

import com.example.childreward.entity.Task;

import java.time.LocalDate;
import java.util.List;

public interface TaskService {
    
    /**
     * 创建新任务
     */
    Task createTask(Task task);
    
    /**
     * 更新任务
     */
    Task updateTask(Long id, Task task);
    
    /**
     * 删除任务
     */
    void deleteTask(Long id);
    
    /**
     * 获取任务详情
     */
    Task getTaskById(Long id);
    
    /**
     * 获取指定日期的所有任务
     */
    List<Task> getTasksByDate(LocalDate date);
    
    /**
     * 获取计划开始日期小于等于指定日期的所有任务
     */
    List<Task> getTasksByScheduledDateLessThanEqual(LocalDate date);
    
    /**
     * 获取所有任务
     */
    List<Task> getAllTasks();

    /**
     * 分页获取所有任务
     */
    List<Task> getAllTasks(int page, int size);
    
    /**
     * 开始任务
     */
    Task startTask(Long id);
    
    /**
     * 完成任务
     */
    Task completeTask(Long id);
    
    /**
     * 审批任务
     */
    Task approveTask(Long id, Integer actualPoints);
    
    /**
     * 拒绝任务
     */
    Task rejectTask(Long id, String reason);
    
    /**
     * 获取待审批的任务
     */
    List<Task> getPendingTasks();

    /**
     * 获取指定日期范围内的待审批任务
     */
    List<Task> getPendingTasksByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 获取指定日期范围和状态的任务
     */
    List<Task> getTasksByDateRangeAndStatus(LocalDate startDate, LocalDate endDate, String status);
    
    /**
     * 处理逾期任务
     */
    void processOverdueTasks();
    
    /**
     * 查找过期任务
     */
    List<Task> findOverdueTasks();

    /**
     * 获取往期任务（昨天以前的最近7天任务）
     */
    List<Task> getPastTasks();

    /**
     * 批准惩罚性任务，将进行扣分。
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    Task approvePenalty(Long taskId);
    
    /**
     * 拒绝或忽略惩罚性任务。
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    Task rejectPenalty(Long taskId);
} 