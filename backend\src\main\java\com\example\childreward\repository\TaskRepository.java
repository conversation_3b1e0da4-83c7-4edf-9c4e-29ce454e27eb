package com.example.childreward.repository;

import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskStatus;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {

    List<Task> findByDueDate(LocalDate dueDate);
    
    List<Task> findByDueDateAndStatus(LocalDate dueDate, TaskStatus status);
    
    @Query("SELECT t FROM Task t WHERE t.dueDate = ?1 AND t.status NOT IN ('COMPLETED', 'OVERDUE')")
    List<Task> findActiveTasksByDate(LocalDate date);
    
    @Query("SELECT t FROM Task t WHERE t.dueDate < ?1 AND t.status IN ('NOT_STARTED', 'IN_PROGRESS')")
    List<Task> findOverdueTasks(LocalDate date);
    
    @Query("SELECT t FROM Task t WHERE t.scheduledDate = ?1 ORDER BY " +
           "CASE WHEN t.status = 'IN_PROGRESS' THEN 1 " +
           "WHEN t.status = 'NOT_STARTED' THEN 2 " +
           "WHEN t.status = 'PENDING' THEN 3 " +
           "WHEN t.status = 'COMPLETED' THEN 4 " +
           "WHEN t.status = 'OVERDUE' THEN 5 END, t.dueTime ASC")
    List<Task> findTasksByDateOrderByStatusPriority(LocalDate date);

    // 优化版本：使用简单查询 + 应用层排序
    List<Task> findByScheduledDateOrderByDueTimeAsc(LocalDate date);

    // 高性能版本：只查询列表需要的字段，排除description字段
    @Query("SELECT new com.example.childreward.entity.Task(" +
           "t.id, t.sourceTemplateId, t.title, t.expectedMinutes, t.basePoints, " +
           "t.dueTime, t.dueDate, t.status, t.taskType, t.startTime, t.endTime, " +
           "t.actualPoints, t.createdTime, t.scheduledDate) " +
           "FROM Task t WHERE t.scheduledDate = ?1 ORDER BY t.dueTime ASC")
    List<Task> findTaskSummaryByScheduledDate(LocalDate date);

    @Query("SELECT t FROM Task t WHERE t.scheduledDate <= ?1 ORDER BY " +
           "CASE WHEN t.status = 'IN_PROGRESS' THEN 1 " +
           "WHEN t.status = 'NOT_STARTED' THEN 2 " +
           "WHEN t.status = 'PENDING' THEN 3 " +
           "WHEN t.status = 'COMPLETED' THEN 4 " +
           "WHEN t.status = 'OVERDUE' THEN 5 END")
    List<Task> findTasksByScheduledDateLessThanEqualOrderByStatusPriority(LocalDate date);

    List<Task> findByScheduledDateLessThanEqual(LocalDate date);
    
    List<Task> findByStatus(TaskStatus status);

    List<Task> findByStatusAndScheduledDateBetween(TaskStatus status, LocalDate startDate, LocalDate endDate);

    List<Task> findByScheduledDateBetween(LocalDate startDate, LocalDate endDate);

    List<Task> findByStatusAndDueDateBefore(TaskStatus status, LocalDate date);

    List<Task> findByDueDateAndStatusIn(LocalDate dueDate, Collection<TaskStatus> statuses);

    @Query("SELECT t FROM Task t WHERE t.scheduledDate < ?1 AND t.scheduledDate >= ?2 ORDER BY t.scheduledDate DESC, " +
           "CASE WHEN t.status = 'IN_PROGRESS' THEN 1 " +
           "WHEN t.status = 'NOT_STARTED' THEN 2 " +
           "WHEN t.status = 'PENDING' THEN 3 " +
           "WHEN t.status = 'COMPLETED' THEN 4 " +
           "WHEN t.status = 'OVERDUE' THEN 5 END")
    List<Task> findPastTasksInDateRange(LocalDate beforeDate, LocalDate afterDate);

    /**
     * 检查指定模板在指定日期是否已经生成过任务
     */
    boolean existsBySourceTemplateIdAndScheduledDate(Long sourceTemplateId, LocalDate scheduledDate);
    

}