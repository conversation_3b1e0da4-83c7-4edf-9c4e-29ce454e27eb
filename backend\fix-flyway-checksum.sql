-- 修复Flyway校验和不匹配问题
-- 使用前请确保连接到正确的数据库：cps

USE cps;

-- 查看当前的flyway schema history状态
SELECT version, checksum, success FROM flyway_schema_history ORDER BY installed_rank;

-- 修复V3迁移的校验和不匹配问题
-- 将数据库中记录的校验和(-1606005600)更新为本地文件的校验和(1878183307)
UPDATE flyway_schema_history 
SET checksum = 1878183307 
WHERE version = '3' AND checksum = -1606005600;

-- 验证修复结果
SELECT version, checksum, success FROM flyway_schema_history WHERE version = '3';

-- 如果需要，也可以查看V3迁移对应的表结构是否正确
DESCRIBE tasks;
