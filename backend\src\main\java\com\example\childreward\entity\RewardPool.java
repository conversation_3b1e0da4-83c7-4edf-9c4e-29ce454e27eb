package com.example.childreward.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "reward_pools")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RewardPool {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 50)
    private String name;

    @Column(name = "cost_points", nullable = false)
    private Integer costPoints;

    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;

    @Enumerated(EnumType.STRING)
    @Column(name = "pool_type", nullable = false)
    private PoolType poolType = PoolType.REWARD;

    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @OneToMany(mappedBy = "rewardPool", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties("rewardPool")
    private List<RewardItem> rewardItems;

    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
    }

    public enum PoolType {
        REWARD,  // 奖励池
        PENALTY  // 惩罚池
    }
} 