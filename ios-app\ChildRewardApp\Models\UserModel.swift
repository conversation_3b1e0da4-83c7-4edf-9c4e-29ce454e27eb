import Foundation

// MARK: - 用户模型
struct UserModel: Codable, Identifiable {
    let id: Int
    let username: String
    let nickname: String?
    let avatar: String?
    let totalPoints: Int
    let availablePoints: Int
    let level: Int
    let experience: Int
    let createdDate: String
    let lastLoginDate: String?
    
    var displayName: String {
        return nickname ?? username
    }
    
    var levelProgress: Double {
        let currentLevelExp = level * 100
        let nextLevelExp = (level + 1) * 100
        let progressExp = experience - currentLevelExp
        let levelRange = nextLevelExp - currentLevelExp
        return Double(progressExp) / Double(levelRange)
    }
    
    var nextLevelPoints: Int {
        return (level + 1) * 100 - experience
    }
}

// MARK: - 用户统计
struct UserStats: Codable {
    let totalTasksCompleted: Int
    let totalPointsEarned: Int
    let totalPointsSpent: Int
    let currentStreak: Int
    let longestStreak: Int
    let averageTasksPerDay: Double
    let favoriteTaskCategory: String?
    let totalRewardsExchanged: Int
    
    var completionRate: Double {
        // 这里可以根据实际需求计算完成率
        return 0.85 // 示例值
    }
}

// MARK: - 每日统计
struct DailyStats: Codable, Identifiable {
    let id: String // 日期字符串作为ID
    let date: String
    let tasksCompleted: Int
    let pointsEarned: Int
    let tasksTotal: Int
    
    var completionRate: Double {
        guard tasksTotal > 0 else { return 0 }
        return Double(tasksCompleted) / Double(tasksTotal)
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        if let date = formatter.date(from: date) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return date
    }
}

// MARK: - 周统计
struct WeeklyStats: Codable {
    let weekStart: String
    let weekEnd: String
    let totalTasks: Int
    let completedTasks: Int
    let totalPoints: Int
    let dailyStats: [DailyStats]
    
    var completionRate: Double {
        guard totalTasks > 0 else { return 0 }
        return Double(completedTasks) / Double(totalTasks)
    }
    
    var averagePointsPerDay: Double {
        guard !dailyStats.isEmpty else { return 0 }
        let totalPoints = dailyStats.reduce(0) { $0 + $1.pointsEarned }
        return Double(totalPoints) / Double(dailyStats.count)
    }
}

// MARK: - 成就系统
struct Achievement: Codable, Identifiable {
    let id: Int
    let name: String
    let description: String
    let iconName: String
    let pointsReward: Int
    let isUnlocked: Bool
    let unlockedDate: String?
    let category: AchievementCategory
    let requirement: AchievementRequirement
}

enum AchievementCategory: String, CaseIterable, Codable {
    case tasks = "TASKS"
    case points = "POINTS"
    case streak = "STREAK"
    case special = "SPECIAL"
    
    var displayName: String {
        switch self {
        case .tasks: return "任务达人"
        case .points: return "积分大师"
        case .streak: return "坚持之星"
        case .special: return "特殊成就"
        }
    }
}

struct AchievementRequirement: Codable {
    let type: String // "TASKS_COUNT", "POINTS_TOTAL", "STREAK_DAYS"
    let target: Int
    let current: Int
    
    var progress: Double {
        guard target > 0 else { return 0 }
        return min(Double(current) / Double(target), 1.0)
    }
    
    var isCompleted: Bool {
        return current >= target
    }
}


