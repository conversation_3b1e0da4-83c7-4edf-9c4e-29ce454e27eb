import Foundation
import SwiftUI

// MARK: - 奖励项目
struct RewardItem: Identifiable, Codable {
    let id: Int
    let name: String
    let description: String
    let pointsCost: Int
    let iconName: String
    let colorHex: String
    let category: RewardCategory
    let isAvailable: Bool
    let stock: Int?
    
    var color: Color {
        Color(hex: colorHex) ?? .blue
    }
}

// MARK: - 奖励分类
enum RewardCategory: String, CaseIterable, Codable {
    case entertainment = "ENTERTAINMENT"
    case food = "FOOD"
    case toy = "TOY"
    case privilege = "PRIVILEGE"
    case experience = "EXPERIENCE"
    
    var displayName: String {
        switch self {
        case .entertainment: return "娱乐"
        case .food: return "美食"
        case .toy: return "玩具"
        case .privilege: return "特权"
        case .experience: return "体验"
        }
    }
    
    var iconName: String {
        switch self {
        case .entertainment: return "gamecontroller"
        case .food: return "fork.knife"
        case .toy: return "teddybear"
        case .privilege: return "crown"
        case .experience: return "star"
        }
    }
}

// MARK: - 兑换记录
struct RedeemRecord: Identifiable, Codable {
    let id: Int
    let reward: RewardItem
    let redeemedAt: String
    let status: RedeemStatus
    let notes: String?
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        if let date = formatter.date(from: redeemedAt) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日 HH:mm"
            displayFormatter.locale = Locale(identifier: "zh_CN")
            return displayFormatter.string(from: date)
        }
        return redeemedAt
    }
}

// MARK: - 兑换状态
enum RedeemStatus: String, CaseIterable, Codable {
    case pending = "PENDING"
    case approved = "APPROVED"
    case delivered = "DELIVERED"
    case cancelled = "CANCELLED"
    
    var displayName: String {
        switch self {
        case .pending: return "待处理"
        case .approved: return "已批准"
        case .delivered: return "已发放"
        case .cancelled: return "已取消"
        }
    }
    
    var color: Color {
        switch self {
        case .pending: return .orange
        case .approved: return .blue
        case .delivered: return .green
        case .cancelled: return .red
        }
    }
}

// MARK: - 积分统计
struct PointsStatistics: Codable {
    let totalPoints: Int
    let todayEarned: Int
    let weeklyEarned: Int
    let monthlyEarned: Int
    let totalEarned: Int
    let totalSpent: Int
    
    var availablePoints: Int {
        return totalEarned - totalSpent
    }
}

// MARK: - 颜色扩展
extension Color {
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}


