# Apache配置文件，用于支持React Router
# 将此文件重命名为 .htaccess 并放在 crs 目录下

Options -MultiViews
RewriteEngine On

# 处理React Router的客户端路由
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /crs/index.html [L]

# 设置缓存策略
<IfModule mod_expires.c>
    ExpiresActive on
    
    # 静态资源缓存1年
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    
    # HTML文件不缓存
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# 启用gzip压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>