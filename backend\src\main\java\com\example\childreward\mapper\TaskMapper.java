package com.example.childreward.mapper;

import com.example.childreward.dto.TaskSummaryDto;
import com.example.childreward.entity.Task;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务映射器 - 用于Task实体和DTO之间的转换
 * 优化性能：列表查询时不包含description字段（图片base64数据）
 */
@Component
public class TaskMapper {
    
    /**
     * 将Task实体转换为TaskSummaryDto
     * 不包含description字段，用于列表查询优化
     */
    public TaskSummaryDto toSummaryDto(Task task) {
        if (task == null) {
            return null;
        }
        
        return TaskSummaryDto.builder()
                .id(task.getId())
                .sourceTemplateId(task.getSourceTemplateId())
                .title(task.getTitle())
                .expectedMinutes(task.getExpectedMinutes())
                .basePoints(task.getBasePoints())
                .dueTime(task.getDueTime())
                .dueDate(task.getDueDate())
                .status(task.getStatus())
                .taskType(task.getTaskType())
                .startTime(task.getStartTime())
                .endTime(task.getEndTime())
                .actualPoints(task.getActualPoints())
                .createdTime(task.getCreatedTime())
                .scheduledDate(task.getScheduledDate())
                .build();
    }
    
    /**
     * 批量转换Task列表为TaskSummaryDto列表
     */
    public List<TaskSummaryDto> toSummaryDtoList(List<Task> tasks) {
        if (tasks == null) {
            return null;
        }
        
        return tasks.stream()
                .map(this::toSummaryDto)
                .collect(Collectors.toList());
    }
}