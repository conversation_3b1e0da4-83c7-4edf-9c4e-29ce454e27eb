import Foundation

// MARK: - 网络错误枚举
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case decodingError
    case networkError(Error)
    case serverError(Int, String?)
    case noData
    case timeout
    case unauthorized
    case forbidden
    case notFound
    case tooManyRequests
    case serverUnavailable
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL地址"
        case .invalidResponse:
            return "服务器响应无效"
        case .decodingError:
            return "数据解析失败"
        case .networkError(let error):
            return "网络连接失败: \(error.localizedDescription)"
        case .serverError(let code, let message):
            return "服务器错误 (\(code)): \(message ?? "未知错误")"
        case .noData:
            return "没有接收到数据"
        case .timeout:
            return "请求超时"
        case .unauthorized:
            return "未授权访问"
        case .forbidden:
            return "访问被禁止"
        case .notFound:
            return "请求的资源不存在"
        case .tooManyRequests:
            return "请求过于频繁，请稍后再试"
        case .serverUnavailable:
            return "服务器暂时不可用"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .invalidURL:
            return "请检查应用配置"
        case .invalidResponse, .decodingError:
            return "请稍后重试或联系技术支持"
        case .networkError:
            return "请检查网络连接后重试"
        case .serverError:
            return "请稍后重试，如果问题持续存在请联系技术支持"
        case .noData:
            return "请刷新页面重试"
        case .timeout:
            return "请检查网络连接并重试"
        case .unauthorized:
            return "请重新登录"
        case .forbidden:
            return "您没有权限执行此操作"
        case .notFound:
            return "请求的内容可能已被删除或移动"
        case .tooManyRequests:
            return "请等待一段时间后再试"
        case .serverUnavailable:
            return "服务器正在维护，请稍后再试"
        }
    }
    
    var isRetryable: Bool {
        switch self {
        case .networkError, .timeout, .serverError, .serverUnavailable, .tooManyRequests:
            return true
        case .invalidURL, .decodingError, .unauthorized, .forbidden, .notFound:
            return false
        case .invalidResponse, .noData:
            return true
        }
    }
    
    static func from(httpStatusCode: Int, data: Data? = nil) -> NetworkError {
        var message: String?
        
        // 尝试解析错误消息
        if let data = data,
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
           let errorMessage = json["message"] as? String {
            message = errorMessage
        }
        
        switch httpStatusCode {
        case 401:
            return .unauthorized
        case 403:
            return .forbidden
        case 404:
            return .notFound
        case 429:
            return .tooManyRequests
        case 500...599:
            return .serverUnavailable
        default:
            return .serverError(httpStatusCode, message)
        }
    }
}

// MARK: - API响应模型
struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let message: String?
    let code: Int?
    
    var isSuccess: Bool {
        return success && data != nil
    }
}

// MARK: - 分页响应模型
struct PaginatedResponse<T: Codable>: Codable {
    let items: [T]
    let totalCount: Int
    let pageSize: Int
    let currentPage: Int
    let totalPages: Int
    let hasNext: Bool
    let hasPrevious: Bool
}

// MARK: - 错误处理扩展
extension NetworkError {
    /// 从URLSession错误转换为NetworkError
    static func from(urlError: URLError) -> NetworkError {
        switch urlError.code {
        case .timedOut:
            return .timeout
        case .notConnectedToInternet, .networkConnectionLost:
            return .networkError(urlError)
        case .badURL:
            return .invalidURL
        case .badServerResponse:
            return .invalidResponse
        default:
            return .networkError(urlError)
        }
    }
    
    /// 从通用Error转换为NetworkError
    static func from(error: Error) -> NetworkError {
        if let urlError = error as? URLError {
            return from(urlError: urlError)
        } else if let networkError = error as? NetworkError {
            return networkError
        } else {
            return .networkError(error)
        }
    }
}
