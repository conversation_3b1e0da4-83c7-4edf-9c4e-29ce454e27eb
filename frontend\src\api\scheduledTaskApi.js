import { createParentApiClient } from './apiConfig.js';

// 使用家长端API客户端配置（计划任务属于家长端功能）
const getScheduledTaskClient = () => {
    const client = createParentApiClient();
    // 为计划任务API添加特定的baseURL后缀
    const originalBaseURL = client.defaults.baseURL;
    client.defaults.baseURL = originalBaseURL.replace('/api', '/api/scheduled-tasks');
    return client;
};

export const scheduledTaskApi = {
    /**
     * 获取所有计划任务模板
     */
    getAll: () => getScheduledTaskClient().get('/'),

    /**
     * 根据ID获取单个计划任务模板
     * @param {number} id 模板ID
     */
    getById: (id) => getScheduledTaskClient().get(`/${id}`),

    /**
     * 创建一个新的计划任务模板
     * @param {object} taskData 任务模板数据
     */
    create: (taskData) => getScheduledTaskClient().post('/', taskData),

    /**
     * 更新一个已有的计划任务模板
     * @param {number} id 模板ID
     * @param {object} taskData 更新后的任务模板数据
     */
    update: (id, taskData) => getScheduledTaskClient().put(`/${id}`, taskData),

    /**
     * 删除一个计划任务模板
     * @param {number} id 模板ID
     */
    delete: (id) => getScheduledTaskClient().delete(`/${id}`),

    /**
     * 手动触发任务生成
     * 根据当前活跃的计划任务模板生成今日任务
     */
    generateTasks: () => getScheduledTaskClient().get('/generate-tasks'),
};