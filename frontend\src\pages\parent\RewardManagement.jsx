import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { parentTheme } from '../../utils/themes';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { rewardApi } from '../../api/apiService';

const RewardManagement = () => {
  const [rewardPools, setRewardPools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activePool, setActivePool] = useState(null);
  const [showPoolModal, setShowPoolModal] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [editingPool, setEditingPool] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [itemPoolId, setItemPoolId] = useState(null);
  const [formValues, setFormValues] = useState({
    name: '',
    costPoints: 10,
    active: true,
    poolType: 'REWARD'
  });
  const [itemFormValues, setItemFormValues] = useState({
    name: '',
    probability: 0.2,
    stock: 10,
    image: '🎁'
  });

  useEffect(() => {
    // 从API获取奖品池列表
    const fetchRewardPools = async () => {
      setLoading(true);
      try {
        // 获取奖品池列表
        const poolsResponse = await rewardApi.getRewardPools();
        
        // 为每个奖品池加载相关的奖品项
        const poolsWithItems = await Promise.all(
          poolsResponse.data.map(async (pool) => {
            try {
              // 获取奖品池详情，包括奖品项
              const detailResponse = await rewardApi.getRewardPool(pool.id);
              const poolDetail = detailResponse.data;
              
              // 格式化奖品项数据
              const formattedItems = poolDetail.rewardItems ? poolDetail.rewardItems.map(item => ({
                id: item.id,
                name: item.name,
                probability: item.probability,
                stock: item.stock,
                image: item.imageUrl || '🎁' // 如果没有图片URL，使用默认emoji
              })) : [];
              
              return {
                id: pool.id,
                name: pool.name,
                costPoints: pool.costPoints,
                active: pool.isEnabled,
                items: formattedItems
              };
            } catch (error) {
              console.error(`获取奖品池 ${pool.id} 详情失败:`, error);
              return {
                id: pool.id,
                name: pool.name,
                costPoints: pool.costPoints,
                active: pool.isEnabled,
                items: []
              };
            }
          })
        );
        
        setRewardPools(poolsWithItems);
        
        if (poolsWithItems.length > 0) {
          setActivePool(poolsWithItems[0].id);
        }
      } catch (error) {
        console.error('获取奖品池列表失败:', error);
        setRewardPools([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRewardPools();
  }, []);

  const handleAddPool = () => {
    setEditingPool(null);
    setFormValues({
      name: '',
      costPoints: 10,
      active: true,
      poolType: 'REWARD'
    });
    setShowPoolModal(true);
  };

  const handleEditPool = (pool) => {
    setEditingPool(pool);
    setFormValues({
      name: pool.name,
      costPoints: pool.costPoints,
      active: pool.active,
      poolType: pool.poolType
    });
    setShowPoolModal(true);
  };

  const handleDeletePool = async (poolId) => {
    if (window.confirm('确定要删除这个奖品池吗？所有关联的奖品项也将被删除。')) {
      try {
        await rewardApi.deleteRewardPool(poolId);
        
        setRewardPools(prevPools => prevPools.filter(pool => pool.id !== poolId));
        if (activePool === poolId) {
          setActivePool(rewardPools.find(p => p.id !== poolId)?.id || null);
        }
      } catch (error) {
        console.error('删除奖品池失败:', error);
        alert('删除奖品池失败，请稍后再试');
      }
    }
  };

  const handlePoolFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormValues({
      ...formValues,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handlePoolSubmit = async (e) => {
    e.preventDefault();

    const poolData = {
      name: formValues.name,
      costPoints: parseInt(formValues.costPoints),
      isEnabled: formValues.active,
      poolType: formValues.poolType
    };

    try {
      if (editingPool) {
        // 更新奖品池
        const response = await rewardApi.updateRewardPool(editingPool.id, poolData);
        
        // 更新本地状态
        setRewardPools(prevPools =>
          prevPools.map(pool =>
            pool.id === editingPool.id ? {
              ...pool,
              name: formValues.name,
              costPoints: parseInt(formValues.costPoints),
              active: formValues.active,
              poolType: formValues.poolType
            } : pool
          )
        );
      } else {
        // 创建新奖品池
        const response = await rewardApi.createRewardPool(poolData);
        const newPool = {
          id: response.data.id,
          name: formValues.name,
          costPoints: parseInt(formValues.costPoints),
          active: formValues.active,
          poolType: formValues.poolType,
          items: []
        };
        
        setRewardPools(prevPools => [...prevPools, newPool]);
        
        // 如果是第一个奖品池，将其设为活动池
        if (rewardPools.length === 0) {
          setActivePool(newPool.id);
        }
      }
      
      setShowPoolModal(false);
    } catch (error) {
      console.error('保存奖品池失败:', error);
      alert('保存奖品池失败，请稍后再试');
    }
  };

  const handleAddItem = (poolId) => {
    setEditingItem(null);
    setItemPoolId(poolId);
    setItemFormValues({
      name: '',
      probability: 0.2,
      stock: 10,
      image: '🎁'
    });
    setShowItemModal(true);
  };

  const handleEditItem = (item, poolId) => {
    setEditingItem(item);
    setItemPoolId(poolId);
    setItemFormValues({
      name: item.name,
      probability: item.probability,
      stock: item.stock,
      image: item.image || '🎁'
    });
    setShowItemModal(true);
  };

  const handleDeleteItem = async (poolId, itemId) => {
    if (window.confirm('确定要删除这个奖品吗？')) {
      try {
        await rewardApi.deleteRewardItem(itemId);
        
        // 更新本地状态
        setRewardPools(prevPools => 
          prevPools.map(pool => 
            pool.id === poolId
              ? { ...pool, items: pool.items.filter(item => item.id !== itemId) }
              : pool
          )
        );
      } catch (error) {
        console.error('删除奖品失败:', error);
        alert('删除奖品失败，请稍后再试');
      }
    }
  };

  const handleItemFormChange = (e) => {
    const { name, value } = e.target;
    setItemFormValues({
      ...itemFormValues,
      [name]: name === 'probability' || name === 'stock' ? parseFloat(value) : value
    });
  };

  const handleItemSubmit = async (e) => {
    e.preventDefault();

    // 准备奖品数据
    const itemData = {
      name: itemFormValues.name,
      probability: parseFloat(itemFormValues.probability),
      stock: parseInt(itemFormValues.stock),
      imageUrl: itemFormValues.image
    };
    
    try {
      if (editingItem) {
        // 更新奖品
        const response = await rewardApi.updateRewardItem(editingItem.id, itemData);
        
        // 更新本地状态
        setRewardPools(prevPools =>
          prevPools.map(pool =>
            pool.id === itemPoolId ? {
              ...pool,
              items: pool.items.map(item =>
                item.id === editingItem.id ? {
                  ...item,
                  name: itemFormValues.name,
                  probability: parseFloat(itemFormValues.probability),
                  stock: parseInt(itemFormValues.stock),
                  image: itemFormValues.image
                } : item
              )
            } : pool
          )
        );
      } else {
        // 添加新奖品
        const response = await rewardApi.addRewardItem(itemPoolId, itemData);
        
        // 更新本地状态
        const newItem = {
          id: response.data.id,
          name: itemFormValues.name,
          probability: parseFloat(itemFormValues.probability),
          stock: parseInt(itemFormValues.stock),
          image: itemFormValues.image
        };
        
        setRewardPools(prevPools =>
          prevPools.map(pool =>
            pool.id === itemPoolId ? {
              ...pool,
              items: [...pool.items, newItem]
            } : pool
          )
        );
      }
      
      setShowItemModal(false);
    } catch (error) {
      console.error('保存奖品失败:', error);
      alert('保存奖品失败，请稍后再试');
    }
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    const poolId = parseInt(result.source.droppableId);
    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;
    
    if (sourceIndex === destinationIndex) return;
    
    setRewardPools(prevPools =>
      prevPools.map(pool => {
        if (pool.id === poolId) {
          const newItems = [...pool.items];
          const [removed] = newItems.splice(sourceIndex, 1);
          newItems.splice(destinationIndex, 0, removed);
          return { ...pool, items: newItems };
        }
        return pool;
      })
    );
  };

  const getActivePoolItems = () => {
    const pool = rewardPools.find(p => p.id === activePool);
    return pool ? pool.items : [];
  };

  if (loading) {
    return <LoadingMessage>加载中...</LoadingMessage>;
  }

  return (
    <RewardManagementContainer>
      <Header>
        <Title>奖品池管理</Title>
        <AddButton 
          onClick={handleAddPool}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <PlusIcon>+</PlusIcon> 新增奖品池
        </AddButton>
      </Header>

      <Content>
        <PoolsNav>
          {rewardPools.map(pool => (
            <PoolTab
              key={pool.id}
              isActive={activePool === pool.id}
              onClick={() => setActivePool(pool.id)}
            >
              <PoolTabContent>
                <PoolIcon>{pool.active ? '🎮' : '🔒'}</PoolIcon>
                <PoolName>{pool.name}</PoolName>
                <PoolCost>{pool.costPoints} 积分</PoolCost>
              </PoolTabContent>
              <PoolActions>
                <PoolActionButton
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditPool(pool);
                  }}
                >
                  ✏️
                </PoolActionButton>
                <PoolActionButton
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeletePool(pool.id);
                  }}
                >
                  🗑️
                </PoolActionButton>
              </PoolActions>
            </PoolTab>
          ))}
        </PoolsNav>

        <PoolDetails>
          {activePool && (
            <>
              <PoolDetailsHeader>
                <PoolTitle>
                  {rewardPools.find(p => p.id === activePool)?.name || '奖品项'}
                </PoolTitle>
                <AddItemButton
                  onClick={() => handleAddItem(activePool)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  添加奖品
                </AddItemButton>
              </PoolDetailsHeader>

              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId={String(activePool)}>
                  {(provided) => (
                    <ItemsList
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                    >
                      {getActivePoolItems().map((item, index) => (
                        <Draggable
                          key={item.id}
                          draggableId={`item-${item.id}`}
                          index={index}
                        >
                          {(provided) => (
                            <ItemCard
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <ItemImage>{item.image}</ItemImage>
                              <ItemContent>
                                <ItemName>{item.name}</ItemName>
                                <ItemDetails>
                                  <ItemStat>
                                    <StatLabel>概率:</StatLabel>
                                    <StatValue>{Math.round(item.probability * 100)}%</StatValue>
                                  </ItemStat>
                                  <ItemStat>
                                    <StatLabel>库存:</StatLabel>
                                    <StatValue>{item.stock}个</StatValue>
                                  </ItemStat>
                                </ItemDetails>
                              </ItemContent>
                              <ItemActions>
                                <ItemActionButton
                                  onClick={() => handleEditItem(item, activePool)}
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                >
                                  ✏️
                                </ItemActionButton>
                                <ItemActionButton
                                  onClick={() => handleDeleteItem(activePool, item.id)}
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                >
                                  🗑️
                                </ItemActionButton>
                              </ItemActions>
                            </ItemCard>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </ItemsList>
                  )}
                </Droppable>
              </DragDropContext>

              {getActivePoolItems().length === 0 && (
                <EmptyMessage>
                  <EmptyIcon>🏆</EmptyIcon>
                  <EmptyText>请添加奖品项来丰富这个奖品池</EmptyText>
                </EmptyMessage>
              )}
            </>
          )}

          {!activePool && (
            <EmptyMessage>
              <EmptyIcon>🏆</EmptyIcon>
              <EmptyText>请创建一个奖品池</EmptyText>
            </EmptyMessage>
          )}
        </PoolDetails>
      </Content>

      {/* 奖品池表单模态框 */}
      <AnimatePresence>
        {showPoolModal && (
          <ModalOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <ModalContainer
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
            >
              <ModalHeader>
                <ModalTitle>{editingPool ? '编辑奖品池' : '新增奖品池'}</ModalTitle>
                <CloseButton onClick={() => setShowPoolModal(false)}>×</CloseButton>
              </ModalHeader>

              <ModalBody>
                <PoolForm onSubmit={handlePoolSubmit}>
                  <FormGroup>
                    <Label htmlFor="name">奖品池名称</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formValues.name}
                      onChange={handlePoolFormChange}
                      required
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label htmlFor="costPoints">消耗积分</Label>
                    <Input
                      id="costPoints"
                      name="costPoints"
                      type="number"
                      min="1"
                      max="1000"
                      value={formValues.costPoints}
                      onChange={handlePoolFormChange}
                      required
                    />
                  </FormGroup>

                  <FormGroup>
                    <CheckboxContainer>
                      <CheckboxInput
                        id="active"
                        name="active"
                        type="checkbox"
                        checked={formValues.active}
                        onChange={handlePoolFormChange}
                      />
                      <CheckboxLabel htmlFor="active">启用奖品池</CheckboxLabel>
                    </CheckboxContainer>
                  </FormGroup>

                  <FormGroup>
                    <Label htmlFor="poolType">奖品池类型</Label>
                    <Select
                      id="poolType"
                      name="poolType"
                      value={formValues.poolType}
                      onChange={handlePoolFormChange}
                      required
                    >
                      <option value="REWARD">奖励池</option>
                      <option value="PENALTY">惩罚池</option>
                    </Select>
                  </FormGroup>

                  <ButtonGroup>
                    <CancelButton
                      type="button"
                      onClick={() => setShowPoolModal(false)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      取消
                    </CancelButton>
                    <SubmitButton
                      type="submit"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {editingPool ? '更新' : '创建'}
                    </SubmitButton>
                  </ButtonGroup>
                </PoolForm>
              </ModalBody>
            </ModalContainer>
          </ModalOverlay>
        )}
      </AnimatePresence>

      {/* 奖品项表单模态框 */}
      <AnimatePresence>
        {showItemModal && (
          <ModalOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <ModalContainer
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
            >
              <ModalHeader>
                <ModalTitle>{editingItem ? '编辑奖品' : '添加奖品'}</ModalTitle>
                <CloseButton onClick={() => setShowItemModal(false)}>×</CloseButton>
              </ModalHeader>

              <ModalBody>
                <ItemForm onSubmit={handleItemSubmit}>
                  <FormRow>
                    <FormGroup>
                      <Label htmlFor="name">奖品名称</Label>
                      <Input
                        id="name"
                        name="name"
                        value={itemFormValues.name}
                        onChange={handleItemFormChange}
                        required
                      />
                    </FormGroup>
                    <FormGroup>
                      <Label htmlFor="image">图标</Label>
                      <EmojiSelector>
                        <EmojiInput
                          id="image"
                          name="image"
                          value={itemFormValues.image}
                          onChange={handleItemFormChange}
                          required
                          maxLength={2}
                        />
                        <EmojiDisplay>{itemFormValues.image}</EmojiDisplay>
                      </EmojiSelector>
                    </FormGroup>
                  </FormRow>

                  <FormRow>
                    <FormGroup>
                      <Label htmlFor="probability">中奖概率(0-1)</Label>
                      <Input
                        id="probability"
                        name="probability"
                        type="number"
                        step="0.01"
                        min="0"
                        max="1"
                        value={itemFormValues.probability}
                        onChange={handleItemFormChange}
                        required
                      />
                      <SmallNote>概率总和应为1.0</SmallNote>
                    </FormGroup>
                    <FormGroup>
                      <Label htmlFor="stock">库存数量</Label>
                      <Input
                        id="stock"
                        name="stock"
                        type="number"
                        min="0"
                        max="1000"
                        value={itemFormValues.stock}
                        onChange={handleItemFormChange}
                        required
                      />
                    </FormGroup>
                  </FormRow>

                  <ButtonGroup>
                    <CancelButton
                      type="button"
                      onClick={() => setShowItemModal(false)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      取消
                    </CancelButton>
                    <SubmitButton
                      type="submit"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {editingItem ? '更新' : '添加'}
                    </SubmitButton>
                  </ButtonGroup>
                </ItemForm>
              </ModalBody>
            </ModalContainer>
          </ModalOverlay>
        )}
      </AnimatePresence>
    </RewardManagementContainer>
  );
};

// 样式组件
const RewardManagementContainer = styled.div`
  padding: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: ${parentTheme.textColor};
  margin: 0;
`;

const AddButton = styled(motion.button)`
  display: flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-weight: 600;
  cursor: pointer;
`;

const PlusIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
  line-height: 1;
`;

const Content = styled.div`
  display: grid;
  grid-template-columns: 240px 1fr;
  gap: 1.5rem;
  height: calc(100vh - 180px);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
`;

const PoolsNav = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const PoolTab = styled.div`
  padding: 1rem;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  background-color: ${props => props.isActive ? parentTheme.lightGray : 'white'};
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:hover {
    background-color: ${props => props.isActive ? parentTheme.lightGray : '#f9f9f9'};
  }
`;

const PoolTabContent = styled.div`
  display: flex;
  flex-direction: column;
`;

const PoolIcon = styled.div`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const PoolName = styled.div`
  font-weight: 600;
  color: ${parentTheme.textColor};
`;

const PoolCost = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
`;

const PoolActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  opacity: 0.6;
  
  &:hover {
    opacity: 1;
  }
`;

const PoolActionButton = styled.button`
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
  }
`;

const PoolDetails = styled.div`
  background: white;
  border-radius: ${parentTheme.borderRadius};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const PoolDetailsHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const PoolTitle = styled.h2`
  margin: 0;
  font-size: 1.2rem;
  color: ${parentTheme.textColor};
`;

const AddItemButton = styled(motion.button)`
  padding: 0.5rem 1rem;
  background: ${parentTheme.gradients.primary};
  color: white;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
`;

const ItemsList = styled.div`
  padding: 1rem;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  flex: 1;
`;

const ItemCard = styled.div`
  background: #f9f9f9;
  border-radius: ${parentTheme.borderRadius};
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  cursor: grab;
  
  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }
  
  &:active {
    cursor: grabbing;
  }
`;

const ItemImage = styled.div`
  font-size: 2rem;
  text-align: center;
  margin-bottom: 0.5rem;
`;

const ItemContent = styled.div`
  flex: 1;
`;

const ItemName = styled.div`
  font-weight: 600;
  color: ${parentTheme.textColor};
  margin-bottom: 0.5rem;
`;

const ItemDetails = styled.div`
  display: flex;
  gap: 1rem;
`;

const ItemStat = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatLabel = styled.div`
  font-size: 0.7rem;
  color: #999;
`;

const StatValue = styled.div`
  font-size: 0.9rem;
  color: ${parentTheme.textColor};
`;

const ItemActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
  opacity: 0.7;
  
  &:hover {
    opacity: a;
  }
`;

const ItemActionButton = styled(motion.button)`
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
  }
`;

const EmptyMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  flex: 1;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
`;

const EmptyText = styled.div`
  color: #999;
  font-size: 1.1rem;
`;

const LoadingMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: ${parentTheme.textColor};
`;

// 模态框样式
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled(motion.div)`
  width: 90%;
  max-width: 600px;
  background-color: white;
  border-radius: ${parentTheme.borderRadius};
  overflow: hidden;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.25rem;
  color: ${parentTheme.textColor};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  
  &:hover {
    color: #666;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const PoolForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ItemForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
`;

const Label = styled.label`
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
`;

const Input = styled.input`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const EmojiSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const EmojiInput = styled.input`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  flex: 1;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

const EmojiDisplay = styled.div`
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: ${parentTheme.borderRadius};
`;

const SmallNote = styled.div`
  font-size: 0.7rem;
  color: #999;
  margin-top: 0.25rem;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
`;

const CheckboxInput = styled.input`
  margin-right: 0.5rem;
  width: 1.2rem;
  height: 1.2rem;
`;

const CheckboxLabel = styled.label`
  font-size: 1rem;
  color: ${parentTheme.textColor};
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
`;

const BaseButton = styled(motion.button)`
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
`;

const CancelButton = styled(BaseButton)`
  background-color: #f5f5f5;
  color: #666;
  
  &:hover {
    background-color: #eee;
  }
`;

const SubmitButton = styled(BaseButton)`
  background: ${parentTheme.gradients.primary};
  color: white;
`;

const Select = styled.select`
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: ${parentTheme.borderRadius};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${parentTheme.primaryColor};
  }
`;

export default RewardManagement; 