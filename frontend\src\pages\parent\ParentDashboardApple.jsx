/**
 * 苹果风格家长端主页
 * 完全重新设计，基于苹果设计标准
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import { pointApi, taskApi, rewardApi } from '../../api/apiService';
import {
  ParentContainer,
  ParentHeader,
  ParentTitle,
  ParentSubtitle,
  ParentGrid,
  ParentCard,
  ParentCardTitle,
  ParentCardContent,
  ParentButton,
  ParentActionGroup,
  ParentStatusBadge,
  ParentStatCard
} from '../../components/parent/ParentAppleUI';
import { ApplePageTransition } from '../../components/apple/AppleAnimations';

const ParentDashboardApple = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalPoints: 0,
    todayTasks: 0,
    pendingApprovals: 0,
    completedTasks: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // 模拟数据
      setStats({
        totalPoints: 150,
        todayTasks: 3,
        pendingApprovals: 1,
        completedTasks: 12
      });

      setRecentActivities([
        { id: 1, type: 'task_completed', description: '完成数学作业', points: 10, time: '2小时前' },
        { id: 2, type: 'reward_claimed', description: '兑换玩具奖励', points: -50, time: '4小时前' },
        { id: 3, type: 'bonus_earned', description: '额外奖励积分', points: 20, time: '昨天' }
      ]);
      
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: '任务管理',
      description: '创建和管理孩子的任务',
      icon: '📝',
      color: 'primary',
      path: '/parent',
      badge: stats.todayTasks
    },
    {
      title: '审批中心',
      description: '处理待审批的任务',
      icon: '✅',
      color: 'warning',
      path: '/parent',
      badge: stats.pendingApprovals
    },
    {
      title: '积分管理',
      description: '查看和管理积分记录',
      icon: '💰',
      color: 'success',
      path: '/parent'
    },
    {
      title: '奖励商店',
      description: '设置和管理奖励项目',
      icon: '🎁',
      color: 'secondary',
      path: '/parent'
    },
    {
      title: '计划管理',
      description: '设置定时任务模板',
      icon: '⏰',
      color: 'info',
      path: '/parent'
    },
    {
      title: '设置中心',
      description: '系统设置和配置',
      icon: '⚙️',
      color: 'neutral',
      path: '/parent'
    }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'task_completed': return '✅';
      case 'reward_claimed': return '🎁';
      case 'bonus_earned': return '⭐';
      default: return '📝';
    }
  };

  if (loading) {
    return (
      <ParentContainer>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          fontSize: AppleDesignSystem.typography.textStyles.headline.fontSize,
          color: AppleDesignSystem.colors.semantic.secondaryLabel
        }}>
          正在加载...
        </div>
      </ParentContainer>
    );
  }

  return (
    <ApplePageTransition>
      <ParentContainer>
        {/* 头部 */}
        <ParentHeader>
          <div>
            <ParentTitle>家长控制台</ParentTitle>
            <ParentSubtitle>管理孩子的任务和奖励系统</ParentSubtitle>
          </div>
          <ParentActionGroup>
            <ParentButton 
              variant="filled"
              color="primary"
              onClick={() => alert('新建任务功能开发中...')}
            >
              ➕ 新建任务
            </ParentButton>
            <ParentButton 
              variant="outline"
              color="primary"
              onClick={() => navigate('/child')}
            >
              👶 儿童端
            </ParentButton>
          </ParentActionGroup>
        </ParentHeader>

        {/* 统计卡片 */}
        <ParentGrid>
          <ParentStatCard
            title="总积分"
            value={stats.totalPoints}
            icon="💰"
            color="primary"
            trend="+12"
          />
          <ParentStatCard
            title="今日任务"
            value={stats.todayTasks}
            icon="📝"
            color="success"
            trend="+2"
          />
          <ParentStatCard
            title="待审批"
            value={stats.pendingApprovals}
            icon="⏳"
            color="warning"
          />
          <ParentStatCard
            title="已完成"
            value={stats.completedTasks}
            icon="✅"
            color="info"
            trend="+5"
          />
        </ParentGrid>

        {/* 快捷操作 */}
        <ParentCard fullWidth>
          <ParentCardTitle>
            🚀 快捷操作
          </ParentCardTitle>
          <ParentCardContent>
            <ParentGrid>
              {quickActions.map((action, index) => (
                <ParentCard 
                  key={index}
                  interactive
                  onClick={() => action.path && navigate(action.path)}
                  style={{ cursor: 'pointer', position: 'relative' }}
                >
                  {action.badge > 0 && (
                    <ParentStatusBadge 
                      status="ACTIVE"
                      style={{
                        position: 'absolute',
                        top: AppleDesignSystem.spacing.sm,
                        right: AppleDesignSystem.spacing.sm,
                        minWidth: '24px',
                        height: '24px',
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: AppleDesignSystem.typography.textStyles.caption2.fontSize,
                        fontWeight: '600'
                      }}
                    >
                      {action.badge}
                    </ParentStatusBadge>
                  )}
                  <div style={{ 
                    fontSize: '48px', 
                    marginBottom: AppleDesignSystem.spacing.md,
                    textAlign: 'center'
                  }}>
                    {action.icon}
                  </div>
                  <ParentCardTitle style={{ margin: 0, textAlign: 'center' }}>
                    {action.title}
                  </ParentCardTitle>
                  <ParentCardContent style={{ textAlign: 'center', margin: 0 }}>
                    {action.description}
                  </ParentCardContent>
                </ParentCard>
              ))}
            </ParentGrid>
          </ParentCardContent>
        </ParentCard>

        {/* 最近活动 */}
        <ParentCard fullWidth>
          <ParentCardTitle>
            📊 最近活动
          </ParentCardTitle>
          <ParentCardContent>
            {recentActivities.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: AppleDesignSystem.spacing.xl,
                color: AppleDesignSystem.colors.semantic.secondaryLabel
              }}>
                <div style={{ fontSize: '48px', marginBottom: AppleDesignSystem.spacing.md }}>📝</div>
                <div>暂无活动记录</div>
              </div>
            ) : (
              <div style={{ display: 'grid', gap: AppleDesignSystem.spacing.md }}>
                {recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: AppleDesignSystem.spacing.md,
                      padding: AppleDesignSystem.spacing.md,
                      background: AppleDesignSystem.colors.semantic.systemFill,
                      borderRadius: AppleDesignSystem.borderRadius.medium,
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <div style={{
                      fontSize: '24px',
                      width: '40px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: 'white',
                      borderRadius: AppleDesignSystem.borderRadius.small
                    }}>
                      {getActivityIcon(activity.type)}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: AppleDesignSystem.typography.textStyles.subheadline.fontSize,
                        fontWeight: '600',
                        color: AppleDesignSystem.colors.semantic.label,
                        marginBottom: '2px'
                      }}>
                        {activity.description}
                      </div>
                      <div style={{
                        fontSize: AppleDesignSystem.typography.textStyles.footnote.fontSize,
                        color: AppleDesignSystem.colors.semantic.secondaryLabel
                      }}>
                        {activity.time}
                      </div>
                    </div>
                    <div style={{
                      fontSize: AppleDesignSystem.typography.textStyles.subheadline.fontSize,
                      fontWeight: '600',
                      color: activity.points > 0 ? 
                        AppleDesignSystem.colors.semantic.systemGreen : 
                        AppleDesignSystem.colors.semantic.systemRed
                    }}>
                      {activity.points > 0 ? '+' : ''}{activity.points}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ParentCardContent>
        </ParentCard>
      </ParentContainer>
    </ApplePageTransition>
  );
};

export default ParentDashboardApple;
