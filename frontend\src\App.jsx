import React, { useEffect, useState } from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';
import styled from 'styled-components';
import { ToastProvider } from './components/common/Toast';

// 导入页面组件
import ChildHome from './pages/child/ChildHome';
import ParentHome from './pages/parent/ParentHome';

// 检测设备方向和类型
const useDeviceDetection = () => {
  const [isLandscape, setIsLandscape] = useState(window.innerWidth > window.innerHeight);
  const [deviceType, setDeviceType] = useState('');
  
  useEffect(() => {
    // 设备检测
    const detectDevice = () => {
      const width = window.innerWidth;
      if (width <= 480) return 'mobile';
      if (width <= 1024) return 'tablet';
      return 'desktop';
    };

    // 屏幕方向检测
    const handleResize = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
      setDeviceType(detectDevice());
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return { isLandscape, deviceType };
};

function App() {
  const { isLandscape, deviceType } = useDeviceDetection();
  const location = useLocation();
  
  const isChildRoute = location.pathname.startsWith('/child');
  const isParentRoute = location.pathname.startsWith('/parent');
  
  // 显示设备方向指导提示
  const ShowOrientationGuide = () => {
    if (isChildRoute && !isLandscape && (deviceType === 'tablet' || deviceType === 'mobile')) {
      return (
        <OrientationGuide>
          <div className="guide-content">
            <div className="icon">📱</div>
            <h2>请旋转设备</h2>
            <p>儿童端需要在横屏模式下使用</p>
          </div>
        </OrientationGuide>
      );
    }
    
    if (isParentRoute && isLandscape && deviceType === 'mobile') {
      return (
        <OrientationGuide>
          <div className="guide-content">
            <div className="icon">📱</div>
            <h2>请旋转设备</h2>
            <p>家长端最好在竖屏模式下使用</p>
          </div>
        </OrientationGuide>
      );
    }
    
    return null;
  };

  return (
    <AppContainer>
      <ShowOrientationGuide />
      <Routes>
        <Route path="/" element={<Navigate to="/child" replace />} />
        <Route path="/child/*" element={<ChildHome />} />
        <Route path="/parent/*" element={<ParentHome />} />
      </Routes>
      <ToastProvider />
    </AppContainer>
  );
}

const AppContainer = styled.div`
  width: 100vw;
  height: 100vh;
  overflow: hidden;
`;

const OrientationGuide = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f0f8ff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .guide-content {
    text-align: center;
    padding: 2rem;
  }

  .icon {
    font-size: 4rem;
    animation: rotate 2s infinite;
    margin-bottom: 1rem;
  }

  h2 {
    color: #4a67ff;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  p {
    color: #555;
  }

  @keyframes rotate {
    0% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
    100% { transform: rotate(0deg); }
  }
`;

export default App; 