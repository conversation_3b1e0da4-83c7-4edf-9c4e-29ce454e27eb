package com.example.childreward.controller;

import com.example.childreward.dto.ScheduledTaskSummaryDto;
import com.example.childreward.entity.ScheduledTask;
import com.example.childreward.mapper.ScheduledTaskMapper;
import com.example.childreward.repository.ScheduledTaskRepository;
import com.example.childreward.service.TaskGenerationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@CrossOrigin(origins = "*") // 允许所有来源跨域请求
@RequestMapping("/api/scheduled-tasks")
public class ScheduledTaskController {

    private final ScheduledTaskRepository scheduledTaskRepository;
    private final TaskGenerationService taskGenerationService;
    private final ScheduledTaskMapper scheduledTaskMapper;
    private static final Logger log = LoggerFactory.getLogger(ScheduledTaskController.class);

    public ScheduledTaskController(ScheduledTaskRepository scheduledTaskRepository,
                                 TaskGenerationService taskGenerationService,
                                 ScheduledTaskMapper scheduledTaskMapper) {
        this.scheduledTaskRepository = scheduledTaskRepository;
        this.taskGenerationService = taskGenerationService;
        this.scheduledTaskMapper = scheduledTaskMapper;
    }

    // 创建一个新的任务模板
    @PostMapping("/")
    public ResponseEntity<?> createScheduledTask(@RequestBody ScheduledTask scheduledTask) {
        try {
            log.info("收到创建计划任务的请求: {}", scheduledTask);
            ScheduledTask savedTask = scheduledTaskRepository.save(scheduledTask);
            log.info("成功创建计划任务: {}", savedTask);
            return new ResponseEntity<>(savedTask, HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("创建计划任务时发生错误: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "创建计划任务失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // 获取所有任务模板（列表查询，不包含详情字段）
    @GetMapping("/")
    public ResponseEntity<?> getAllScheduledTasks() {
        log.info("收到获取所有计划任务的请求");
        try {
            List<ScheduledTask> tasks = scheduledTaskRepository.findAllOrderByActiveAndCreatedTime();
            List<ScheduledTaskSummaryDto> summaryTasks = scheduledTaskMapper.toSummaryDtoList(tasks);
            log.info("成功获取所有计划任务，共 {} 条", summaryTasks.size());
            return new ResponseEntity<>(summaryTasks, HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取计划任务时发生错误: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取计划任务失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // 根据ID获取单个任务模板
    @GetMapping("/{id}")
    public ResponseEntity<?> getScheduledTaskById(@PathVariable Long id) {
        try {
            log.info("收到获取计划任务的请求，ID: {}", id);
            Optional<ScheduledTask> taskOpt = scheduledTaskRepository.findById(id);
            
            if (taskOpt.isPresent()) {
                ScheduledTask task = taskOpt.get();
                log.info("成功获取计划任务: {}", task);
                return ResponseEntity.ok(task);
            } else {
                log.warn("未找到ID为 {} 的计划任务", id);
                Map<String, String> notFoundResponse = new HashMap<>();
                notFoundResponse.put("error", "未找到ID为 " + id + " 的计划任务");
                notFoundResponse.put("status", "not_found");
                return new ResponseEntity<>(notFoundResponse, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("获取计划任务时发生错误，ID: {}, 错误: {}", id, e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取计划任务失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // 更新一个任务模板
    @PutMapping("/{id}")
    public ResponseEntity<?> updateScheduledTask(@PathVariable Long id, @RequestBody ScheduledTask taskDetails) {
        try {
            log.info("收到更新计划任务的请求，ID: {}, 详情: {}", id, taskDetails);
            Optional<ScheduledTask> existingTaskOpt = scheduledTaskRepository.findById(id);
            
            if (existingTaskOpt.isPresent()) {
                ScheduledTask existingTask = existingTaskOpt.get();
                existingTask.setTitle(taskDetails.getTitle());
                existingTask.setDescription(taskDetails.getDescription());
                existingTask.setPoints(taskDetails.getPoints());
                existingTask.setType(taskDetails.getType());
                existingTask.setTaskRequirementType(taskDetails.getTaskRequirementType()); // 新增：更新任务类型
                existingTask.setCronExpression(taskDetails.getCronExpression());
                existingTask.setExpectedMinutes(taskDetails.getExpectedMinutes());
                existingTask.setDueTime(taskDetails.getDueTime());
                existingTask.setActive(taskDetails.isActive());
                existingTask.setDirectToReview(taskDetails.getDirectToReview());
                
                ScheduledTask updatedTask = scheduledTaskRepository.save(existingTask);
                log.info("成功更新计划任务: {}", updatedTask);
                return ResponseEntity.ok(updatedTask);
            } else {
                log.warn("未找到ID为 {} 的计划任务，无法更新", id);
                Map<String, String> notFoundResponse = new HashMap<>();
                notFoundResponse.put("error", "未找到ID为 " + id + " 的计划任务");
                notFoundResponse.put("status", "not_found");
                return new ResponseEntity<>(notFoundResponse, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("更新计划任务时发生错误，ID: {}, 错误: {}", id, e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "更新计划任务失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // 删除一个任务模板
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteScheduledTask(@PathVariable Long id) {
        try {
            log.info("收到删除计划任务的请求，ID: {}", id);
            Optional<ScheduledTask> taskOpt = scheduledTaskRepository.findById(id);
            
            if (taskOpt.isPresent()) {
                scheduledTaskRepository.delete(taskOpt.get());
                log.info("成功删除计划任务，ID: {}", id);
                return ResponseEntity.noContent().build();
            } else {
                log.warn("未找到ID为 {} 的计划任务，无法删除", id);
                Map<String, String> notFoundResponse = new HashMap<>();
                notFoundResponse.put("error", "未找到ID为 " + id + " 的计划任务");
                notFoundResponse.put("status", "not_found");
                return new ResponseEntity<>(notFoundResponse, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("删除计划任务时发生错误，ID: {}, 错误: {}", id, e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "删除计划任务失败: " + e.getMessage());
            errorResponse.put("status", "error");
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // 测试接口，用于检查API是否正常工作
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testApi() {
        log.info("收到测试API的请求");
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "API工作正常");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/generate-tasks")
    public ResponseEntity<Map<String, Object>> manualGenerateTasks() {
        try {
            log.info("手动触发任务生成");
            taskGenerationService.manualGenerateTasksFromTemplates();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "任务生成完成");
            result.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动生成任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "任务生成失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
}