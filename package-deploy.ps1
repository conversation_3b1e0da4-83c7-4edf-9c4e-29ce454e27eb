# CRS Children Reward System - Package Deployment Script
# Package frontend and backend for deployment

param(
    [switch]$SkipFrontend = $false,
    [switch]$SkipBackend = $false
)

$DeployDir = "deploy"
$BackendJar = "crs-backend.jar"
$FrontendZip = "crs-frontend-deploy.zip"

Write-Host "=== CRS Package Deployment Script ===" -ForegroundColor Cyan
Write-Host "Deploy Directory: $DeployDir" -ForegroundColor Cyan
Write-Host ""

# Create deploy directory if not exists
if (-not (Test-Path $DeployDir)) {
    New-Item -ItemType Directory -Path $DeployDir | Out-Null
    Write-Host "Created deploy directory" -ForegroundColor Green
}

# Package Backend
if (-not $SkipBackend) {
    Write-Host "=== Packaging Backend ===" -ForegroundColor Cyan
    
    # Check if Maven is available
    try {
        mvn --version | Out-Null
        Write-Host "Maven is available" -ForegroundColor Green
    } catch {
        Write-Host "Maven is not available, please install Maven" -ForegroundColor Red
        exit 1
    }
    
    # Build backend
    Write-Host "Building backend with Maven..." -ForegroundColor Cyan
    Set-Location backend
    
    # Clean and package
    mvn clean package -DskipTests
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Backend build failed" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    
    # Copy JAR file
    $jarFile = Get-ChildItem -Path "target" -Name "*.jar" | Where-Object { $_ -notlike "*-sources.jar" -and $_ -notlike "*-javadoc.jar" } | Select-Object -First 1
    if ($jarFile) {
        Copy-Item "target/$jarFile" "../$DeployDir/$BackendJar" -Force
        Write-Host "Backend JAR copied to $DeployDir/$BackendJar" -ForegroundColor Green
    } else {
        Write-Host "Backend JAR file not found" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    
    Set-Location ..
    Write-Host "Backend packaging completed" -ForegroundColor Green
} else {
    Write-Host "Skipping backend packaging" -ForegroundColor Yellow
}

# Package Frontend
if (-not $SkipFrontend) {
    Write-Host "=== Packaging Frontend ===" -ForegroundColor Cyan
    
    # Check if Node.js is available
    try {
        node --version | Out-Null
        Write-Host "Node.js is available" -ForegroundColor Green
    } catch {
        Write-Host "Node.js is not available, please install Node.js" -ForegroundColor Red
        exit 1
    }
    
    # Build frontend
    Write-Host "Building frontend with npm..." -ForegroundColor Cyan
    Set-Location frontend
    
    # Install dependencies if node_modules doesn't exist
    if (-not (Test-Path "node_modules")) {
        Write-Host "Installing npm dependencies..." -ForegroundColor Cyan
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "npm install failed" -ForegroundColor Red
            Set-Location ..
            exit 1
        }
    }
    
    # Build for production
    Write-Host "Building for production..." -ForegroundColor Cyan
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Frontend build failed" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    
    # Create zip package
    Write-Host "Creating frontend deployment package..." -ForegroundColor Cyan
    if (Test-Path "../$DeployDir/$FrontendZip") {
        Remove-Item "../$DeployDir/$FrontendZip" -Force
    }
    
    # Use PowerShell to create zip
    Compress-Archive -Path "dist/*" -DestinationPath "../$DeployDir/$FrontendZip" -Force
    Write-Host "Frontend package created: $DeployDir/$FrontendZip" -ForegroundColor Green
    
    Set-Location ..
    Write-Host "Frontend packaging completed" -ForegroundColor Green
} else {
    Write-Host "Skipping frontend packaging" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Packaging Complete ===" -ForegroundColor Green
Write-Host "Deployment files:" -ForegroundColor Cyan

if (Test-Path "$DeployDir/$BackendJar") {
    $backendSize = (Get-Item "$DeployDir/$BackendJar").Length / 1MB
    Write-Host "  Backend: $DeployDir/$BackendJar ($([math]::Round($backendSize, 2)) MB)" -ForegroundColor Yellow
}

if (Test-Path "$DeployDir/$FrontendZip") {
    $frontendSize = (Get-Item "$DeployDir/$FrontendZip").Length / 1MB
    Write-Host "  Frontend: $DeployDir/$FrontendZip ($([math]::Round($frontendSize, 2)) MB)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Deployment Instructions:" -ForegroundColor Cyan
Write-Host "1. Copy files to target server" -ForegroundColor White
Write-Host "2. Backend: java -jar $BackendJar" -ForegroundColor White
Write-Host "3. Frontend: Extract $FrontendZip to nginx root directory" -ForegroundColor White
Write-Host ""
