import SwiftUI
import Combine
import Foundation
import Combine

// 数据模型已移动到Models/Task.swift

struct ContentView: View {
    @State private var selectedTab: Int = 1  // 设置任务页为默认首页
    @State private var showTaskDetail = false
    @State private var selectedTask: TaskSummary?  // 添加选中的任务
    @State private var avatarClickCount = 0
    @State private var showEnvironmentSelector = false

    // 环境管理
    @StateObject private var environmentManager = EnvironmentManager.shared

    // 设置管理
    @StateObject private var settingsManager = SettingsManager.shared

    // 息屏管理
    @StateObject private var screenDimmingManager = ScreenDimmingManager.shared

    // API服务
    private let apiService = ChildAPIService.shared

    // 数据状态 - 使用真实API数据
    @State private var tasks: [TaskSummary] = [] {
        didSet {
            print("🔍 DEBUG: tasks数组已更新，新数量: \(tasks.count)")
            for (index, task) in tasks.enumerated() {
                print("🔍 DEBUG: 任务\(index + 1): \(task.title) - ID: \(task.id)")
            }
        }
    }
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var totalPoints = 0
    @State private var todayPoints = 0

    // 统计数据状态
    @State private var taskStatistics: TaskStatistics?
    @State private var todayPointChange: TodayPointChange?
    @State private var pointBalance: PointBalance?
    @State private var pointRecords: [PointRecord] = []

    // 积分页面状态
    @State private var pointsTimeRange = 7 // 默认7天
    @State private var pointsFilter = "ALL" // 积分记录筛选器
    @State private var pointsStats = PointsStatistics(totalEarned: 0, totalSpent: 0, net: 0)
    @State private var filteredPointRecords: [PointRecord] = []

    // 头像加载辅助函数
    private func loadAvatarImage() -> UIImage? {
        print("🔍 开始加载头像...")

        // 方法1: 尝试从Assets.xcassets加载
        if let image = UIImage(named: "avatar") {
            print("✅ 成功从Assets加载头像，尺寸: \(image.size)")
            return image
        } else {
            print("❌ 从Assets加载头像失败")
        }

        // 方法2: 尝试从Bundle加载
        if let path = Bundle.main.path(forResource: "avatar", ofType: "png") {
            print("🔍 找到Bundle路径: \(path)")
            if let image = UIImage(contentsOfFile: path) {
                print("✅ 成功从Bundle加载头像，尺寸: \(image.size)")
                return image
            } else {
                print("❌ 从Bundle路径加载图片失败")
            }
        } else {
            print("❌ 在Bundle中找不到avatar.png")
        }

        // 方法3: 列出所有可用的图片资源
        if let resourcePath = Bundle.main.resourcePath {
            print("🔍 Bundle资源路径: \(resourcePath)")
            let fileManager = FileManager.default
            do {
                let files = try fileManager.contentsOfDirectory(atPath: resourcePath)
                let imageFiles = files.filter { $0.lowercased().contains("avatar") || $0.lowercased().hasSuffix(".png") }
                print("🔍 找到的相关文件: \(imageFiles)")
            } catch {
                print("❌ 无法列出Bundle内容: \(error)")
            }
        }

        print("❌ 所有头像加载方法都失败了")
        return nil
    }

    // 网络请求管理 - 分离不同类型的订阅以避免竞态条件
    @State private var taskCancellables = Set<AnyCancellable>()
    @State private var pointsCancellables = Set<AnyCancellable>()
    @State private var statisticsCancellables = Set<AnyCancellable>()
    @State private var actionCancellables = Set<AnyCancellable>()

    // 操作状态管理
    @State private var isPerformingAction = false
    @State private var showRetryButton = false

    // 右侧面板展开状态
    @State private var isRightPanelExpanded = true

    // 奖励系统ViewModel
    @StateObject private var rewardViewModel = RewardViewModel()

    var body: some View {
        ZStack {
            // 专为iPad横屏设计的全新三栏布局
            HStack(spacing: 0) {
                // 左侧导航栏 (280pt)
                leftSidebar()
                    .frame(width: 280)
                    .background(.ultraThinMaterial)

                // 中间主内容区域 (自适应)
                mainContentArea()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGroupedBackground))

                // 右侧信息面板 (根据设置显示或隐藏)
                if settingsManager.infoPanelEnabled {
                    rightInfoPanel()
                        .frame(width: isRightPanelExpanded ? 320 : 60)
                        .background(.ultraThinMaterial)
                }
            }

            // 息屏覆盖层（仅在模拟器中显示）
            if screenDimmingManager.showDimmingOverlay {
                DimmingOverlayView()
                    .onTapGesture {
                        screenDimmingManager.restoreBrightness()
                    }
            }
        }
        .ignoresSafeArea(.all, edges: .bottom)
        .onAppear {
            print("🔍 DEBUG: ContentView.onAppear 被调用")
            print("🔍 DEBUG: 当前tasks数组大小: \(tasks.count)")

            // 🔍 DEBUG: 强制清除所有可能的缓存
            clearAllCaches()

            loadData()

            // 监听积分变化通知
            NotificationCenter.default.addObserver(
                forName: .pointsDidChange,
                object: nil,
                queue: .main
            ) { _ in
                print("🔔 收到积分变化通知，刷新积分数据...")
                loadPoints()
                loadStatistics()
            }
        }
        .onChange(of: settingsManager.rewardTabEnabled) { enabled in
            // 当奖励页tab被禁用且当前选中奖励页时，自动切换到任务页
            if !enabled && selectedTab == 4 {
                selectedTab = 1 // 切换到任务页
                print("🔄 奖励页tab被禁用，自动切换到任务页")
            }
        }
        .onChange(of: selectedTab) { newTab in
            // 当切换tab时，强制刷新对应页面的数据
            print("🔄 Tab切换到: \(newTab)，强制刷新所有数据...")

            // 🔥 关键修复：强制清除所有缓存和订阅
            clearAllCaches()

            // 🔥 强制清除URLSession缓存，确保不使用任何缓存数据
            URLCache.shared.removeAllCachedResponses()

            // 🔥 添加短暂延迟，确保缓存清除完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                // 强制重新加载基础数据
                self.loadPoints()
                self.loadStatistics()

                // 根据切换的tab刷新对应的内容数据
                switch newTab {
                case 1:
                    // 任务页面 - 重新获取任务数据
                    print("🔄 强制刷新任务页面数据...")
                    self.loadTasksData()
                case 2:
                    // 积分页面 - 重新获取积分记录和余额
                    print("🔄 强制刷新积分页面数据...")
                    self.loadPointsData()
                case 3:
                    // 兑换页面 - 重新获取兑换数据
                    print("🔄 强制刷新兑换页面数据...")
                    self.loadExchangeData()
                    // 通知RewardViewModel刷新数据
                    self.rewardViewModel.loadData()
                case 4:
                    // 奖励页面 - 重新获取奖励数据
                    print("🔄 强制刷新奖励页面数据...")
                    self.loadRewardsData()
                    // 通知RewardViewModel刷新数据
                    self.rewardViewModel.loadData()
                default:
                    break
                }
            }
        }
        .onChange(of: environmentManager.currentEnvironment) { _ in
            print("🔄 环境切换到: \(environmentManager.currentEnvironmentName)")
            print("🌐 API URL: \(environmentManager.currentBaseURL)")
            loadData()
        }
        .onDisappear {
            // 移除通知监听器
            NotificationCenter.default.removeObserver(self, name: .pointsDidChange, object: nil)
        }
        .sheet(isPresented: $showEnvironmentSelector) {
            VStack(spacing: 32) {
                Text("环境配置")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                VStack(spacing: 16) {
                    Button("开发环境") {
                        environmentManager.switchEnvironment(to: .development)
                        showEnvironmentSelector = false
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(environmentManager.currentEnvironment == .development ? .blue : .gray)
                    .foregroundColor(.white)
                    .cornerRadius(12)

                    Button("生产环境") {
                        environmentManager.switchEnvironment(to: .production)
                        showEnvironmentSelector = false
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(environmentManager.currentEnvironment == .production ? .green : .gray)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .padding(.horizontal, 32)

                Spacer()
            }
            .padding(.top, 40)
        }
    }

    // MARK: - 左侧导航栏
    private func leftSidebar() -> some View {
        VStack(spacing: 0) {
            // 用户信息区域
            VStack(spacing: 20) {
                // 头像（可点击切换环境）
                Button(action: {
                    avatarClickCount += 1
                    if avatarClickCount >= 10 {
                        showEnvironmentSelector = true
                        avatarClickCount = 0
                    }
                }) {
                    ZStack {
                        // 背景圆形（作为边框效果）
                        Circle()
                            .fill(LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 84, height: 84)

                        // 头像图片
                        Group {
                            if let avatarImage = loadAvatarImage() {
                                Image(uiImage: avatarImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 80, height: 80)
                                    .clipShape(Circle())
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white, lineWidth: 2)
                                    )
                            } else {
                                // 备用显示
                                Text("👶")
                                    .font(.system(size: 40))
                                    .frame(width: 80, height: 80)
                            }
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())

                VStack(spacing: 8) {
                    Text("七七")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Lv.5 积分达人")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    // 环境指示器
                    HStack(spacing: 4) {
                        Circle()
                            .fill(environmentManager.currentEnvironment == .production ? .green : .orange)
                            .frame(width: 6, height: 6)
                        Text(environmentManager.currentEnvironmentName)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 积分信息卡片
                VStack(spacing: 12) {
                    // 积分余额
                    HStack(spacing: 8) {
                        Image(systemName: "creditcard.fill")
                            .foregroundColor(.blue)
                            .font(.system(size: 16))
                        Text("\(totalPoints)")
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .font(.system(size: 18))
                        Text("积分余额")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // 分隔线
                    Divider()
                        .frame(height: 1)
                        .background(.secondary.opacity(0.3))

                    // 今日积分变化
                    HStack(spacing: 8) {
                        Image(systemName: todayPoints >= 0 ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                            .foregroundColor(todayPoints >= 0 ? .green : .red)
                            .font(.system(size: 16))
                        Text(todayPoints >= 0 ? "+\(todayPoints)" : "\(todayPoints)")
                            .fontWeight(.semibold)
                            .foregroundColor(todayPoints >= 0 ? .green : .red)
                            .font(.system(size: 16))
                        Text("今日变化")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.ultraThinMaterial)
                .cornerRadius(16)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            }
            .padding(.top, 40)
            .padding(.horizontal, 20)

            Spacer()

            // 导航菜单
            VStack(spacing: 8) {
                NavigationButton(
                    icon: "list.bullet.clipboard.fill",
                    title: "任务",
                    isSelected: selectedTab == 1
                ) {
                    print("🔄 用户点击任务tab - 强制刷新")
                    selectedTab = 1
                    // 强制刷新任务数据
                    loadTasks()
                    refreshDataForTab()
                }

                NavigationButton(
                    icon: "star.circle.fill",
                    title: "积分",
                    isSelected: selectedTab == 2
                ) {
                    selectedTab = 2
                    refreshDataForTab()
                }

                NavigationButton(
                    icon: "cart.fill",
                    title: "兑换",
                    isSelected: selectedTab == 3
                ) {
                    selectedTab = 3
                    refreshDataForTab()
                }

                // 奖励页tab（根据设置显示或隐藏）
                if settingsManager.rewardTabEnabled {
                    NavigationButton(
                        icon: "gift.fill",
                        title: "奖励",
                        isSelected: selectedTab == 4
                    ) {
                        selectedTab = 4
                        refreshDataForTab()
                    }
                }
            }
            .padding(.horizontal, 20)

            Spacer()

            // 设置按钮
            Button(action: {
                selectedTab = 5 // 设置页面
            }) {
                HStack {
                    Image(systemName: "gearshape.fill")
                    Text("设置")
                    Spacer()
                }
                .foregroundColor(selectedTab == 5 ? .blue : .secondary)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
            }
            .padding(.bottom, 40)
        }
    }

    // MARK: - 中间主内容区域
    private func mainContentArea() -> some View {
        VStack(spacing: 0) {
            // 顶部状态栏
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(pageTitle)
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text(getCurrentTime())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 总积分显示
                HStack(spacing: 12) {
                    Image(systemName: "star.fill")
                        .font(.title2)
                        .foregroundColor(.orange)

                    Text("\(totalPoints)")
                        .font(.title)
                        .fontWeight(.bold)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(.ultraThinMaterial)
                .cornerRadius(25)
            }
            .padding(.horizontal, 32)
            .padding(.top, 20)
            .padding(.bottom, 24)

            // 主内容区域
            ScrollView {
                currentContent
            }
        }
    }

    // MARK: - 右侧信息面板
    private func rightInfoPanel() -> some View {
        VStack(spacing: 0) {
            // 顶部控制栏
            HStack {
                if isRightPanelExpanded {
                    Text("信息面板")
                        .font(.headline)
                        .fontWeight(.bold)

                    Spacer()
                }

                // 展开/收起按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isRightPanelExpanded.toggle()
                    }
                }) {
                    Image(systemName: isRightPanelExpanded ? "sidebar.right" : "sidebar.left")
                        .font(.title2)
                        .foregroundColor(.blue)
                        .frame(width: 24, height: 24)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, isRightPanelExpanded ? 20 : 18)
            .padding(.vertical, 16)
            .background(.ultraThinMaterial)

            if isRightPanelExpanded {
                // 展开状态：显示完整内容
                ScrollView {
                    VStack(spacing: 24) {
                        // 今日统计
                        VStack(alignment: .leading, spacing: 16) {
                            Text("今日统计")
                                .font(.headline)
                                .fontWeight(.bold)

                            VStack(spacing: 12) {
                                StatRow(
                                    icon: "checkmark.circle.fill",
                                    title: "已完成",
                                    value: "\(taskStatistics?.todayCompleted ?? 0)",
                                    color: .green
                                )
                                StatRow(
                                    icon: "clock.fill",
                                    title: "进行中",
                                    value: "\(taskStatistics?.todayInProgress ?? 0)",
                                    color: .blue
                                )
                                StatRow(
                                    icon: "circle.fill",
                                    title: "未开始",
                                    value: "\(taskStatistics?.todayNotStarted ?? 0)",
                                    color: .gray
                                )
                                StatRow(
                                    icon: "gift.fill",
                                    title: "可获得积分",
                                    value: "\(taskStatistics?.todayAvailablePoints ?? 0)",
                                    color: .purple
                                )
                                StatRow(
                                    icon: "star.fill",
                                    title: "已获得积分",
                                    value: "\(taskStatistics?.todayEarnedPoints ?? 0)",
                                    color: .orange
                                )
                            }
                        }
                        .padding(20)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)


                        // 积分变化情况
                        VStack(alignment: .leading, spacing: 16) {
                            Text("积分变化")
                                .font(.headline)
                                .fontWeight(.bold)

                            VStack(spacing: 12) {
                                PointChangeRow(
                                    icon: "plus.circle.fill",
                                    title: "今日获得",
                                    value: "+\(taskStatistics?.todayEarnedPoints ?? 0)",
                                    color: .green
                                )
                                PointChangeRow(
                                    icon: "minus.circle.fill",
                                    title: "今日消费",
                                    value: "-\(todayPointChange?.calculatedSpent ?? 0)",
                                    color: .red
                                )
                                PointChangeRow(
                                    icon: "equal.circle.fill",
                                    title: "净变化",
                                    value: todayPointChange?.formattedChange ?? "+0",
                                    color: (todayPointChange?.todayChange ?? 0) >= 0 ? .blue : .orange
                                )
                                PointChangeRow(
                                    icon: "creditcard.fill",
                                    title: "总积分",
                                    value: "\(pointBalance?.totalPoints ?? 0)",
                                    color: .purple
                                )
                            }
                        }
                        .padding(20)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)

            // 根据当前tab显示不同的侧边栏内容
            if selectedTab == 2 { // 积分页面显示积分统计摘要
                    VStack(alignment: .leading, spacing: 16) {
                        Text("积分统计")
                            .font(.headline)
                            .fontWeight(.bold)

                        VStack(spacing: 12) {
                            StatRow(
                                icon: "arrow.up.circle.fill",
                                title: "总获得",
                                value: "+\(pointsStats.totalEarned)",
                                color: .green
                            )
                            StatRow(
                                icon: "arrow.down.circle.fill",
                                title: "总消费",
                                value: "-\(pointsStats.totalSpent)",
                                color: .red
                            )
                            StatRow(
                                icon: "equal.circle.fill",
                                title: "净积分",
                                value: pointsStats.net >= 0 ? "+\(pointsStats.net)" : "\(pointsStats.net)",
                                color: pointsStats.net >= 0 ? .blue : .orange
                            )
                        }
                    }
                    .padding(20)
                    .background(.ultraThinMaterial)
                    .cornerRadius(16)
                } else { // 其他页面显示积分记录
                    VStack(alignment: .leading, spacing: 16) {
                        Text("积分记录")
                            .font(.headline)
                            .fontWeight(.bold)

                        if pointRecords.isEmpty {
                            Text("暂无积分记录")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding(.vertical, 20)
                        } else {
                                VStack(spacing: 8) {
                                    ForEach(pointRecords) { record in
                                        PointRecordRow(record: record)
                                    }
                                }
                            }
                        }
                        .padding(20)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)
                    }
                    }
                    .padding(20)
                }
            } else {
                // 收起状态：显示简化的快速信息
                VStack(spacing: 16) {
                    // 总积分快速显示
                    VStack(spacing: 8) {
                        Image(systemName: "star.fill")
                            .font(.title2)
                            .foregroundColor(.orange)

                        Text("\(totalPoints)")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        Text("总积分")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 12)
                    .frame(maxWidth: .infinity)
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)

                    // 今日积分变化
                    VStack(spacing: 8) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.green)

                        Text("+\(todayPoints)")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.green)

                        Text("今日")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 12)
                    .frame(maxWidth: .infinity)
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)

                    Spacer()
                }
                .padding(.horizontal, 8)
                .padding(.top, 16)
            }
        }
    }

    // MARK: - 辅助函数
    private var pageTitle: String {
        switch selectedTab {
        case 1: return "我的任务"
        case 2: return "积分记录"
        case 3: return "兑换商店"
        case 4: return settingsManager.rewardTabEnabled ? "奖励中心" : "我的任务"
        case 5: return "应用设置"
        default: return "我的任务"
        }
    }

    // MARK: - 今日总用时计算

    /// 计算今日已完成任务的总用时（分钟）
    private var todayTotalUsedMinutes: Int {
        return tasks.filter { task in
            // 只要不是未开始和进行中就算已用时
            task.status != .notStarted && task.status != .inProgress
        }.reduce(0) { total, task in
            // 使用实际用时，如果没有实际用时则使用预计用时
            let usedMinutes = task.actualMinutes ?? task.expectedMinutes
            return total + usedMinutes
        }
    }

    /// 格式化今日总用时显示
    private var formattedTodayTotalUsedTime: String {
        let totalMinutes = todayTotalUsedMinutes

        if totalMinutes == 0 {
            return "0分钟"
        }

        if totalMinutes < 60 {
            return "\(totalMinutes)分钟"
        } else {
            let hours = totalMinutes / 60
            let minutes = totalMinutes % 60
            if minutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(minutes)分钟"
            }
        }
    }

    @ViewBuilder
    private var currentContent: some View {
        switch selectedTab {
        case 1:
            tasksContent()
        case 2:
            pointsContent()
        case 3:
            exchangeContent()
        case 4:
            if settingsManager.rewardTabEnabled {
                rewardsContent()
            } else {
                tasksContent()
            }
        case 5:
            SettingsView()
        default:
            tasksContent()
        }
    }

    private func getCurrentTime() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMM d"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: Date())
    }

    // MARK: - 数据加载方法

    private func clearAllCaches() {
        print("🔥 强制清除所有缓存和订阅，确保获取最新数据")

        // 🔥 清除所有网络订阅，确保重新发起请求
        taskCancellables.removeAll()
        pointsCancellables.removeAll()
        statisticsCancellables.removeAll()
        actionCancellables.removeAll()

        // 🔥 强制清除URLSession缓存
        URLCache.shared.removeAllCachedResponses()

        // 🔥 清除内存中的数据状态
        tasks = []
        pointRecords = []
        filteredPointRecords = []

        // 🔥 重置加载状态
        isLoading = false
        errorMessage = nil

        // 🔥 清除UserDefaults中可能的任务缓存
        let userDefaults = UserDefaults.standard
        userDefaults.removeObject(forKey: "cached_tasks")
        userDefaults.removeObject(forKey: "tasks_cache")
        userDefaults.removeObject(forKey: "today_tasks")
        userDefaults.removeObject(forKey: "cached_points")
        userDefaults.removeObject(forKey: "point_records")
        userDefaults.removeObject(forKey: "cached_statistics")
        userDefaults.removeObject(forKey: "cached_rewards")
        userDefaults.synchronize()

        print("🔥 缓存清除完成，准备重新获取数据")

        // 强制清空所有数据数组和状态
        tasks = []
        taskStatistics = nil
        pointBalance = nil
        todayPointChange = nil
        pointRecords = []
        filteredPointRecords = []
        pointsStats = PointsStatistics(totalEarned: 0, totalSpent: 0, net: 0)

        // 重置积分显示
        totalPoints = 0
        todayPoints = 0

        print("🔍 DEBUG: 缓存和订阅清除完成")
    }

    private func loadData() {
        print("🔄 开始加载数据...")
        print("🌐 当前环境: \(environmentManager.currentEnvironmentName)")
        print("🌐 API基础URL: \(environmentManager.currentBaseURL)")

        loadTasks()
        loadPoints()
        loadStatistics()
    }

    private func loadTasks() {
        let loadId = UUID().uuidString.prefix(8)
        let startTime = Date()

        print("🚀 [加载\(loadId)] ===== 开始加载任务 =====")
        print("⏰ [加载\(loadId)] 开始时间: \(DateFormatter.logFormatter.string(from: startTime))")
        print("🌐 [加载\(loadId)] 当前环境: \(environmentManager.currentEnvironmentName)")
        print("🌐 [加载\(loadId)] API基础URL: \(environmentManager.currentBaseURL)")
        print("🌐 [加载\(loadId)] 完整API URL: \(APIConfig.baseURL)")

        // 先进行网络连接测试
        print("🔍 [加载\(loadId)] 开始网络连接测试...")
        NetworkManager.shared.testConnection()
            .sink { isConnected in
                print("🔍 [加载\(loadId)] 网络连接测试结果: \(isConnected ? "成功" : "失败")")
                if !isConnected {
                    print("⚠️ [加载\(loadId)] 网络连接不可用，但仍尝试加载任务")
                }
            }
            .store(in: &taskCancellables)

        // 只取消任务相关的请求，避免影响其他功能的网络请求
        print("🔄 [加载\(loadId)] 取消之前的请求，数量: \(taskCancellables.count)")
        taskCancellables.removeAll()

        // 清空任务数组
        print("🗑️ [加载\(loadId)] 清空tasks数组，之前数量: \(tasks.count)")
        tasks = []

        isLoading = true
        errorMessage = nil
        print("📊 [加载\(loadId)] 设置加载状态: isLoading=true, errorMessage=nil")

        // 直接进行网络请求
        print("🌐 [加载\(loadId)] 即将调用 apiService.getTodayTasks()")
        apiService.getTodayTasks()
            .timeout(.seconds(15), scheduler: DispatchQueue.main) // 增加超时时间到15秒
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    let endTime = Date()
                    let duration = endTime.timeIntervalSince(startTime)

                    self.isLoading = false
                    print("📊 [加载\(loadId)] 设置加载状态: isLoading=false")
                    print("⏱️ [加载\(loadId)] 总耗时: \(String(format: "%.3f", duration))秒")

                    if case .failure(let error) = completion {
                        print("❌ [加载\(loadId)] 请求失败")
                        print("❌ [加载\(loadId)] 错误类型: \(type(of: error))")
                        print("❌ [加载\(loadId)] 错误详情: \(error)")

                        let errorMsg: String
                        if case .timeout = error {
                            errorMsg = "网络连接超时"
                            print("⏰ [加载\(loadId)] 具体原因: 请求超时")
                        } else if case .networkError(let underlyingError) = error,
                                  let urlError = underlyingError as? URLError,
                                  urlError.code == .timedOut {
                            errorMsg = "网络连接超时"
                            print("⏰ [加载\(loadId)] 具体原因: 底层网络超时")
                        } else {
                            errorMsg = "网络请求失败"
                            print("🔍 [加载\(loadId)] 具体原因: \(error.localizedDescription)")
                        }

                        self.errorMessage = errorMsg
                        self.showRetryButton = true
                        print("📋 [加载\(loadId)] 设置错误状态: errorMessage=\(errorMsg), showRetryButton=true")
                    } else {
                        print("✅ [加载\(loadId)] 请求完成成功")
                        self.showRetryButton = false
                    }
                },
                receiveValue: { tasks in
                    let endTime = Date()
                    let duration = endTime.timeIntervalSince(startTime)

                    print("✅ [加载\(loadId)] 成功获取任务数据")
                    print("📋 [加载\(loadId)] 任务数量: \(tasks.count)")
                    print("⏱️ [加载\(loadId)] 数据接收耗时: \(String(format: "%.3f", duration))秒")

                    for (index, task) in tasks.enumerated() {
                        print("📋 [加载\(loadId)] 网络任务\(index + 1): \(task.title) - 状态: \(task.status.rawValue) - ID:\(task.id)")
                    }

                    // 检查任务数据的详细信息
                    print("📊 [加载\(loadId)] 即将更新tasks数组")
                    print("📊 [加载\(loadId)] 当前tasks数组大小: \(self.tasks.count)")
                    print("📊 [加载\(loadId)] 新任务数组大小: \(tasks.count)")

                    // 更新任务数据
                    self.tasks = tasks
                    print("📊 [加载\(loadId)] 任务数组已更新，当前任务数: \(self.tasks.count)")

                    // 验证更新后的数据
                    print("🔍 [加载\(loadId)] 更新后验证:")
                    for (index, task) in self.tasks.enumerated() {
                        print("🔍 [加载\(loadId)] 本地任务\(index + 1): \(task.title) - ID: \(task.id)")
                    }

                    self.showRetryButton = false
                    print("🏁 [加载\(loadId)] ===== 任务加载完成 =====")
                }
            )
            .store(in: &taskCancellables)
    }



    private func loadPoints() {
        let requestId = UUID().uuidString.prefix(8)
        print("💰 [请求ID: \(requestId)] 开始强制加载积分数据...")
        print("🌐 当前环境: \(environmentManager.currentEnvironmentName)")
        print("🌐 当前API地址: \(environmentManager.currentBaseURL)")
        print("🌐 完整积分API: \(environmentManager.currentBaseURL)/api/points/total")

        // 清除积分相关的订阅，确保重新获取
        pointsCancellables.removeAll()

        // 强制清除URLCache，确保不使用缓存
        URLCache.shared.removeAllCachedResponses()
        print("🔄 [请求ID: \(requestId)] 已清除URL缓存，强制重新请求")

        // 加载总积分 - 添加超时和重试
        apiService.getPointBalance()
            .retry(1) // 失败时重试1次
            .timeout(.seconds(8), scheduler: DispatchQueue.main) // 8秒超时
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .failure(let error):
                        print("❌ [请求ID: \(requestId)] 加载积分失败: \(error)")
                        print("❌ [请求ID: \(requestId)] 错误详情: \(error.localizedDescription)")
                        // 积分加载失败时使用默认值，不影响主要功能
                        self.totalPoints = 0
                        print("❌ [请求ID: \(requestId)] 设置默认积分为0")
                    case .finished:
                        print("✅ [请求ID: \(requestId)] 积分请求完成")
                    }
                },
                receiveValue: { balance in
                    print("✅ [请求ID: \(requestId)] 收到积分响应: \(balance)")
                    print("✅ [请求ID: \(requestId)] balance.totalPoints = \(balance.totalPoints)")
                    print("✅ [请求ID: \(requestId)] balance.total = \(balance.total)")
                    print("✅ [请求ID: \(requestId)] 更新前 totalPoints = \(self.totalPoints)")
                    self.totalPoints = balance.total
                    print("✅ [请求ID: \(requestId)] 更新后 totalPoints = \(self.totalPoints)")
                    print("✅ [请求ID: \(requestId)] 成功加载总积分: \(balance.total)")
                }
            )
            .store(in: &pointsCancellables)

        // 加载今日积分变化 - 添加超时和重试
        apiService.getTodayPointChange()
            .retry(1) // 失败时重试1次
            .timeout(.seconds(8), scheduler: DispatchQueue.main) // 8秒超时
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载今日积分失败: \(error)")
                        // 今日积分加载失败时使用默认值
                        self.todayPoints = 0
                    }
                },
                receiveValue: { pointChange in
                    todayPoints = pointChange.todayChange
                    print("✅ 成功加载今日积分变化: \(pointChange.todayChange)")
                }
            )
            .store(in: &pointsCancellables)
    }

    private func loadStatistics() {
        print("📊 开始加载统计数据...")

        // 加载任务统计
        apiService.getTaskStatistics()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载任务统计失败: \(error)")
                    }
                },
                receiveValue: { statistics in
                    self.taskStatistics = statistics
                    print("✅ 成功加载任务统计: 今日完成\(statistics.todayCompleted)/\(statistics.todayTotal)")
                }
            )
            .store(in: &statisticsCancellables)

        // 加载积分余额（用于右侧面板）
        apiService.getPointBalance()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载积分余额失败: \(error)")
                    }
                },
                receiveValue: { balance in
                    self.pointBalance = balance
                    print("✅ 成功加载积分余额: \(balance.totalPoints)")
                }
            )
            .store(in: &pointsCancellables)

        // 加载今日积分变化（用于右侧面板）
        apiService.getTodayPointChange()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载今日积分变化失败: \(error)")
                    }
                },
                receiveValue: { pointChange in
                    self.todayPointChange = pointChange
                    print("✅ 成功加载今日积分变化: \(pointChange.todayChange)")
                }
            )
            .store(in: &pointsCancellables)

        // 加载积分记录（用于右侧面板）
        apiService.getPointRecords()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载积分记录失败: \(error)")
                    }
                },
                receiveValue: { records in
                    // 只保留最近的5条记录，按时间倒序排列
                    self.pointRecords = Array(records.prefix(5))
                    print("✅ 成功加载积分记录: \(records.count)条记录")
                }
            )
            .store(in: &pointsCancellables)
    }

    private func toggleTaskCompletion(_ task: TaskSummary) {
        if task.isCompleted {
            // 任务已完成，不允许取消
            print("⚠️ 任务已完成，无法取消")
            return
        }

        print("🎯 开始完成任务: \(task.title)")

        apiService.completeTask(taskId: task.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        let errorMsg = "完成任务失败: \(error.localizedDescription)"
                        print("❌ \(errorMsg)")
                        errorMessage = errorMsg
                    }
                },
                receiveValue: { _ in
                    print("✅ 任务完成成功")
                    // 重新加载数据
                    loadData()
                }
            )
            .store(in: &actionCancellables)
    }

    private func refreshDataForTab() {
        print("🔄 强制刷新当前页面数据...")

        // 🔥 关键修复：强制清除所有缓存，确保获取最新数据
        clearAllCaches()

        // 🔥 强制清除URLSession缓存
        URLCache.shared.removeAllCachedResponses()

        // 🔥 添加短暂延迟，确保缓存清除完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 根据当前选中的tab，强制刷新对应的数据
            switch self.selectedTab {
            case 1: // 任务页面
                print("🔄 强制刷新任务数据...")
                self.loadTasks()
                self.loadStatistics()
                self.loadPoints() // 确保任务页面也显示最新积分
            case 2: // 积分页面
                print("🔄 强制刷新积分数据...")
                self.loadPoints() // 更新左上角总积分显示
                self.loadPointsData() // 更新积分页面详细数据
                self.loadStatistics() // 更新统计数据
            case 3: // 兑换页面
                print("🔄 强制刷新兑换页面数据...")
                self.loadPoints() // 兑换页面需要显示最新积分余额
                self.loadExchangeData() // 重新获取兑换数据
                // 通知RewardViewModel强制刷新数据
                self.rewardViewModel.loadData()
            case 4: // 奖励页面
                print("🔄 强制刷新奖励页面数据...")
                self.loadPoints() // 奖励页面也需要显示最新积分
                self.loadRewardsData() // 重新获取奖励数据
                // 通知RewardViewModel强制刷新数据
                self.rewardViewModel.loadData()
            default:
                print("🔄 未知tab，执行全量数据刷新")
                self.loadData()
            }
        }
    }

    private func startTask(_ task: TaskSummary) {
        print("🚀 开始任务: \(task.title)")

        // 设置操作状态
        isPerformingAction = true
        errorMessage = nil

        apiService.startTask(taskId: task.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isPerformingAction = false
                    if case .failure(let error) = completion {
                        let errorMsg = "开始任务失败: \(error.localizedDescription)"
                        print("❌ \(errorMsg)")
                        self.errorMessage = errorMsg
                    }
                },
                receiveValue: { updatedTask in
                    print("✅ 任务开始成功")

                    // 重新加载数据以获取最新状态
                    self.loadData()
                }
            )
            .store(in: &actionCancellables)
    }

    private func completeTask(_ task: TaskSummary) {
        print("🎯 完成任务: \(task.title)")

        // 设置操作状态
        isPerformingAction = true
        errorMessage = nil

        apiService.completeTask(taskId: task.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isPerformingAction = false
                    if case .failure(let error) = completion {
                        let errorMsg = "完成任务失败: \(error.localizedDescription)"
                        print("❌ \(errorMsg)")
                        self.errorMessage = errorMsg
                    }
                },
                receiveValue: { updatedTask in
                    print("✅ 任务完成成功")

                    // 重新加载数据以获取最新状态
                    self.loadData()
                }
            )
            .store(in: &actionCancellables)
    }

    // MARK: - 内容区域

    private func tasksContent() -> some View {
        VStack(spacing: 0) {
            // 任务页面头部工具栏
            HStack {
                Text("今日任务")
                    .font(.title2)
                    .fontWeight(.bold)

                Spacer()

                // 当前已用时显示
                VStack(alignment: .trailing, spacing: 2) {
                    Text("当前已用时")
                        .font(.system(size: 11, weight: .medium, design: .rounded))
                        .foregroundColor(Color(red: 142/255, green: 142/255, blue: 147/255))

                    Text(formattedTodayTotalUsedTime)
                        .font(.system(size: 13, weight: .semibold, design: .rounded))
                        .foregroundColor(Color(red: 255/255, green: 149/255, blue: 0/255))
                }
                .padding(.trailing, 16)

                // 强制刷新按钮
                Button(action: {
                    print("🔄 用户触发强制刷新")
                    // 🔥 关键修复：使用专门的刷新方法
                    refreshDataForTab()
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 16)
            .background(.ultraThinMaterial)

            // 今日任务进度条
            if !tasks.isEmpty {
                VStack(spacing: 0) {
                    DailyTaskProgressBar(tasks: tasks, showPercentage: true)
                        .padding(.horizontal, 32)
                        .padding(.vertical, 12)
                }
                .background(.ultraThinMaterial)
            }

            // 任务内容
            Group {
                if showTaskDetail {
                    // 任务详情页
                    taskDetailView()
                } else {
                    // 任务列表
                    taskListView()
                }
            }
        }
        .onAppear {
            print("📋 任务页面出现，开始加载数据...")
            loadTasksData()
        }
    }

    private func taskListView() -> some View {
        Group {
            if isLoading {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("加载任务中...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let error = errorMessage {
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                    Text("加载失败")
                        .font(.headline)
                    Text(error)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    Button("重试") {
                        loadTasks() // 只重新加载任务，不重新加载所有数据
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(.horizontal, 32)
            } else if tasks.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "calendar.badge.exclamationmark")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                    Text("今天没有需要完成的任务")
                        .font(.headline)
                    Text("请联系家长安排今日任务")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 20),
                    GridItem(.flexible(), spacing: 20)
                ], spacing: 20) {
                    ForEach(tasks, id: \.id) { task in
                        TaskCard(
                            title: task.title,
                            description: task.formattedTimeInfo,
                            points: task.basePoints,
                            status: task.displayStatus,
                            onToggle: {
                                toggleTaskCompletion(task)
                            },
                            onTap: {
                                // 点击查看详情
                                selectedTask = task
                                showTaskDetail = true
                            }
                        )
                    }
                }
                .padding(.horizontal, 32)
                .padding(.bottom, 32)
            }
        }
    }

    private func taskDetailView() -> some View {
        Group {
            if let selectedTask = selectedTask {
                TaskDetailPageView(
                    task: selectedTask,
                    onBack: {
                        showTaskDetail = false
                        self.selectedTask = nil
                    },
                    onTaskUpdated: { updatedTask in
                        // 同步更新任务列表中的对应任务
                        updateTaskInList(updatedTask)
                    }
                )
            } else {
                Text("未选择任务")
                    .font(.title)
                    .foregroundColor(.secondary)
            }
        }
    }

    // 转换TaskSummary为TaskModel
    private func convertToTaskModel(_ summary: TaskSummary) -> TaskModel {
        return TaskModel(
            id: summary.id,
            sourceTemplateId: summary.sourceTemplateId,
            title: summary.title,
            description: summary.description,
            expectedMinutes: summary.expectedMinutes,
            basePoints: summary.basePoints,
            dueTime: summary.dueTime,
            dueDate: summary.dueDate,
            status: summary.status,
            taskType: summary.taskType,
            startTime: summary.startTime,
            endTime: summary.endTime,
            actualPoints: summary.actualPoints,
            createdTime: summary.createdTime,
            scheduledDate: summary.scheduledDate,
            actualMinutes: summary.actualMinutes
        )
    }

    // 同步更新任务列表中的任务状态
    private func updateTaskInList(_ updatedTask: TaskSummary) {
        print("🔄 ContentView: 同步任务状态: \(updatedTask.title) -> \(updatedTask.status)")

        // 更新任务列表中对应的任务
        if let index = tasks.firstIndex(where: { $0.id == updatedTask.id }) {
            tasks[index] = updatedTask
            print("✅ ContentView: 任务列表状态已同步")
        }

        // 更新选中的任务
        if selectedTask?.id == updatedTask.id {
            selectedTask = updatedTask
        }
    }

    // 处理任务操作（保留用于其他地方的直接操作）
    private func handleTaskAction(_ action: TaskAction, for task: TaskSummary) {
        switch action {
        case .start:
            startTask(task)
        case .complete:
            completeTask(task)
        case .viewDetail:
            break // 已经在详情页
        }
    }

    private func pointsContent() -> some View {
        ScrollView {
            VStack(spacing: 20) {
                // 积分统计卡片
                pointsStatsCards()

                // 筛选器
                pointsFilters()

                // 积分记录列表
                pointsRecordsList()
            }
            .padding(20)
        }
        .onAppear {
            loadPointsData()
        }
    }

    private func rewardsContent() -> some View {
        EnhancedRewardView()
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private func exchangeContent() -> some View {
        ExchangeStoreView(rewardViewModel: rewardViewModel)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 积分页面组件

    private func pointsStatsCards() -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
            // 当前积分
            StatCard(
                title: "当前积分",
                value: "\(pointBalance?.totalPoints ?? 0)",
                color: .blue,
                icon: "star.fill"
            )

            // 今日变化
            StatCard(
                title: "今日变化",
                value: todayPointChange?.formattedChange ?? "+0",
                color: (todayPointChange?.isPositive ?? true) ? .green : .red,
                icon: "arrow.up.right"
            )

            // 总获得
            StatCard(
                title: "总获得",
                value: "+\(pointsStats.totalEarned)",
                color: .green,
                icon: "plus.circle.fill"
            )

            // 总消费
            StatCard(
                title: "总消费",
                value: "-\(pointsStats.totalSpent)",
                color: .orange,
                icon: "minus.circle.fill"
            )
        }
    }

    private func pointsFilters() -> some View {
        VStack(spacing: 16) {
            // 时间范围筛选器
            HStack {
                Text("时间范围")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Picker("时间范围", selection: $pointsTimeRange) {
                    Text("7天").tag(7)
                    Text("30天").tag(30)
                    Text("90天").tag(90)
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 200)
            }

            // 记录类型筛选器
            HStack {
                Text("记录类型")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Picker("记录类型", selection: $pointsFilter) {
                    Text("全部").tag("ALL")
                    Text("任务完成").tag("TASK_COMPLETION")
                    Text("奖励兑换").tag("REWARD_EXCHANGE")
                    Text("惩罚扣分").tag("TASK_PENALTY")
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 300)
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .onChange(of: pointsTimeRange) { _ in
            filterPointRecords()
        }
        .onChange(of: pointsFilter) { _ in
            filterPointRecords()
        }
    }

    private func pointsRecordsList() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("积分记录")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("共 \(filteredPointRecords.count) 条记录")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if filteredPointRecords.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "star.slash")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)

                    Text("暂无积分记录")
                        .font(.title3)
                        .foregroundColor(.secondary)

                    Text("完成任务或兑换奖励后会显示记录")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(40)
                .background(.ultraThinMaterial)
                .cornerRadius(16)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(filteredPointRecords, id: \.id) { record in
                        pointRecordRow(record)
                    }
                }
            }
        }
    }

    private func pointRecordRow(_ record: PointRecord) -> some View {
        HStack(spacing: 16) {
            // 记录类型图标
            Image(systemName: record.typeIcon)
                .font(.title2)
                .foregroundColor(record.typeColor)
                .frame(width: 32, height: 32)
                .background(record.typeColor.opacity(0.1))
                .cornerRadius(8)

            // 记录信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.description)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(record.formattedTime)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.1))
                    .cornerRadius(4)
            }

            Spacer()

            // 积分变化
            Text(record.formattedPointChange)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(record.pointChange > 0 ? .green : .red)
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }

    // MARK: - 积分页面数据加载

    private func loadPointsData() {
        print("📊 强制重新加载积分页面数据...")

        // 清除积分相关的缓存订阅
        pointsCancellables.removeAll()

        // 重置积分相关状态
        pointRecords = []
        filteredPointRecords = []
        pointBalance = nil
        todayPointChange = nil

        // 强制重新加载积分记录
        apiService.getPointRecords()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载积分记录失败: \(error)")
                        print("❌ 错误详情: \(error.localizedDescription)")
                    }
                },
                receiveValue: { records in
                    print("📊 收到积分记录响应: \(records.count)条记录")
                    self.pointRecords = records
                    self.pointsStats = PointsStatistics.calculate(from: records)
                    self.filterPointRecords()
                    print("✅ 成功加载积分记录: \(records.count)条")

                    // 打印前几条记录的详细信息用于调试
                    for (index, record) in records.prefix(3).enumerated() {
                        print("📝 记录\(index + 1): ID=\(record.id), 积分=\(record.pointChange), 描述=\(record.description), 时间=\(record.recordTime)")
                    }
                }
            )
            .store(in: &pointsCancellables)

        // 强制重新加载积分余额
        let balanceRequestId = UUID().uuidString.prefix(8)
        print("💰 [余额请求ID: \(balanceRequestId)] 开始强制加载积分余额...")

        apiService.getPointBalance()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .failure(let error):
                        print("❌ [余额请求ID: \(balanceRequestId)] 加载积分余额失败: \(error)")
                        print("❌ [余额请求ID: \(balanceRequestId)] 错误详情: \(error.localizedDescription)")
                    case .finished:
                        print("✅ [余额请求ID: \(balanceRequestId)] 积分余额请求完成")
                    }
                },
                receiveValue: { balance in
                    print("💰 [余额请求ID: \(balanceRequestId)] 收到积分余额响应: \(balance)")
                    print("💰 [余额请求ID: \(balanceRequestId)] balance.totalPoints = \(balance.totalPoints)")
                    print("💰 [余额请求ID: \(balanceRequestId)] balance.total = \(balance.total)")
                    self.pointBalance = balance
                    // 同时更新主页面的积分显示
                    print("💰 [余额请求ID: \(balanceRequestId)] 更新前主页面积分: \(self.totalPoints)")
                    self.totalPoints = balance.total
                    print("💰 [余额请求ID: \(balanceRequestId)] 更新后主页面积分: \(self.totalPoints)")
                    print("✅ [余额请求ID: \(balanceRequestId)] 成功加载积分余额: \(balance.totalPoints)")
                }
            )
            .store(in: &pointsCancellables)

        // 强制重新加载今日积分变化
        apiService.getTodayPointChange()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ 加载今日积分变化失败: \(error)")
                        print("❌ 错误详情: \(error.localizedDescription)")
                    }
                },
                receiveValue: { pointChange in
                    print("📊 收到今日积分变化响应: \(pointChange)")
                    self.todayPointChange = pointChange
                    // 同时更新主页面的今日积分显示
                    self.todayPoints = pointChange.todayChange
                    print("✅ 成功加载今日积分变化: \(pointChange.todayChange)")
                    print("✅ 主页面今日积分已更新为: \(self.todayPoints)")
                }
            )
            .store(in: &pointsCancellables)
    }

    // MARK: - 各页面数据加载函数

    private func loadTasksData() {
        print("📋 强制重新加载任务页面数据...")
        print("📋 当前任务数量: \(tasks.count)")

        // 🔥 强制清除任务相关缓存
        taskCancellables.removeAll()
        tasks = []

        // 🔥 强制重新加载任务数据
        loadTasks()
    }

    private func loadRewardsData() {
        print("🎁 开始加载奖励页面数据...")
        // TODO: 实现奖励数据加载
    }

    private func loadExchangeData() {
        print("🛒 开始加载兑换页面数据...")
        // TODO: 实现兑换数据加载
    }

    private func filterPointRecords() {
        print("🔍 开始筛选积分记录，原始记录数: \(pointRecords.count)")

        // 打印所有记录的changeType，用于调试
        print("📊 所有记录的changeType:")
        for (index, record) in pointRecords.prefix(10).enumerated() {
            print("📝 记录\(index + 1): ID=\(record.id), 积分=\(record.pointChange), 描述=\(record.description), changeType=\(record.changeType ?? "nil")")
        }

        // 暂时简化筛选逻辑，先确保记录能显示
        // TODO: 后续添加时间筛选功能

        // 根据记录类型筛选 - 使用更灵活的匹配逻辑
        let typeFilteredRecords: [PointRecord]

        if pointsFilter == "ALL" {
            typeFilteredRecords = pointRecords
        } else {
            typeFilteredRecords = pointRecords.filter { record in
                guard let changeType = record.changeType else { return false }

                // 根据筛选类型进行匹配
                switch pointsFilter {
                case "TASK_COMPLETION":
                    // 任务完成相关：正积分的记录
                    return record.pointChange > 0 && (changeType.contains("TASK") || changeType.contains("COMPLETE") || changeType.contains("APPROVE"))
                case "REWARD_EXCHANGE":
                    // 奖励兑换相关：负积分且与兑换相关
                    return record.pointChange < 0 && (changeType.contains("EXCHANGE") || changeType.contains("REWARD") || changeType.contains("DRAW"))
                case "TASK_PENALTY":
                    // 惩罚扣分：负积分且不是兑换相关的
                    return record.pointChange < 0 && !changeType.contains("EXCHANGE") && !changeType.contains("REWARD") && !changeType.contains("DRAW")
                default:
                    return changeType == pointsFilter
                }
            }
        }

        print("🏷️ 类型筛选后: \(typeFilteredRecords.count)条记录")

        // 按ID倒序排列（最新的记录在前）
        filteredPointRecords = typeFilteredRecords.sorted { $0.id > $1.id }

        print("🔍 筛选结果: \(filteredPointRecords.count)条记录 (类型:\(pointsFilter))")

        // 打印筛选后的记录详细信息
        for (index, record) in filteredPointRecords.prefix(3).enumerated() {
            print("📝 筛选后记录\(index + 1): ID=\(record.id), 积分=\(record.pointChange), 描述=\(record.description), changeType=\(record.changeType ?? "nil")")
        }
    }


}

// MARK: - 辅助组件

// 导航按钮
struct NavigationButton: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .frame(width: 24)

                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                Spacer()
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                isSelected ?
                LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing) :
                LinearGradient(colors: [.clear], startPoint: .leading, endPoint: .trailing)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 统计行
struct StatRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)

            Text(title)
                .font(.subheadline)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
        }
    }
}





// 仪表板卡片
struct DashboardCard<Content: View>: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片头部
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 32, height: 32)
                    .background(color.opacity(0.1))
                    .cornerRadius(8)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // 卡片内容
            content()
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
    }
}

// 任务卡片
struct TaskCard: View {
    let title: String
    let description: String
    let points: Int
    let status: TaskStatus
    let onToggle: () -> Void
    let onTap: () -> Void

    // 根据状态计算显示属性
    private var statusInfo: (icon: String, color: Color, text: String) {
        switch status {
        case .notStarted:
            return ("circle", .gray, "未开始")
        case .pending:
            return ("clock", .orange, "待审批")  // 修改为"待审批"
        case .inProgress:
            return ("play.circle.fill", .blue, "进行中")
        case .completed:
            return ("checkmark.circle.fill", .green, "已完成")
        case .approved:
            return ("checkmark.circle.fill", .green, "已完成")
        case .overdue:
            return ("exclamationmark.circle.fill", .red, "已过期")
        case .cancelled:
            return ("xmark.circle.fill", .gray, "已取消")
        case .rejected:
            return ("xmark.circle.fill", .red, "已作废")
        case .penaltyApproval:
            return ("exclamationmark.triangle.fill", .purple, "惩罚审批")
        }
    }

    private var isCompleted: Bool {
        return status == .completed || status == .approved
    }

    var body: some View {
        HStack(spacing: 16) {
            // 状态按钮
            Button(action: onToggle) {
                Image(systemName: statusInfo.icon)
                    .font(.title2)
                    .foregroundColor(statusInfo.color)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .strikethrough(status == .approved)

                if !description.isEmpty {
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                // 状态标签
                Text(statusInfo.text)
                    .font(.caption)
                    .foregroundColor(statusInfo.color)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(statusInfo.color.opacity(0.1))
                    .cornerRadius(4)

                // 移除单独的进度条，因为顶部已有整体进度条
            }

            Spacer()

            // 积分奖励
            HStack(spacing: 4) {
                Image(systemName: "star.fill")
                    .font(.caption)
                    .foregroundColor(.orange)

                Text("\(points)")
                    .font(.footnote)
                    .foregroundColor(.primary)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(.systemGray6))
            .cornerRadius(8)

            // 详情箭头
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        .opacity(status == .approved ? 0.7 : 1.0)
        .onTapGesture {
            onTap()
        }
    }
}

// TaskStepRow已删除，因为不再使用TaskStep

// 任务详情卡片
struct TaskDetailCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片标题
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            // 卡片内容
            content()
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
        .padding(.horizontal, 32)
    }
}

// MARK: - 任务详情页面
struct TaskDetailPageView: View {
    @State private var task: TaskSummary
    let onBack: () -> Void
    let onTaskUpdated: (TaskSummary) -> Void

    @State private var taskDetail: TaskModel?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var cancellables = Set<AnyCancellable>()

    // 操作状态管理
    @State private var isPerformingAction = false

    // 初始化器
    init(task: TaskSummary, onBack: @escaping () -> Void, onTaskUpdated: @escaping (TaskSummary) -> Void) {
        self._task = State(initialValue: task)
        self.onBack = onBack
        self.onTaskUpdated = onTaskUpdated
    }

    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            HStack {
                Button(action: onBack) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                        Text("返回")
                    }
                    .foregroundColor(.blue)
                }

                Spacer()

                Text("任务详情")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                // 占位，保持居中
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                    Text("返回")
                }
                .opacity(0)
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 16)

            if isLoading {
                Spacer()
                ProgressView("加载中...")
                    .font(.title2)
                Spacer()
            } else if let errorMessage = errorMessage {
                Spacer()
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 48))
                        .foregroundColor(.orange)
                    Text(errorMessage)
                        .font(.title2)
                        .multilineTextAlignment(.center)
                    Button("重试") {
                        loadTaskDetail()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
                Spacer()
            } else {
                ScrollView {
                    VStack(spacing: 24) {
                        // 任务基本信息
                        TaskDetailCard(
                            title: "任务信息",
                            icon: "info.circle.fill",
                            color: .blue,
                            content: {
                                VStack(alignment: .leading, spacing: 16) {
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("任务标题")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        Text(task.title)
                                            .font(.title2)
                                            .fontWeight(.bold)
                                    }

                                    HStack(spacing: 20) {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text("奖励积分")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                            HStack(spacing: 4) {
                                                Image(systemName: "star.fill")
                                                    .foregroundColor(.orange)
                                                Text("\(task.basePoints)")
                                                    .font(.title3)
                                                    .fontWeight(.semibold)
                                            }
                                        }

                                        VStack(alignment: .leading, spacing: 4) {
                                            Text("预计时间")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                            HStack(spacing: 4) {
                                                Image(systemName: "clock")
                                                    .foregroundColor(.blue)
                                                Text("\(task.expectedMinutes)分钟")
                                                    .font(.subheadline)
                                                    .fontWeight(.medium)
                                            }
                                        }

                                        if let actualMinutes = task.actualMinutes {
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("实际时间")
                                                    .font(.subheadline)
                                                    .foregroundColor(.secondary)
                                                HStack(spacing: 4) {
                                                    Image(systemName: "timer")
                                                        .foregroundColor(.green)
                                                    Text("\(actualMinutes)分钟")
                                                        .font(.subheadline)
                                                        .fontWeight(.medium)
                                                }
                                            }
                                        }

                                        Spacer()

                                        VStack(alignment: .trailing, spacing: 4) {
                                            Text("任务状态")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                            HStack(spacing: 4) {
                                                Image(systemName: task.displayStatus.iconName)
                                                    .foregroundColor(task.displayStatus.color)
                                                Text(task.displayStatus.displayName)
                                                    .font(.subheadline)
                                                    .fontWeight(.medium)
                                            }
                                        }
                                    }
                                }
                            }
                        )

                        // 操作按钮 - 移到任务描述前面
                        actionButtons

                        // 任务详细描述
                        let displayDescription = taskDetail?.description ?? task.description
                        if let description = displayDescription, !description.isEmpty {
                            TaskDetailCard(
                                title: "任务详情",
                                icon: "doc.text.fill",
                                color: .green,
                                content: {
                                    VStack(alignment: .leading, spacing: 8) {
                                        // 使用富文本组件渲染HTML内容
                                        RichTextView(description, font: .body, color: .primary)
                                    }
                                }
                            )
                        }
                    }
                    .padding(.bottom, 32)
                }
            }
        }
        .onAppear {
            loadTaskDetail()
        }
    }

    @ViewBuilder
    private var actionButtons: some View {
        VStack(spacing: 12) {
            switch task.displayStatus {
            case .notStarted, .pending:
                if task.actualMinutes == nil {
                    // 未开始的任务
                    Button(action: {
                        handleTaskAction(.start)
                    }) {
                        HStack(spacing: 12) {
                            if isPerformingAction {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "play.circle.fill")
                                    .font(.title3)
                            }
                            Text(isPerformingAction ? "处理中..." : "开始任务")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                colors: isPerformingAction ? [.gray, .gray] : [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(16)
                    }
                    .disabled(isPerformingAction)
                } else {
                    // 已完成等待审批
                    HStack(spacing: 12) {
                        Image(systemName: "clock.fill")
                            .font(.title3)
                            .foregroundColor(.orange)
                        Text("等待审批")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                    .cornerRadius(16)
                }

            case .inProgress:
                Button(action: {
                    handleTaskAction(.complete)
                }) {
                    HStack(spacing: 12) {
                        if isPerformingAction {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title3)
                        }
                        Text(isPerformingAction ? "处理中..." : "完成任务")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            colors: isPerformingAction ? [.gray, .gray] : [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(16)
                }
                .disabled(isPerformingAction)

            case .completed, .approved:
                HStack(spacing: 12) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.green)
                    Text("任务已完成")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(.ultraThinMaterial)
                .cornerRadius(16)

            case .overdue, .cancelled, .rejected:
                HStack(spacing: 12) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.red)
                    Text("任务已结束")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.red)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(.ultraThinMaterial)
                .cornerRadius(16)

            case .penaltyApproval:
                HStack(spacing: 12) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.title3)
                        .foregroundColor(.purple)
                    Text("惩罚审批中")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(.ultraThinMaterial)
                .cornerRadius(16)
            }
        }
        .padding(.horizontal, 32)
    }

    private func loadTaskDetail() {
        isLoading = true
        errorMessage = nil

        print("🔍 TaskDetailPageView: 开始加载任务详情，任务ID: \(task.id)")
        print("🔍 TaskDetailPageView: 任务基本信息 - 标题: \(task.title)")
        print("🔍 TaskDetailPageView: 任务基本信息 - 描述: \(task.description ?? "无")")

        // 调用真实的API
        ChildAPIService.shared.getTaskDetail(taskId: task.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("❌ TaskDetailPageView: API调用失败: \(error)")
                        self.errorMessage = "加载任务详情失败: \(error.localizedDescription)"
                        self.isLoading = false
                    }
                },
                receiveValue: { detail in
                    print("✅ TaskDetailPageView: API调用成功，获取到详细信息")
                    print("📝 TaskDetailPageView: 详细描述: \(detail.description ?? "无")")
                    self.taskDetail = detail
                    self.isLoading = false
                }
            )
            .store(in: &cancellables)
    }

    // 处理任务操作
    private func handleTaskAction(_ action: TaskAction) {
        switch action {
        case .start:
            startTaskInDetail()
        case .complete:
            completeTaskInDetail()
        case .viewDetail:
            break // 已经在详情页
        }
    }

    private func startTaskInDetail() {
        print("🚀 TaskDetailPageView: 开始任务: \(task.title)")

        // 设置操作状态
        isPerformingAction = true
        errorMessage = nil

        ChildAPIService.shared.startTask(taskId: task.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isPerformingAction = false
                    if case .failure(let error) = completion {
                        let errorMsg = "开始任务失败: \(error.localizedDescription)"
                        print("❌ TaskDetailPageView: \(errorMsg)")
                        self.errorMessage = errorMsg
                    }
                },
                receiveValue: { updatedTask in
                    print("✅ TaskDetailPageView: 任务开始成功")

                    // 任务开始成功后，启动息屏功能
                    print("🔘 TaskDetailPageView: 任务开始成功，启动息屏功能")

                    // 启动正常的息屏倒计时
                    ScreenDimmingManager.shared.startDimmingTimer()
                    print("🔘 TaskDetailPageView: 已启动息屏倒计时")

                    // 直接更新任务状态 - 这是最好的反馈
                    let updatedTaskSummary = TaskSummary(
                        id: self.task.id,
                        sourceTemplateId: self.task.sourceTemplateId,
                        title: self.task.title,
                        description: self.task.description,
                        expectedMinutes: self.task.expectedMinutes,
                        basePoints: self.task.basePoints,
                        dueTime: self.task.dueTime,
                        dueDate: self.task.dueDate,
                        status: .inProgress,  // 更新状态为进行中
                        taskType: self.task.taskType,
                        startTime: self.task.startTime,
                        endTime: self.task.endTime,
                        actualPoints: self.task.actualPoints,
                        createdTime: self.task.createdTime,
                        scheduledDate: self.task.scheduledDate,
                        actualMinutes: self.task.actualMinutes
                    )
                    self.task = updatedTaskSummary

                    // 通知父组件同步状态（不再触发API调用）
                    self.onTaskUpdated(updatedTaskSummary)
                }
            )
            .store(in: &cancellables)
    }

    private func completeTaskInDetail() {
        print("🎯 TaskDetailPageView: 完成任务: \(task.title)")

        // 设置操作状态
        isPerformingAction = true
        errorMessage = nil

        ChildAPIService.shared.completeTask(taskId: task.id)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isPerformingAction = false
                    if case .failure(let error) = completion {
                        let errorMsg = "完成任务失败: \(error.localizedDescription)"
                        print("❌ TaskDetailPageView: \(errorMsg)")
                        self.errorMessage = errorMsg
                    }
                },
                receiveValue: { updatedTask in
                    print("✅ TaskDetailPageView: 任务完成成功")

                    // 直接更新任务状态 - 这是最好的反馈
                    let updatedTaskSummary = TaskSummary(
                        id: self.task.id,
                        sourceTemplateId: self.task.sourceTemplateId,
                        title: self.task.title,
                        description: self.task.description,
                        expectedMinutes: self.task.expectedMinutes,
                        basePoints: self.task.basePoints,
                        dueTime: self.task.dueTime,
                        dueDate: self.task.dueDate,
                        status: .completed,  // 更新状态为已完成
                        taskType: self.task.taskType,
                        startTime: self.task.startTime,
                        endTime: self.task.endTime,
                        actualPoints: self.task.actualPoints,
                        createdTime: self.task.createdTime,
                        scheduledDate: self.task.scheduledDate,
                        actualMinutes: self.task.actualMinutes
                    )
                    self.task = updatedTaskSummary

                    // 通知父组件同步状态（不再触发API调用）
                    self.onTaskUpdated(updatedTaskSummary)
                }
            )
            .store(in: &cancellables)
    }
}

// 积分变化行组件
struct PointChangeRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)

            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}

// 积分记录行组件
struct PointRecordRow: View {
    let record: PointRecord

    var body: some View {
        HStack(spacing: 12) {
            // 图标
            Text(record.displayIcon)
                .font(.system(size: 16))
                .frame(width: 20)

            // 描述和时间
            VStack(alignment: .leading, spacing: 4) {
                Text(record.description)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(1)

                Text(record.formattedDate)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 1)
                    .background(Color.secondary.opacity(0.1))
                    .cornerRadius(3)
            }

            Spacer()

            // 积分变化
            Text(record.formattedChange)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(record.isPositive ? .green : .red)
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 4)
    }
}

// MARK: - 兑换商店视图（iOS风格设计）
struct ExchangeStoreView: View {
    @ObservedObject var rewardViewModel: RewardViewModel

    @State private var selectedCategory: String? = nil
    @State private var showingExchangeAlert = false
    @State private var selectedItem: ExchangeItem? = nil
    @State private var showOnlyAffordable = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 筛选器区域
                filterSection

                // 商品网格
                itemsGridSection
            }
            .padding(20)
        }
        .background(Color(.systemGroupedBackground))
        .alert("兑换确认", isPresented: $showingExchangeAlert) {
            exchangeAlert
        }
        .alert("操作结果", isPresented: .constant(rewardViewModel.successMessage != nil)) {
            Button("确定") {
                rewardViewModel.clearMessages()
            }
        } message: {
            if let message = rewardViewModel.successMessage {
                Text(message)
            }
        }
        .alert("错误", isPresented: .constant(rewardViewModel.errorMessage != nil)) {
            Button("确定") {
                rewardViewModel.clearMessages()
            }
        } message: {
            if let message = rewardViewModel.errorMessage {
                Text(message)
            }
        }
        .onAppear {
            rewardViewModel.loadData()
        }
    }

    // MARK: - 子视图



    // 筛选器区域
    private var filterSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 分类筛选器（包含可兑换过滤器）
            if !rewardViewModel.availableCategories.isEmpty {
                categoryFilterSection
            }
        }
    }



    // 分类筛选器（iOS风格）
    private var categoryFilterSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题行：商品分类 + 可兑换筛选按钮
            HStack {
                Text("商品分类")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                // 可兑换过滤按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        showOnlyAffordable.toggle()
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: showOnlyAffordable ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(showOnlyAffordable ? .green : .gray)

                        Text("仅显示可兑换")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(showOnlyAffordable ? .green : .primary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(showOnlyAffordable ? Color.green.opacity(0.1) : Color(.systemGray6))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(showOnlyAffordable ? Color.green : Color.clear, lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    CategoryFilterChip(
                        title: "全部",
                        isSelected: selectedCategory == nil
                    ) {
                        selectedCategory = nil
                    }

                    ForEach(rewardViewModel.availableCategories, id: \.self) { category in
                        CategoryFilterChip(
                            title: category,
                            isSelected: selectedCategory == category
                        ) {
                            selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }

    // 商品网格区域（iOS风格）
    private var itemsGridSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("兑换商品")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                if !filteredExchangeItems.isEmpty {
                    Text("共 \(filteredExchangeItems.count) 件商品")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            if rewardViewModel.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)

                    Text("加载中...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else if filteredExchangeItems.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "gift.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)

                    Text("暂无可兑换的商品")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    Text("请稍后再来看看吧！")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
                .padding(20)
                .background(.ultraThinMaterial)
                .cornerRadius(16)
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 16),
                    GridItem(.flexible(), spacing: 16)
                ], spacing: 16) {
                    ForEach(filteredExchangeItems, id: \.id) { item in
                        ExchangeItemCardIOS(
                            item: item,
                            userPoints: rewardViewModel.totalPoints,
                            onExchange: {
                                selectedItem = item
                                showingExchangeAlert = true
                            }
                        )
                    }
                }
            }
        }
    }

    // 筛选后的兑换商品
    private var filteredExchangeItems: [ExchangeItem] {
        var items = rewardViewModel.getExchangeItems(for: selectedCategory)

        // 如果启用了可兑换筛选，只显示用户积分足够且可兑换的商品
        if showOnlyAffordable {
            items = items.filter { item in
                return rewardViewModel.totalPoints >= item.requiredPoints && item.canExchange
            }
        }

        return items
    }

    // 兑换确认弹窗
    private var exchangeAlert: some View {
        Group {
            if let item = selectedItem {
                Button("确认兑换") {
                    rewardViewModel.exchangeItem(item)
                    selectedItem = nil
                }

                Button("取消", role: .cancel) {
                    selectedItem = nil
                }
            }
        }
    }
}

// MARK: - iOS风格兑换商品卡片
struct ExchangeItemCardIOS: View {
    let item: ExchangeItem
    let userPoints: Int
    let onExchange: () -> Void

    private var canExchange: Bool {
        return userPoints >= item.requiredPoints && item.canExchange
    }

    private var categoryIcon: String {
        switch item.category {
        case "游戏时间": return "gamecontroller.fill"
        case "特权": return "crown.fill"
        case "实物奖励": return "gift.fill"
        case "零花钱": return "dollarsign.circle.fill"
        default: return "star.fill"
        }
    }

    private var categoryColor: Color {
        switch item.category {
        case "游戏时间": return .blue
        case "特权": return .purple
        case "实物奖励": return .orange
        case "零花钱": return .green
        default: return .gray
        }
    }

    var body: some View {
        VStack(spacing: 16) {
            // 商品图标和名称
            VStack(spacing: 12) {
                Image(systemName: categoryIcon)
                    .font(.system(size: 32))
                    .foregroundColor(categoryColor)
                    .frame(width: 48, height: 48)
                    .background(categoryColor.opacity(0.1))
                    .cornerRadius(12)

                Text(item.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }

            // 商品描述
            if let description = item.description, !description.isEmpty {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            // 商品信息
            VStack(spacing: 8) {
                HStack {
                    Text("所需积分")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                        Text("\(item.requiredPoints)")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                    }
                }

                HStack {
                    Text("库存状态")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text(item.stock == -1 ? "无限库存" : "库存 \(item.stock ?? 0)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(item.hasStock ? .green : .red)
                }
            }

            // 兑换按钮
            Button(action: onExchange) {
                HStack {
                    Image(systemName: "cart.fill")
                        .font(.subheadline)

                    Text(getButtonText())
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(canExchange ? categoryColor : Color.gray)
                .cornerRadius(12)
            }
            .disabled(!canExchange)
            .buttonStyle(PlainButtonStyle())
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
        .scaleEffect(canExchange ? 1.0 : 0.95)
        .opacity(canExchange ? 1.0 : 0.7)
        .animation(.easeInOut(duration: 0.2), value: canExchange)
    }

    private func getButtonText() -> String {
        if !item.isActive {
            return "已下架"
        } else if !item.hasStock {
            return "缺货"
        } else if userPoints < item.requiredPoints {
            return "积分不足"
        } else {
            return "立即兑换"
        }
    }
}

// MARK: - iOS风格分类筛选按钮
struct CategoryFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ?
                    Color.blue :
                    Color(.systemGray6)
                )
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}









// MARK: - 奖励中心内容视图
struct RewardCenterContentView: View {
    @State private var currentPoints: Int = 0
    @State private var isLoading = false
    @State private var showingDrawResult = false
    @State private var drawResultMessage = ""

    // API服务和网络请求管理
    private let apiService = ChildAPIService.shared
    @State private var pointsCancellables = Set<AnyCancellable>()

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 奖励区域
                if currentPoints >= 0 {
                    rewardSection
                } else {
                    penaltySection
                }

                // 功能说明
                featureDescriptionCard

                Spacer(minLength: 32)
            }
            .padding(32)
        }
        .background(Color(.systemGroupedBackground))
        .onAppear {
            loadCurrentPoints()
        }
        .alert("抽奖结果", isPresented: $showingDrawResult) {
            Button("确定") {
                showingDrawResult = false
                loadCurrentPoints() // 重新加载积分
            }
        } message: {
            Text(drawResultMessage)
        }
    }



    // MARK: - 奖励区域
    private var rewardSection: some View {
        VStack(spacing: 20) {
            HStack {
                Image(systemName: "gift.fill")
                    .font(.title2)
                    .foregroundColor(.blue)

                Text("🎁 奖励区")
                    .font(.title3)
                    .fontWeight(.semibold)

                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                RewardPoolCardView(
                    title: "每日奖励池",
                    icon: "gift.fill",
                    color: .blue,
                    costPoints: 50,
                    description: "完成日常任务获得奖励",
                    canDraw: currentPoints >= 50,
                    isLoading: isLoading
                ) {
                    drawReward(poolName: "每日奖励池", cost: 50)
                }

                RewardPoolCardView(
                    title: "特殊奖励池",
                    icon: "star.fill",
                    color: .purple,
                    costPoints: 100,
                    description: "超级奖励等你来抽",
                    canDraw: currentPoints >= 100,
                    isLoading: isLoading
                ) {
                    drawReward(poolName: "特殊奖励池", cost: 100)
                }
            }
        }
    }

    // MARK: - 惩罚区域
    private var penaltySection: some View {
        VStack(spacing: 20) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.title2)
                    .foregroundColor(.orange)

                Text("⚡ 惩罚区")
                    .font(.title3)
                    .fontWeight(.semibold)

                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                RewardPoolCardView(
                    title: "轻度惩罚",
                    icon: "exclamationmark.triangle.fill",
                    color: .orange,
                    costPoints: 20,
                    description: "执行简单的惩罚任务",
                    canDraw: true,
                    isLoading: isLoading,
                    isPenalty: true
                ) {
                    executePenalty(penaltyName: "轻度惩罚", reward: 20)
                }

                RewardPoolCardView(
                    title: "重度惩罚",
                    icon: "exclamationmark.octagon.fill",
                    color: .red,
                    costPoints: 50,
                    description: "执行较难的惩罚任务",
                    canDraw: currentPoints <= -50,
                    isLoading: isLoading,
                    isPenalty: true
                ) {
                    executePenalty(penaltyName: "重度惩罚", reward: 50)
                }
            }
        }
    }

    // MARK: - 数据加载和操作
    private func loadCurrentPoints() {
        // 使用真实API加载积分
        apiService.getPointBalance()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        print("❌ 加载积分失败: \(error)")
                        self.currentPoints = 0
                    }
                },
                receiveValue: { balance in
                    self.currentPoints = balance.totalPoints
                    self.isLoading = false
                }
            )
            .store(in: &pointsCancellables)
        isLoading = true
    }

    private func drawReward(poolName: String, cost: Int) {
        guard currentPoints >= cost && !isLoading else { return }

        isLoading = true

        // TODO: 对接真实的抽奖API
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 临时使用模拟结果，待后端API完成后替换
            let rewards = ["🍭 糖果", "🧸 玩具熊", "📚 故事书", "🎮 游戏时间", "🎬 电影票"]
            let reward = rewards.randomElement() ?? "🎁 神秘奖品"

            self.currentPoints -= cost
            self.drawResultMessage = "恭喜从\(poolName)中获得：\(reward)！"
            self.showingDrawResult = true
            self.isLoading = false
        }
    }

    private func executePenalty(penaltyName: String, reward: Int) {
        guard !isLoading else { return }

        isLoading = true

        // TODO: 对接真实的惩罚任务API
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 临时使用模拟结果，待后端API完成后替换
            let penalties = ["🧹 打扫房间", "📖 背诵古诗", "🏃‍♂️ 跑步10分钟", "🧽 洗碗", "📝 写检讨书"]
            let penalty = penalties.randomElement() ?? "🔄 额外任务"

            self.currentPoints += reward
            self.drawResultMessage = "执行\(penaltyName)：\(penalty)，获得\(reward)积分！"
            self.showingDrawResult = true
            self.isLoading = false
        }
    }

    // MARK: - 功能说明卡片
    private var featureDescriptionCard: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "info.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)

                Text("功能说明")
                    .font(.title3)
                    .fontWeight(.semibold)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 12) {
                FeatureDescriptionRow(
                    icon: "plus.circle.fill",
                    color: .green,
                    title: "积分为正",
                    description: "可以抽取奖励，消耗积分获得惊喜"
                )

                FeatureDescriptionRow(
                    icon: "minus.circle.fill",
                    color: .red,
                    title: "积分为负",
                    description: "需要执行惩罚任务，完成后获得积分"
                )

                FeatureDescriptionRow(
                    icon: "arrow.clockwise.circle.fill",
                    color: .blue,
                    title: "动态平衡",
                    description: "通过奖励和惩罚机制保持积分平衡"
                )
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
    }
}

// MARK: - 奖励池卡片视图
struct RewardPoolCardView: View {
    let title: String
    let icon: String
    let color: Color
    let costPoints: Int
    let description: String
    let canDraw: Bool
    let isLoading: Bool
    let isPenalty: Bool
    let onDraw: () -> Void

    init(title: String, icon: String, color: Color, costPoints: Int, description: String, canDraw: Bool, isLoading: Bool, isPenalty: Bool = false, onDraw: @escaping () -> Void) {
        self.title = title
        self.icon = icon
        self.color = color
        self.costPoints = costPoints
        self.description = description
        self.canDraw = canDraw
        self.isLoading = isLoading
        self.isPenalty = isPenalty
        self.onDraw = onDraw
    }

    var body: some View {
        VStack(spacing: 16) {
            // 池子图标和名称
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 32))
                    .foregroundColor(color)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }

            // 描述
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)

            // 消耗积分
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.orange)
                    .font(.caption)

                Text(isPenalty ? "获得\(costPoints)积分" : "\(costPoints)积分")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 抽奖按钮
            Button(action: onDraw) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: isPenalty ? "bolt.fill" : "dice.fill")
                    }

                    Text(isLoading ? "处理中..." : (isPenalty ? "执行" : "抽奖"))
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(canDraw ? color : .gray)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
            .disabled(!canDraw || isLoading)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.06), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - 功能说明行
struct FeatureDescriptionRow: View {
    let icon: String
    let color: Color
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// MARK: - 息屏覆盖层（用于模拟器）
struct DimmingOverlayView: View {
    var body: some View {
        ZStack {
            // 半透明黑色背景
            Color.black.opacity(0.8)
                .ignoresSafeArea(.all)

            // 息屏提示
            VStack(spacing: 16) {
                Image(systemName: "moon.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.white.opacity(0.8))

                Text("屏幕已息屏")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white.opacity(0.9))

                Text("点击任意位置恢复")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.7))
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .opacity(0.3)
            )
        }
        .animation(.easeInOut(duration: 0.5), value: true)
    }
}

#Preview {
    ContentView()
}
