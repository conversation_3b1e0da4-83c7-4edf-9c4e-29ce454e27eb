-- 手动初始化数据脚本
-- 在应用启动成功后执行此脚本来初始化系统配置数据

USE cps;

-- 检查表是否存在
SHOW TABLES;

-- 插入系统配置（如果不存在）
INSERT IGNORE INTO system_configs (config_key, config_value, description, update_time) VALUES
('PARENT_PIN', '0907', '家长PIN码', NOW()),
('CHILD_PIN', '0000', '儿童PIN码', NOW()),
('TASK_EXPIRED_PENALTY', '10', '任务过期惩罚分值', NOW()),
('REQUIRED_TASK_MULTIPLIER', '1.5', '必做任务乘数', NOW()),
('BONUS_REWARD_PROBABILITY', '0.1', '奖励概率加成', NOW()),
('DAILY_MAX_POINTS', '100', '每日最大积分', NOW()),
('OVERDRAFT_ALLOWED', 'true', '允许透支积分', NOW()),
('OVERDRAFT_LIMIT', '50', '积分透支限额', NOW()),
('NOTIFICATION_TASK_REMINDER', 'true', '任务提醒通知', NOW()),
('NOTIFICATION_APPROVAL_NEEDED', 'true', '审批提醒通知', NOW()),
('NOTIFICATION_POINTS_EARNED', 'true', '积分获得通知', NOW()),
('NOTIFICATION_OVERDRAFT_ALERT', 'true', '透支提醒通知', NOW());

-- 初始化积分余额（如果不存在）
INSERT IGNORE INTO point_balance (id, total_points, update_time) VALUES
(1, 0, NOW());

-- 验证数据插入
SELECT * FROM system_configs;
SELECT * FROM point_balance;
